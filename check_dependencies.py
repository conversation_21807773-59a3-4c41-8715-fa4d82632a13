#!/usr/bin/env python3
"""
检查Web RAG系统依赖
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查:")
    print(f"   当前版本: {sys.version}")
    if sys.version_info >= (3, 8):
        print("   ✅ Python版本符合要求 (>= 3.8)")
        return True
    else:
        print("   ❌ Python版本过低，需要 >= 3.8")
        return False

def check_packages():
    """检查必要的Python包"""
    print("\n📦 Python包检查:")
    
    required_packages = {
        'fastapi': 'FastAPI web框架',
        'uvicorn': 'ASGI服务器',
        'websockets': 'WebSocket支持',
        'pathlib': 'Python标准库',
        'asyncio': 'Python标准库',
        'json': 'Python标准库',
        'logging': 'Python标准库'
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少包: {', '.join(missing_packages)}")
        print("安装命令:")
        print("pip install fastapi uvicorn websockets")
        return False
    else:
        print("\n✅ 所有必要包都已安装")
        return True

def check_files():
    """检查必要文件"""
    print("\n📁 文件检查:")
    
    required_files = [
        'web_interface.html',
        'avatar_engine.js',
        'web_client.js',
        'web_rag_server.py',
        'simple_voice_rag.py'
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ 所有必要文件都存在")
        return True

def check_api_key():
    """检查API密钥"""
    print("\n🔑 API密钥检查:")
    
    api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
    
    if api_key:
        print(f"   ✅ API密钥已设置: {api_key[:8]}...{api_key[-4:]}")
        return True
    else:
        print("   ❌ 未设置API密钥")
        print("   设置方法: export DASHSCOPE_API_KEY='sk-e182b143987f48a385e70370515db60a'")
        return False

def check_port():
    """检查端口是否可用"""
    print("\n🔌 端口检查:")
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8000))
        sock.close()
        
        if result == 0:
            print("   ⚠️ 端口8000已被占用")
            print("   请检查是否有其他服务在运行")
            return False
        else:
            print("   ✅ 端口8000可用")
            return True
    except Exception as e:
        print(f"   ❌ 端口检查失败: {e}")
        return False

def test_simple_server():
    """测试简单服务器"""
    print("\n🧪 测试简单服务器:")
    
    try:
        import uvicorn
        from fastapi import FastAPI
        
        app = FastAPI()
        
        @app.get("/")
        def read_root():
            return {"message": "Hello World"}
        
        print("   ✅ FastAPI和Uvicorn工作正常")
        print("   可以尝试启动完整服务器")
        return True
        
    except Exception as e:
        print(f"   ❌ 服务器测试失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 Web RAG系统依赖检查")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_packages(),
        check_files(),
        check_api_key(),
        check_port(),
        test_simple_server()
    ]
    
    print("\n" + "=" * 50)
    print("📊 检查结果:")
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"   通过: {passed}/{total}")
    
    if passed == total:
        print("   ✅ 所有检查通过，可以启动系统")
        print("\n🚀 启动命令:")
        print("   export DASHSCOPE_API_KEY='sk-e182b143987f48a385e70370515db60a'")
        print("   python web_rag_server.py")
    else:
        print("   ❌ 存在问题，请解决后重试")
        print("\n🔧 建议解决步骤:")
        if not checks[1]:  # packages
            print("   1. pip install fastapi uvicorn websockets")
        if not checks[2]:  # files
            print("   2. 确保所有文件在同一目录下")
        if not checks[3]:  # api key
            print("   3. export DASHSCOPE_API_KEY='sk-e182b143987f48a385e70370515db60a'")

if __name__ == "__main__":
    main()
