<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGAnything 五层架构与七阶段流程可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
        }
        
        .section h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #764ba2;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .diagram-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        
        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .layer-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
        }
        
        .layer-1 { border-left-color: #ff6b6b; }
        .layer-2 { border-left-color: #4ecdc4; }
        .layer-3 { border-left-color: #45b7d1; }
        .layer-4 { border-left-color: #96ceb4; }
        .layer-5 { border-left-color: #feca57; }
        
        .layer-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.2em;
        }
        
        .layer-card .layer-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .stage-timeline {
            position: relative;
            padding: 20px 0;
        }
        
        .stage-item {
            display: flex;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .stage-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .stage-content h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .stage-content p {
            margin: 0;
            color: #666;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .tech-tag {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        
        .flow-arrow {
            text-align: center;
            font-size: 2em;
            color: #667eea;
            margin: 10px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .architecture-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 RAGAnything 系统架构</h1>
            <p>五层架构与七阶段流程完整可视化</p>
        </div>
        
        <div class="content">
            <!-- 五层架构 -->
            <div class="section">
                <h2>🏗️ 五层架构详解</h2>
                
                <div class="architecture-grid">
                    <div class="layer-card layer-1">
                        <div class="layer-icon">📱</div>
                        <h4>第一层：应用层</h4>
                        <p><strong>RAGAnything (多模态RAG系统)</strong></p>
                        <div class="tech-stack">
                            <span class="tech-tag">统一接口</span>
                            <span class="tech-tag">业务逻辑</span>
                            <span class="tech-tag">配置管理</span>
                            <span class="tech-tag">错误处理</span>
                        </div>
                        <p>提供用户友好的API接口，协调各组件工作流程，管理系统配置和异常处理。</p>
                    </div>
                    
                    <div class="layer-card layer-2">
                        <div class="layer-icon">🔧</div>
                        <h4>第二层：框架层</h4>
                        <p><strong>LightRAG (知识图谱RAG框架)</strong></p>
                        <div class="tech-stack">
                            <span class="tech-tag">知识图谱</span>
                            <span class="tech-tag">向量数据库</span>
                            <span class="tech-tag">查询引擎</span>
                            <span class="tech-tag">存储管理</span>
                        </div>
                        <p>负责知识图谱构建、向量数据库管理、多模式检索和持久化存储。</p>
                    </div>
                    
                    <div class="layer-card layer-3">
                        <div class="layer-icon">📄</div>
                        <h4>第三层：解析层</h4>
                        <p><strong>MinerU (多模态文档解析)</strong></p>
                        <div class="tech-stack">
                            <span class="tech-tag">PDF解析</span>
                            <span class="tech-tag">图像识别</span>
                            <span class="tech-tag">表格提取</span>
                            <span class="tech-tag">公式识别</span>
                        </div>
                        <p>自动识别文档格式，精确提取多模态内容，输出统一的结构化表示。</p>
                    </div>
                    
                    <div class="layer-card layer-4">
                        <div class="layer-icon">🤖</div>
                        <h4>第四层：模型层</h4>
                        <p><strong>阿里云百炼API (LLM + Embedding + Vision)</strong></p>
                        <div class="tech-stack">
                            <span class="tech-tag">qwen-turbo</span>
                            <span class="tech-tag">qwen-vl-plus</span>
                            <span class="tech-tag">text-embedding-v1</span>
                            <span class="tech-tag">API网关</span>
                        </div>
                        <p>提供自然语言理解、多模态理解、向量表示和智能生成能力。</p>
                    </div>
                    
                    <div class="layer-card layer-5">
                        <div class="layer-icon">⚡</div>
                        <h4>第五层：硬件层</h4>
                        <p><strong>CPU/GPU (MPS/CUDA加速)</strong></p>
                        <div class="tech-stack">
                            <span class="tech-tag">CUDA</span>
                            <span class="tech-tag">MPS</span>
                            <span class="tech-tag">并行计算</span>
                            <span class="tech-tag">内存优化</span>
                        </div>
                        <p>管理计算资源，提供并行计算加速，优化内存使用，支持跨平台部署。</p>
                    </div>
                </div>
                
                <div class="diagram-container">
                    <h3>架构层次关系图</h3>
                    <div class="mermaid">
                        graph TD
                            A[📱 应用层<br/>RAGAnything] --> B[🔧 框架层<br/>LightRAG]
                            B --> C[📄 解析层<br/>MinerU]
                            B --> D[🤖 模型层<br/>阿里云百炼API]
                            B --> E[⚡ 硬件层<br/>CPU/GPU]
                            
                            A1[统一接口管理] --> A
                            A2[业务逻辑协调] --> A
                            A3[配置管理] --> A
                            
                            B1[知识图谱构建] --> B
                            B2[向量数据库] --> B
                            B3[查询引擎] --> B
                            
                            C1[文档解析] --> C
                            C2[多模态提取] --> C
                            
                            D1[LLM服务] --> D
                            D2[Vision服务] --> D
                            D3[Embedding服务] --> D
                            
                            E1[GPU加速] --> E
                            E2[并行计算] --> E
                            
                            style A fill:#ff6b6b,color:#fff
                            style B fill:#4ecdc4,color:#fff
                            style C fill:#45b7d1,color:#fff
                            style D fill:#96ceb4,color:#fff
                            style E fill:#feca57,color:#fff
                    </div>
                </div>
            </div>
            
            <!-- 七阶段流程 -->
            <div class="section">
                <h2>🔄 七阶段处理流程</h2>
                
                <div class="stage-timeline">
                    <div class="stage-item">
                        <div class="stage-number">1</div>
                        <div class="stage-content">
                            <h4>📄 文档解析 - MinerU多模态解析</h4>
                            <p>将各种格式文档转换为统一结构化内容，支持PDF、图像、Office文档的精确解析。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">布局检测</span>
                                <span class="tech-tag">OCR识别</span>
                                <span class="tech-tag">表格识别</span>
                                <span class="tech-tag">公式提取</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">⬇️</div>
                    
                    <div class="stage-item">
                        <div class="stage-number">2</div>
                        <div class="stage-content">
                            <h4>🔄 内容分离 - 文本与多模态内容分类</h4>
                            <p>智能分离纯文本和多模态内容，保持文档结构，为专门处理做准备。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">内容分类</span>
                                <span class="tech-tag">结构保持</span>
                                <span class="tech-tag">上下文关联</span>
                                <span class="tech-tag">质量评估</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">⬇️</div>
                    
                    <div class="stage-item">
                        <div class="stage-number">3</div>
                        <div class="stage-content">
                            <h4>🎨 多模态处理 - 图像/表格/公式专门处理</h4>
                            <p>对不同类型多模态内容进行深度处理，提取语义信息并转换为结构化数据。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">图像分析</span>
                                <span class="tech-tag">表格理解</span>
                                <span class="tech-tag">公式解析</span>
                                <span class="tech-tag">实体提取</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">⬇️</div>
                    
                    <div class="stage-item">
                        <div class="stage-number">4</div>
                        <div class="stage-content">
                            <h4>🧠 知识图谱构建 - LightRAG实体关系提取</h4>
                            <p>基于文本和多模态内容构建知识图谱，包括实体识别、关系抽取和图谱存储。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">实体提取</span>
                                <span class="tech-tag">关系抽取</span>
                                <span class="tech-tag">图谱构建</span>
                                <span class="tech-tag">索引优化</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">⬇️</div>
                    
                    <div class="stage-item">
                        <div class="stage-number">5</div>
                        <div class="stage-content">
                            <h4>🗄️ 向量化存储 - 多层次向量数据库</h4>
                            <p>将文本块、实体、关系转换为向量表示，构建多层次向量数据库支持语义检索。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">文本向量化</span>
                                <span class="tech-tag">实体向量化</span>
                                <span class="tech-tag">关系向量化</span>
                                <span class="tech-tag">索引构建</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">⬇️</div>
                    
                    <div class="stage-item">
                        <div class="stage-number">6</div>
                        <div class="stage-content">
                            <h4>🔍 查询检索 - 混合检索策略</h4>
                            <p>实现多模式智能检索，结合向量相似度和知识图谱推理，找到最相关信息。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">查询理解</span>
                                <span class="tech-tag">向量检索</span>
                                <span class="tech-tag">图谱推理</span>
                                <span class="tech-tag">结果融合</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">⬇️</div>
                    
                    <div class="stage-item">
                        <div class="stage-number">7</div>
                        <div class="stage-content">
                            <h4>🤖 答案生成 - 上下文感知生成</h4>
                            <p>基于检索上下文生成准确答案，支持多模态内容综合理解和结构化表达。</p>
                            <div class="tech-stack">
                                <span class="tech-tag">策略选择</span>
                                <span class="tech-tag">内容生成</span>
                                <span class="tech-tag">质量评估</span>
                                <span class="tech-tag">后处理优化</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="diagram-container">
                    <h3>完整数据流程图</h3>
                    <div class="mermaid">
                        graph TD
                            A[📄 文档输入] --> B{文件类型检测}
                            B -->|PDF| C[MinerU PDF解析]
                            B -->|图像| D[MinerU 图像解析]
                            B -->|Office| E[MinerU Office解析]

                            C --> F[结构化内容列表]
                            D --> F
                            E --> F

                            F --> G[内容分离]
                            G --> H[📝 纯文本内容]
                            G --> I[🎨 多模态内容]

                            H --> J[LightRAG文本处理]
                            J --> K[文本分块]
                            K --> L[实体提取]
                            L --> M[关系抽取]

                            I --> N{多模态类型}
                            N -->|图像| O[图像处理器]
                            N -->|表格| P[表格处理器]
                            N -->|公式| Q[公式处理器]

                            O --> R[Vision API分析]
                            P --> S[结构化分析]
                            Q --> T[数学语义解析]

                            R --> U[实体提取]
                            S --> U
                            T --> U

                            M --> V[知识图谱构建]
                            U --> V

                            V --> W[📊 实体节点]
                            V --> X[🔗 关系边]

                            W --> Y[向量化]
                            X --> Y
                            K --> Y

                            Y --> Z[向量数据库]
                            Z --> AA[🔍 查询处理]

                            AA --> BB{查询模式}
                            BB -->|local| CC[局部检索]
                            BB -->|global| DD[全局检索]
                            BB -->|hybrid| EE[混合检索]

                            CC --> FF[上下文构建]
                            DD --> FF
                            EE --> FF

                            FF --> GG[🤖 LLM生成]
                            GG --> HH[📋 最终答案]

                            style A fill:#e1f5fe
                            style F fill:#f3e5f5
                            style V fill:#e8f5e8
                            style Z fill:#fff3e0
                            style HH fill:#ffebee
                    </div>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="section">
                <h2>📊 系统性能指标</h2>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">95%</div>
                        <div class="metric-label">解析准确率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">90%</div>
                        <div class="metric-label">实体提取准确率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">85%</div>
                        <div class="metric-label">关系识别准确率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">88%</div>
                        <div class="metric-label">多模态理解准确率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">35%</div>
                        <div class="metric-label">GPU性能提升</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2-5秒</div>
                        <div class="metric-label">平均处理时间/页</div>
                    </div>
                </div>

                <div class="diagram-container">
                    <h3>处理时间分布</h3>
                    <div class="mermaid">
                        pie title 七阶段处理时间占比
                            "文档解析" : 40
                            "多模态处理" : 30
                            "知识图谱构建" : 15
                            "向量化存储" : 8
                            "内容分离" : 3
                            "查询检索" : 2
                            "答案生成" : 2
                    </div>
                </div>
            </div>

            <!-- 技术优势 -->
            <div class="section">
                <h2>🎯 核心技术优势</h2>

                <div class="architecture-grid">
                    <div class="layer-card layer-1">
                        <div class="layer-icon">🔄</div>
                        <h4>多模态统一处理</h4>
                        <p>支持文本、图像、表格、公式的一体化处理，实现跨模态的语义理解和关联。</p>
                        <div class="tech-stack">
                            <span class="tech-tag">文本理解</span>
                            <span class="tech-tag">图像分析</span>
                            <span class="tech-tag">表格解析</span>
                            <span class="tech-tag">公式识别</span>
                        </div>
                    </div>

                    <div class="layer-card layer-2">
                        <div class="layer-icon">🧠</div>
                        <h4>知识图谱增强</h4>
                        <p>构建丰富的实体关系网络，支持复杂推理和多跳查询，提升答案质量。</p>
                        <div class="tech-stack">
                            <span class="tech-tag">实体识别</span>
                            <span class="tech-tag">关系抽取</span>
                            <span class="tech-tag">图谱推理</span>
                            <span class="tech-tag">语义关联</span>
                        </div>
                    </div>

                    <div class="layer-card layer-3">
                        <div class="layer-icon">🔍</div>
                        <h4>混合检索策略</h4>
                        <p>结合向量相似度和图谱推理，实现精确匹配和语义理解的完美平衡。</p>
                        <div class="tech-stack">
                            <span class="tech-tag">向量检索</span>
                            <span class="tech-tag">图谱检索</span>
                            <span class="tech-tag">混合融合</span>
                            <span class="tech-tag">智能排序</span>
                        </div>
                    </div>

                    <div class="layer-card layer-4">
                        <div class="layer-icon">⚡</div>
                        <h4>高性能架构</h4>
                        <p>异步处理、GPU加速、智能缓存，确保大规模文档处理的高效性。</p>
                        <div class="tech-stack">
                            <span class="tech-tag">异步并发</span>
                            <span class="tech-tag">GPU加速</span>
                            <span class="tech-tag">智能缓存</span>
                            <span class="tech-tag">批处理优化</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 应用场景 -->
            <div class="section">
                <h2>🌟 应用场景</h2>

                <div class="architecture-grid">
                    <div class="layer-card layer-1">
                        <div class="layer-icon">📚</div>
                        <h4>学术研究</h4>
                        <p>处理学术论文、研究报告，提取关键信息，构建知识图谱，支持文献综述和研究分析。</p>
                    </div>

                    <div class="layer-card layer-2">
                        <div class="layer-icon">🏢</div>
                        <h4>企业知识管理</h4>
                        <p>整合企业文档、报告、手册，构建企业知识库，提升信息检索和决策支持能力。</p>
                    </div>

                    <div class="layer-card layer-3">
                        <div class="layer-icon">⚖️</div>
                        <h4>法律文档分析</h4>
                        <p>分析法律条文、案例、合同，提取关键条款，支持法律研究和合规检查。</p>
                    </div>

                    <div class="layer-card layer-4">
                        <div class="layer-icon">🏥</div>
                        <h4>医疗文档处理</h4>
                        <p>处理医疗报告、病历、研究文献，提取医疗实体和关系，支持临床决策。</p>
                    </div>

                    <div class="layer-card layer-5">
                        <div class="layer-icon">📊</div>
                        <h4>金融报告分析</h4>
                        <p>分析财务报告、市场研究，提取关键数据和趋势，支持投资决策和风险评估。</p>
                    </div>

                    <div class="layer-card layer-1">
                        <div class="layer-icon">🎓</div>
                        <h4>教育培训</h4>
                        <p>处理教材、课件、习题，构建知识体系，支持个性化学习和智能答疑。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            pie: {
                textPosition: 0.5
            }
        });
    </script>
</body>
</html>
