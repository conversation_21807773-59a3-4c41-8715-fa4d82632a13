#!/bin/bash
# 阿里云百炼API环境变量设置脚本

echo "🚀 设置阿里云百炼API环境变量..."

# 设置API密钥
export DASHSCOPE_API_KEY="sk-c7b965ee5fc64ab482174967dabd4805"
export OPENAI_API_KEY="sk-c7b965ee5fc64ab482174967dabd4805"
export OPENAI_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"

# 设置RAGAnything相关环境变量
export LLM_BINDING="openai"
export LLM_MODEL="qwen-turbo"
export LLM_BINDING_HOST="https://dashscope.aliyuncs.com/compatible-mode/v1"
export LLM_BINDING_API_KEY="sk-c7b965ee5fc64ab482174967dabd4805"

export EMBEDDING_BINDING="openai"
export EMBEDDING_MODEL="text-embedding-v1"
export EMBEDDING_DIM="1536"
export EMBEDDING_BINDING_API_KEY="sk-c7b965ee5fc64ab482174967dabd4805"
export EMBEDDING_BINDING_HOST="https://dashscope.aliyuncs.com/compatible-mode/v1"

echo "✅ 环境变量设置完成！"
echo "📋 当前配置："
echo "  🔑 DASHSCOPE_API_KEY: ${DASHSCOPE_API_KEY:0:8}...${DASHSCOPE_API_KEY: -4}"
echo "  🌐 OPENAI_BASE_URL: $OPENAI_BASE_URL"
echo "  🤖 LLM_MODEL: $LLM_MODEL"
echo "  🔢 EMBEDDING_MODEL: $EMBEDDING_MODEL"

echo ""
echo "💡 使用方法："
echo "  1. 运行此脚本: source setup_env.sh"
echo "  2. 测试连接: python test_dashscope_connection.py"
echo "  3. 运行RAGAnything: python examples/raganything_example.py your_document.pdf"
echo ""
echo "📝 要永久设置环境变量，请将以下内容添加到您的 ~/.bashrc 或 ~/.zshrc 文件中："
echo ""
echo "# 阿里云百炼API配置"
echo "export DASHSCOPE_API_KEY=\"sk-c7b965ee5fc64ab482174967dabd4805\""
echo "export OPENAI_API_KEY=\"sk-c7b965ee5fc64ab482174967dabd4805\""
echo "export OPENAI_BASE_URL=\"https://dashscope.aliyuncs.com/compatible-mode/v1\""
