<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-Anything 项目技术总结</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #007AFF;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #007AFF;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #5856D6;
            font-size: 1.5em;
            margin: 20px 0 15px 0;
        }

        .diagram-container {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .model-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #007AFF;
            transition: transform 0.3s ease;
        }

        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .model-card h4 {
            color: #007AFF;
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .model-card .model-type {
            background: #007AFF;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            display: inline-block;
            margin-bottom: 10px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .feature-list li:before {
            content: "✅ ";
            color: #34C759;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(52, 199, 89, 0.3);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 1em;
            opacity: 0.9;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }

        .tech-tag {
            background: linear-gradient(45deg, #FF9500, #FF6B35);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 122, 255, 0.9);
            padding: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .nav-link {
            display: block;
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 10px;
            margin: 5px 0;
            transition: background 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .highlight-box {
            background: linear-gradient(45deg, #FFE082, #FFCC02);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #FF9500;
        }

        .highlight-box h4 {
            color: #E65100;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .model-grid {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#overview" class="nav-link">🏗️ 系统概览</a>
        <a href="#models" class="nav-link">🤖 AI模型</a>
        <a href="#architecture" class="nav-link">📊 架构图</a>
        <a href="#features" class="nav-link">✨ 功能特色</a>
        <a href="#stats" class="nav-link">📈 技术指标</a>
        <a href="#links" class="nav-link">🔗 访问链接</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 RAG-Anything</h1>
            <p>多模态RAG + AI数字人 + 知识图谱 技术总结</p>
            <p>基于10+个AI模型的智能问答与数字人交互系统</p>
        </div>

        <section id="overview" class="section">
            <h2>🏗️ 系统架构概览</h2>
            <div class="highlight-box">
                <h4>🎯 核心技术亮点</h4>
                <p>RAG-Anything是一个集成了文档解析、知识图谱、多模态AI、语音交互和数字人动画的完整智能系统，实现了从PDF文档到智能对话再到视觉呈现的全链路AI能力。</p>
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
                    graph TB
                        subgraph "输入层 Input Layer"
                            A[📄 PDF文档]
                            B[🎤 语音输入]
                            C[⌨️ 文本输入]
                            D[🖼️ 图片输入]
                        end
                        
                        subgraph "解析层 Parsing Layer"
                            E[MinerU 2.0<br/>PDF解析引擎]
                            F[Whisper Base<br/>语音识别]
                        end
                        
                        subgraph "AI推理层 AI Layer"
                            G[Qwen-turbo<br/>大语言模型]
                            H[Qwen-VL-Plus<br/>视觉理解]
                            I[text-embedding-v1<br/>向量化]
                        end
                        
                        subgraph "知识层 Knowledge Layer"
                            J[LightRAG<br/>知识图谱]
                            K[向量数据库<br/>Vector DB]
                        end
                        
                        subgraph "输出层 Output Layer"
                            L[Edge-TTS<br/>语音合成]
                            M[MediaPipe<br/>面部动画]
                            N[🎭 数字人展示]
                        end
                        
                        A --> E
                        B --> F
                        C --> G
                        D --> H
                        
                        E --> G
                        F --> G
                        
                        G --> J
                        H --> J
                        I --> K
                        
                        J --> L
                        K --> G
                        
                        L --> M
                        M --> N
                        
                        style A fill:#e1f5fe
                        style G fill:#fff3e0
                        style J fill:#e8f5e8
                        style N fill:#fce4ec
                </div>
            </div>
        </section>

        <section id="models" class="section">
            <h2>🤖 AI模型技术栈</h2>
            
            <div class="model-grid">
                <div class="model-card">
                    <div class="model-type">语言模型</div>
                    <h4>Qwen-turbo</h4>
                    <ul class="feature-list">
                        <li>阿里云百炼大语言模型</li>
                        <li>文本理解与生成</li>
                        <li>实体关系抽取</li>
                        <li>智能问答生成</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">视觉模型</div>
                    <h4>Qwen-VL-Plus</h4>
                    <ul class="feature-list">
                        <li>多模态视觉理解</li>
                        <li>图像内容分析</li>
                        <li>OCR文字识别</li>
                        <li>图表理解</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">嵌入模型</div>
                    <h4>text-embedding-v1</h4>
                    <ul class="feature-list">
                        <li>1536维文本向量</li>
                        <li>语义相似度计算</li>
                        <li>向量检索匹配</li>
                        <li>知识关联分析</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">语音识别</div>
                    <h4>Whisper Base</h4>
                    <ul class="feature-list">
                        <li>OpenAI语音识别</li>
                        <li>多语言支持</li>
                        <li>高精度转录</li>
                        <li>实时语音处理</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">语音合成</div>
                    <h4>Edge-TTS</h4>
                    <ul class="feature-list">
                        <li>Microsoft语音合成</li>
                        <li>自然女声发音</li>
                        <li>中英文支持</li>
                        <li>高质量音频输出</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">计算机视觉</div>
                    <h4>MediaPipe FaceMesh</h4>
                    <ul class="feature-list">
                        <li>Google面部检测</li>
                        <li>468个关键点</li>
                        <li>实时面部追踪</li>
                        <li>精准动画驱动</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">文档解析</div>
                    <h4>MinerU 2.0</h4>
                    <ul class="feature-list">
                        <li>PDF结构化解析</li>
                        <li>表格图像提取</li>
                        <li>公式识别</li>
                        <li>多模态内容处理</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <div class="model-type">知识图谱</div>
                    <h4>LightRAG</h4>
                    <ul class="feature-list">
                        <li>图算法引擎</li>
                        <li>实体关系抽取</li>
                        <li>知识图谱构建</li>
                        <li>智能检索优化</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="architecture" class="section">
            <h2>📊 技术架构流程</h2>
            
            <h3>🔄 数据处理流程</h3>
            <div class="diagram-container">
                <div class="mermaid">
                    flowchart LR
                        A[📄 PDF输入] --> B[MinerU解析]
                        B --> C[多模态提取]
                        C --> D[实体识别]
                        D --> E[关系抽取]
                        E --> F[知识图谱]
                        F --> G[向量化存储]
                        
                        H[🎤 用户查询] --> I[语音识别]
                        I --> J[文本处理]
                        J --> K[向量检索]
                        G --> K
                        K --> L[LLM推理]
                        F --> L
                        L --> M[文本生成]
                        M --> N[语音合成]
                        N --> O[🎭 数字人动画]
                        
                        style A fill:#e1f5fe
                        style F fill:#e8f5e8
                        style L fill:#fff3e0
                        style O fill:#fce4ec
                </div>
            </div>
            
            <h3>🏗️ 分层架构</h3>
            <div class="tech-stack">
                <div class="tech-tag">前端: HTML5 + JavaScript + Canvas</div>
                <div class="tech-tag">后端: FastAPI + WebSocket</div>
                <div class="tech-tag">AI: Qwen + Whisper + Edge-TTS</div>
                <div class="tech-tag">存储: 向量数据库 + 知识图谱</div>
                <div class="tech-tag">硬件: Apple M3 + WebGL加速</div>
            </div>
        </section>

        <section id="features" class="section">
            <h2>✨ 核心功能特色</h2>
            
            <div class="model-grid">
                <div class="model-card">
                    <h4>🎭 AI数字人动画</h4>
                    <ul class="feature-list">
                        <li>真实美女人脸检测</li>
                        <li>468个面部关键点</li>
                        <li>实时嘴形同步</li>
                        <li>自然眨眼呼吸</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <h4>🎤 智能语音交互</h4>
                    <ul class="feature-list">
                        <li>Whisper语音识别</li>
                        <li>Edge-TTS语音合成</li>
                        <li>中英文自适应</li>
                        <li>自然对话体验</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <h4>📚 多模态RAG</h4>
                    <ul class="feature-list">
                        <li>PDF文档解析</li>
                        <li>图像表格理解</li>
                        <li>知识图谱构建</li>
                        <li>智能检索问答</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <h4>🚀 性能优化</h4>
                    <ul class="feature-list">
                        <li>Apple M3芯片加速</li>
                        <li>WebGL硬件渲染</li>
                        <li>异步并行处理</li>
                        <li>智能缓存机制</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="stats" class="section">
            <h2>📈 技术指标统计</h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">10+</span>
                    <span class="stat-label">AI模型集成</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">468</span>
                    <span class="stat-label">面部关键点</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">1536</span>
                    <span class="stat-label">向量维度</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">4</span>
                    <span class="stat-label">数字人版本</span>
                </div>
            </div>
            
            <div class="highlight-box">
                <h4>🎯 性能亮点</h4>
                <ul class="feature-list">
                    <li>毫秒级语音识别响应</li>
                    <li>实时面部动画渲染</li>
                    <li>高精度文档解析</li>
                    <li>智能知识图谱检索</li>
                    <li>多语言无缝切换</li>
                    <li>硬件加速优化</li>
                </ul>
            </div>
        </section>

        <section id="links" class="section">
            <h2>🔗 系统访问链接</h2>
            
            <div class="model-grid">
                <div class="model-card">
                    <h4>🤖 AI人脸动画 (推荐)</h4>
                    <p><strong>链接:</strong> <a href="http://localhost:8000/face" target="_blank">http://localhost:8000/face</a></p>
                    <ul class="feature-list">
                        <li>MediaPipe AI面部检测</li>
                        <li>468个关键点可视化</li>
                        <li>实时质量评估</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <h4>💄 真实美女数字人</h4>
                    <p><strong>链接:</strong> <a href="http://localhost:8000/beauty" target="_blank">http://localhost:8000/beauty</a></p>
                    <ul class="feature-list">
                        <li>基于真实美女图片</li>
                        <li>自然面部动画</li>
                        <li>完整语音交互</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <h4>🎭 3D数字人系统</h4>
                    <p><strong>链接:</strong> <a href="http://localhost:8000/rpm" target="_blank">http://localhost:8000/rpm</a></p>
                    <ul class="feature-list">
                        <li>Ready Player Me技术</li>
                        <li>WebGL硬件加速</li>
                        <li>多角色切换</li>
                    </ul>
                </div>
                
                <div class="model-card">
                    <h4>📱 经典版本</h4>
                    <p><strong>链接:</strong> <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
                    <ul class="feature-list">
                        <li>Canvas 2D动画</li>
                        <li>稳定可靠</li>
                        <li>完整功能</li>
                    </ul>
                </div>
            </div>
            
            <div class="highlight-box">
                <h4>🚀 快速开始</h4>
                <p><strong>推荐体验路径:</strong></p>
                <ol>
                    <li>访问 <a href="http://localhost:8000/face" target="_blank">AI人脸动画系统</a></li>
                    <li>点击"🚀 初始化AI"启动面部检测</li>
                    <li>点击🎤按钮进行语音输入测试</li>
                    <li>观察多色关键点和动画同步效果</li>
                </ol>
            </div>
        </section>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 添加页面加载动画
        window.addEventListener('load', function() {
            document.querySelectorAll('.section').forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
