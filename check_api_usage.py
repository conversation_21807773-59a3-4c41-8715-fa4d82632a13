#!/usr/bin/env python3
"""
检查API调用情况和费用扣除
"""

import os
import sys
import asyncio
import requests
import json
from pathlib import Path

sys.path.append(str(Path(__file__).parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed

def check_api_configuration():
    """检查API配置"""
    print("🔍 检查API配置")
    print("=" * 30)
    
    # 检查环境变量
    api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    print(f"API密钥: {api_key[:8]}...{api_key[-4:] if api_key else 'None'}")
    print(f"API端点: {base_url}")
    
    # 检查.env文件
    if os.path.exists(".env"):
        print("✅ .env文件存在")
        with open(".env", "r") as f:
            content = f.read()
            if "sk-c7b965ee5fc64ab482174967dabd4805" in content:
                print("✅ .env文件包含您的API密钥")
            else:
                print("❌ .env文件不包含您的API密钥")
    
    return api_key, base_url

async def test_llm_api_call():
    """测试LLM API调用"""
    print("\n🧪 测试LLM API调用")
    print("=" * 25)
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    try:
        print("📤 发送测试请求...")
        response = await openai_complete_if_cache(
            "qwen-turbo",
            "这是一个API调用测试，请回复'API调用成功'",
            api_key=api_key,
            base_url=base_url,
            max_tokens=20
        )
        
        print(f"✅ LLM API调用成功")
        print(f"📝 响应: {response}")
        return True
        
    except Exception as e:
        print(f"❌ LLM API调用失败: {e}")
        return False

async def test_embedding_api_call():
    """测试Embedding API调用"""
    print("\n🧪 测试Embedding API调用")
    print("=" * 30)
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    try:
        print("📤 发送embedding请求...")
        embeddings = await openai_embed(
            ["这是一个embedding测试"],
            model="text-embedding-v1",
            api_key=api_key,
            base_url=base_url
        )
        
        print(f"✅ Embedding API调用成功")
        print(f"📝 向量维度: {len(embeddings[0])}")
        return True
        
    except Exception as e:
        print(f"❌ Embedding API调用失败: {e}")
        return False

def check_cache_usage():
    """检查缓存使用情况"""
    print("\n🗄️ 检查缓存使用情况")
    print("=" * 25)
    
    cache_file = "./rag_storage/kv_store_llm_response_cache.json"
    
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            cache_count = len(cache_data)
            print(f"📊 缓存条目数量: {cache_count}")
            
            if cache_count > 0:
                print("⚠️ 发现缓存数据！这可能是费用未扣除的原因")
                print("💡 缓存的API调用不会重复计费")
                
                # 显示一些缓存的键
                keys = list(cache_data.keys())[:3]
                for i, key in enumerate(keys, 1):
                    print(f"  缓存项 {i}: {key[:50]}...")
                
                return True
            else:
                print("✅ 缓存为空，所有调用都是新的")
                return False
                
        except Exception as e:
            print(f"❌ 读取缓存文件失败: {e}")
            return False
    else:
        print("❌ 缓存文件不存在")
        return False

def check_api_call_logs():
    """检查可能的API调用日志"""
    print("\n📋 检查API调用模式")
    print("=" * 25)
    
    # 检查配置中的缓存设置
    cache_enabled = os.getenv("ENABLE_LLM_CACHE", "true").lower() == "true"
    cache_extract = os.getenv("ENABLE_LLM_CACHE_FOR_EXTRACT", "true").lower() == "true"
    
    print(f"LLM缓存启用: {cache_enabled}")
    print(f"提取缓存启用: {cache_extract}")
    
    if cache_enabled:
        print("⚠️ LLM缓存已启用 - 重复查询不会产生费用")
    
    # 检查是否使用了本地模型
    print("\n🔍 检查可能的本地模型使用:")
    
    # 检查是否有ollama或其他本地模型配置
    if os.getenv("OLLAMA_EMULATING_MODEL_TAG"):
        print("⚠️ 检测到OLLAMA配置 - 可能使用了本地模型")
    
    return cache_enabled

def analyze_cost_factors():
    """分析费用因素"""
    print("\n💰 费用分析")
    print("=" * 15)
    
    factors = []
    
    # 检查缓存
    if os.path.exists("./rag_storage/kv_store_llm_response_cache.json"):
        factors.append("✅ 使用了LLM响应缓存")
    
    # 检查文档大小
    if os.path.exists("./rag_storage/kv_store_text_chunks.json"):
        try:
            with open("./rag_storage/kv_store_text_chunks.json", 'r') as f:
                chunks_data = json.load(f)
            chunk_count = len(chunks_data.get('data', []))
            factors.append(f"📄 处理了 {chunk_count} 个文本块")
        except:
            pass
    
    # 检查向量数据
    if os.path.exists("./rag_storage/vdb_chunks.json"):
        try:
            with open("./rag_storage/vdb_chunks.json", 'r') as f:
                vdb_data = json.load(f)
            vector_count = len(vdb_data.get('data', []))
            factors.append(f"🔢 生成了 {vector_count} 个向量")
        except:
            pass
    
    print("费用影响因素:")
    for factor in factors:
        print(f"  {factor}")
    
    return factors

async def main():
    """主函数"""
    print("🔍 API使用情况检查工具")
    print("=" * 40)
    
    # 检查配置
    api_key, base_url = check_api_configuration()
    
    if not api_key:
        print("❌ 未找到API密钥配置")
        return
    
    # 检查缓存
    has_cache = check_cache_usage()
    
    # 检查配置
    cache_enabled = check_api_call_logs()
    
    # 分析费用因素
    cost_factors = analyze_cost_factors()
    
    # 测试API调用
    print("\n" + "=" * 40)
    print("🧪 执行API调用测试")
    
    llm_success = await test_llm_api_call()
    embedding_success = await test_embedding_api_call()
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 总结报告")
    
    print(f"\n🔧 配置状态:")
    print(f"  API密钥: {'✅ 已配置' if api_key else '❌ 未配置'}")
    print(f"  API端点: {'✅ 阿里云百炼' if 'dashscope' in (base_url or '') else '❌ 其他'}")
    
    print(f"\n🧪 API测试:")
    print(f"  LLM调用: {'✅ 成功' if llm_success else '❌ 失败'}")
    print(f"  Embedding调用: {'✅ 成功' if embedding_success else '❌ 失败'}")
    
    print(f"\n💰 费用分析:")
    if has_cache and cache_enabled:
        print("  ⚠️ 主要原因：使用了缓存机制")
        print("  💡 重复的API调用从缓存返回，不产生费用")
        print("  🔄 如要测试计费，请删除缓存文件后重新运行")
    elif not (llm_success or embedding_success):
        print("  ❌ API调用失败，可能未实际使用您的API")
    else:
        print("  ✅ API调用正常，应该会产生费用")
    
    print(f"\n💡 建议:")
    if has_cache:
        print("  1. 删除 ./rag_storage/ 目录清空缓存")
        print("  2. 重新运行程序测试计费")
        print("  3. 检查阿里云控制台的API调用记录")
    else:
        print("  1. 检查阿里云控制台的API调用记录")
        print("  2. 确认API密钥是否正确绑定账户")
        print("  3. 查看API调用日志")

if __name__ == "__main__":
    asyncio.run(main())
