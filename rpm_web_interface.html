<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ready Player Me RAG数字人 - M3优化版</title>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            min-height: 700px;
        }

        .avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .avatar-canvas {
            width: 100%;
            height: 500px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
            border: 3px solid #007AFF;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .avatar-canvas:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .avatar-controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 15px;
            background: #007AFF;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: #34C759;
        }

        .avatar-status {
            margin-top: 15px;
            padding: 12px 20px;
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(52, 199, 89, 0.3);
        }

        .chat-section {
            display: flex;
            flex-direction: column;
            height: 640px;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .chat-header h1 {
            color: #1d1d1f;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .chat-header p {
            color: #86868b;
            font-size: 18px;
            font-weight: 500;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 20px;
            margin-bottom: 20px;
            border: 2px solid #e5e5ea;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 20px;
            max-width: 85%;
            word-wrap: break-word;
            animation: messageSlideIn 0.4s ease-out;
            position: relative;
        }

        .message.user {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 8px;
        }

        .message.assistant {
            background: linear-gradient(45deg, #E5E5EA, #F2F2F7);
            color: #1d1d1f;
            border-bottom-left-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .message.system {
            background: linear-gradient(45deg, #FF9500, #FF6B35);
            color: white;
            margin: 0 auto;
            text-align: center;
            border-radius: 15px;
        }

        .message-content {
            line-height: 1.6;
            font-size: 16px;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 8px;
            text-align: right;
        }

        .input-section {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: 2px solid #e5e5ea;
        }

        .text-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .text-input:focus {
            background: white;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        .btn-voice {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
        }

        .btn-voice:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 199, 89, 0.3);
        }

        .btn-voice.recording {
            background: linear-gradient(45deg, #FF3B30, #FF6B35);
            animation: pulse 1.5s infinite;
        }

        .connection-status {
            position: fixed;
            top: 25px;
            right: 25px;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .connection-status.connected {
            background: rgba(52, 199, 89, 0.9);
            color: white;
        }

        .connection-status.disconnected {
            background: rgba(255, 59, 48, 0.9);
            color: white;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 15px;
            color: #007AFF;
            font-weight: 600;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 15px;
            margin: 10px 0;
        }

        .loading.show {
            display: block;
            animation: loadingPulse 1.5s infinite;
        }

        .performance-info {
            position: fixed;
            bottom: 25px;
            left: 25px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-family: monospace;
            backdrop-filter: blur(10px);
        }



        @keyframes messageSlideIn {
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes loadingPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .avatar-canvas {
                height: 400px;
            }
            
            .chat-section {
                height: 500px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 15px;
            }
            
            .chat-header h1 {
                font-size: 24px;
            }
            
            .avatar-canvas {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔄 连接中...</div>
    
    <div class="container">
        <!-- 3D数字人区域 -->
        <div class="avatar-section">
            <canvas id="avatarCanvas" class="avatar-canvas"></canvas>
            
            <div class="avatar-controls">
                <button class="control-btn" id="resetCameraBtn">
                    📷 重置视角
                </button>
                <button class="control-btn" id="toggleAnimationBtn">
                    🎭 切换动画
                </button>
                <button class="control-btn" id="changeAvatarBtn">
                    🌟 切换美女
                </button>
            </div>
            
            <div class="avatar-status" id="avatarStatus">🚀 初始化中...</div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>🤖 Ready Player Me RAG助手</h1>
                <p>基于3D数字人的智能问答系统</p>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>真实美女数字人助手:</strong><br>
                        您好！我是基于真实美女人脸的3D数字人助手。我拥有逼真的美女外观和自然的面部动画，可以通过高质量的Edge-TTS语音与您交流。<br><br>
                        🌟 <strong>特色功能：</strong><br>
                        • 真实美女人脸3D建模<br>
                        • 实时面部动画和嘴形同步<br>
                        • 高质量Edge-TTS语音合成<br>
                        • 智能RAG知识问答<br>
                        • 多个美女角色可切换<br>
                        • M3芯片WebGL加速渲染<br><br>
                        🎭 <strong>当前美女：</strong>艾莉亚 - 欧美混血美女，深邃眼眸<br><br>
                        请随时向我提问，我会用生动的3D美女动画为您解答！点击"🌟 切换美女"可以更换不同的美女角色。
                    </div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>

            <div class="loading" id="loadingIndicator">
                <span>🧠 AI正在思考中...</span>
            </div>

            <div class="input-section">
                <input 
                    type="text" 
                    id="textInput" 
                    class="text-input" 
                    placeholder="输入您的问题..."
                    maxlength="500"
                >
                <button id="voiceBtn" class="btn btn-voice" title="语音输入">
                    🎤
                </button>
                <button id="sendBtn" class="btn btn-primary" title="发送">
                    发送
                </button>
            </div>
        </div>
    </div>

    <!-- 性能信息 -->
    <div class="performance-info" id="performanceInfo">
        FPS: <span id="fpsCounter">--</span> |
        Triangles: <span id="triangleCounter">--</span> |
        M3 GPU: <span id="gpuInfo">WebGL 2.0</span>
    </div>



    <!-- 引入JavaScript模块 -->
    <script src="realistic_face_manager.js"></script>
    <script src="rpm_avatar_engine.js"></script>
    <script src="rpm_web_client.js"></script>
    
    <script>
        // 设置欢迎消息时间
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();
        
        // 检测M3芯片优化
        console.log('🍎 检测到macOS设备，启用M3优化');
        console.log('🎮 WebGL版本:', document.createElement('canvas').getContext('webgl2') ? '2.0' : '1.0');
    </script>
</body>
</html>
