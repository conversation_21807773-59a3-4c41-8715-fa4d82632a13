/**
 * Ready Player Me + 实时面部动画引擎
 * 基于Three.js + WebGL，针对M3芯片优化
 */

class RPMAvatarEngine {
    constructor(canvas) {
        this.canvas = canvas;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.avatar = null;
        this.mixer = null;
        this.clock = new THREE.Clock();
        
        // 面部动画控制
        this.morphTargets = {};
        this.isAnimating = false;
        this.speechIntensity = 0;
        this.blinkTimer = 0;

        // 真实人脸管理器
        this.faceManager = null;
        this.currentAvatarData = null;
        
        // 音频分析
        this.audioContext = null;
        this.analyser = null;
        this.audioData = null;
        
        // 初始化
        this.initialize();
    }

    async initialize() {
        try {
            console.log('🚀 初始化Ready Player Me引擎...');
            
            // 设置Three.js场景
            this.setupScene();

            // 初始化人脸管理器
            this.initializeFaceManager();

            // 加载默认头像
            await this.loadDefaultAvatar();
            
            // 设置动画循环
            this.startAnimationLoop();
            
            // 初始化音频分析
            this.initializeAudioAnalysis();
            
            console.log('✅ Ready Player Me引擎初始化成功');
            
        } catch (error) {
            console.error('❌ Ready Player Me引擎初始化失败:', error);
        }
    }

    setupScene() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);

        // 创建相机 - 调整为头部特写视角
        this.camera = new THREE.PerspectiveCamera(
            45,
            this.canvas.clientWidth / this.canvas.clientHeight,
            0.1,
            1000
        );
        // 设置相机位置：调整到最佳头部观看角度
        this.camera.position.set(0, 0, 2.5);
        this.camera.lookAt(0, 0, 0); // 看向头部位置

        // 创建渲染器 (利用M3的WebGL加速)
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: this.canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 优化Retina显示
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // 添加灯光
        this.setupLighting();

        console.log('✅ Three.js场景设置完成');
        console.log('📷 相机位置:', this.camera.position);
        console.log('📷 相机视角:', this.camera.fov);
    }

    initializeFaceManager() {
        try {
            if (typeof RealisticFaceManager !== 'undefined') {
                this.faceManager = new RealisticFaceManager();
                console.log('✅ 人脸管理器初始化成功');
            } else {
                console.error('❌ RealisticFaceManager类未找到');
                throw new Error('RealisticFaceManager未加载');
            }
        } catch (error) {
            console.error('❌ 人脸管理器初始化失败:', error);
            this.faceManager = null;
        }
    }

    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        this.scene.add(ambientLight);

        // 主光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        this.scene.add(directionalLight);

        // 补光
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-1, 0, 1);
        this.scene.add(fillLight);
    }

    async loadDefaultAvatar() {
        try {
            console.log('🌟 加载真实美女头像...');

            // 检查依赖
            if (!this.faceManager) {
                console.error('❌ 人脸管理器未初始化');
                this.createFallbackAvatar();
                return;
            }

            // 使用真实人脸管理器加载美女头像
            this.currentAvatarData = await this.faceManager.loadRealisticFace(0);

            if (this.currentAvatarData && this.currentAvatarData.avatar) {
                console.log('✅ 美女头像数据加载成功');
                this.setupRealisticAvatar(this.currentAvatarData);
            } else {
                console.warn('⚠️ 无法加载真实美女头像，使用备用方案');
                this.createFallbackAvatar();
            }

        } catch (error) {
            console.error('❌ 真实头像加载失败:', error);
            console.error('错误详情:', error);
            this.createFallbackAvatar();
        }
    }

    loadGLTF(loader, url) {
        return new Promise((resolve, reject) => {
            loader.load(
                url,
                (gltf) => resolve(gltf),
                (progress) => {
                    console.log('📊 加载进度:', (progress.loaded / progress.total * 100) + '%');
                },
                (error) => reject(error)
            );
        });
    }

    setupAvatar(gltf) {
        this.avatar = gltf.scene;

        // 调整头像位置和大小 - 确保头部在视野中心
        this.avatar.position.set(0, -1.8, 0); // 继续向下移动，让头部完全显示
        this.avatar.scale.set(1.8, 1.8, 1.8); // 进一步放大模型
        
        // 设置材质
        this.avatar.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                
                // 如果有morph targets，保存引用
                if (child.morphTargetDictionary) {
                    this.morphTargets[child.name] = child;
                    console.log('🎭 发现morph targets:', Object.keys(child.morphTargetDictionary));
                }
            }
        });

        // 添加到场景
        this.scene.add(this.avatar);

        // 设置动画混合器
        if (gltf.animations && gltf.animations.length > 0) {
            this.mixer = new THREE.AnimationMixer(this.avatar);
            
            // 播放待机动画
            const idleAnimation = gltf.animations.find(anim => 
                anim.name.toLowerCase().includes('idle') || 
                anim.name.toLowerCase().includes('breathing')
            );
            
            if (idleAnimation) {
                const action = this.mixer.clipAction(idleAnimation);
                action.play();
            }
        }

        console.log('✅ Ready Player Me头像设置完成');
        console.log('📍 头像位置:', this.avatar.position);
        console.log('📏 头像缩放:', this.avatar.scale);
    }

    setupRealisticAvatar(avatarData) {
        this.avatar = avatarData.avatar;

        // 添加到场景
        this.scene.add(this.avatar);

        // 设置动画混合器
        if (avatarData.animations && avatarData.animations.length > 0) {
            this.mixer = new THREE.AnimationMixer(this.avatar);

            // 播放待机动画
            const idleAnimation = avatarData.animations.find(anim =>
                anim.name.toLowerCase().includes('idle') ||
                anim.name.toLowerCase().includes('breathing')
            );

            if (idleAnimation) {
                const action = this.mixer.clipAction(idleAnimation);
                action.play();
            }
        }

        // 如果是程序化人脸，保存特殊引用
        if (avatarData.isProceduralFace) {
            this.proceduralFaceParts = this.avatar.userData;
        }

        console.log(`✨ 真实美女${avatarData.faceInfo.name}已就绪`);
        console.log('📍 头像位置:', this.avatar.position);
        console.log('📏 头像缩放:', this.avatar.scale);
    }

    createFallbackAvatar() {
        console.log('🎨 创建备用3D头像...');
        
        // 创建简单的3D头像作为备用
        const group = new THREE.Group();

        // 头部 - 调整大小和位置
        const headGeometry = new THREE.SphereGeometry(0.4, 32, 32);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 0.2; // 稍微向上
        head.castShadow = true;
        group.add(head);

        // 眼睛 - 调整位置和大小
        const eyeGeometry = new THREE.SphereGeometry(0.04, 16, 16);
        const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.1, 0.25, 0.32); // 调整到头部相对位置
        group.add(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.1, 0.25, 0.32);
        group.add(rightEye);

        // 嘴巴 - 调整位置和大小
        const mouthGeometry = new THREE.SphereGeometry(0.025, 16, 16);
        const mouthMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
        const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
        mouth.position.set(0, 0.1, 0.35); // 调整到头部相对位置
        mouth.scale.set(2.5, 1, 1);
        group.add(mouth);

        // 保存引用用于动画
        this.fallbackParts = {
            head: head,
            leftEye: leftEye,
            rightEye: rightEye,
            mouth: mouth
        };

        // 应用与Ready Player Me相同的位置和缩放
        group.position.set(0, -1.8, 0);
        group.scale.set(1.8, 1.8, 1.8);

        this.avatar = group;
        this.scene.add(this.avatar);

        console.log('✅ 备用3D头像创建完成');
        console.log('📍 备用头像位置:', this.avatar.position);
        console.log('📏 备用头像缩放:', this.avatar.scale);
    }

    initializeAudioAnalysis() {
        try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建分析器
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            this.audioData = new Uint8Array(this.analyser.frequencyBinCount);
            
            console.log('✅ 音频分析初始化完成');
            
        } catch (error) {
            console.warn('⚠️ 音频分析初始化失败:', error);
        }
    }

    startAnimationLoop() {
        const animate = () => {
            requestAnimationFrame(animate);
            
            const deltaTime = this.clock.getDelta();
            
            // 更新动画混合器
            if (this.mixer) {
                this.mixer.update(deltaTime);
            }
            
            // 更新面部动画
            this.updateFacialAnimation(deltaTime);
            
            // 渲染场景
            this.renderer.render(this.scene, this.camera);
        };
        
        animate();
        console.log('✅ 动画循环已启动');
    }

    updateFacialAnimation(deltaTime) {
        if (!this.avatar) return;

        // 眨眼动画
        this.blinkTimer += deltaTime;
        if (this.blinkTimer > 3 + Math.random() * 2) { // 3-5秒随机眨眼
            this.blink();
            this.blinkTimer = 0;
        }

        // 说话动画
        if (this.isAnimating) {
            this.updateSpeechAnimation();
        }

        // 轻微的头部摆动
        if (this.avatar) {
            this.avatar.rotation.y = Math.sin(this.clock.elapsedTime * 0.5) * 0.05;
            this.avatar.rotation.x = Math.sin(this.clock.elapsedTime * 0.3) * 0.02;
        }
    }

    blink() {
        if (this.morphTargets.head) {
            // 使用morph targets实现眨眼
            const head = this.morphTargets.head;
            if (head.morphTargetDictionary && head.morphTargetDictionary.eyesClosed !== undefined) {
                const blinkIndex = head.morphTargetDictionary.eyesClosed;
                
                // 眨眼动画
                const blinkDuration = 0.15;
                let blinkProgress = 0;
                
                const blinkFrame = () => {
                    blinkProgress += 0.05;
                    
                    if (blinkProgress < 0.5) {
                        head.morphTargetInfluences[blinkIndex] = blinkProgress * 2;
                    } else {
                        head.morphTargetInfluences[blinkIndex] = 2 - blinkProgress * 2;
                    }
                    
                    if (blinkProgress < 1) {
                        requestAnimationFrame(blinkFrame);
                    } else {
                        head.morphTargetInfluences[blinkIndex] = 0;
                    }
                };
                
                blinkFrame();
            }
        } else if (this.fallbackParts) {
            // 备用头像的眨眼动画
            const { leftEye, rightEye } = this.fallbackParts;

            leftEye.scale.y = 0.1;
            rightEye.scale.y = 0.1;

            setTimeout(() => {
                leftEye.scale.y = 1;
                rightEye.scale.y = 1;
            }, 150);
        } else if (this.proceduralFaceParts && this.proceduralFaceParts.eyes) {
            // 程序化真实人脸的眨眼动画
            const { leftEyeball, rightEyeball } = this.proceduralFaceParts.eyes;

            // 更自然的眨眼效果
            leftEyeball.scale.y = 0.1;
            rightEyeball.scale.y = 0.1;

            setTimeout(() => {
                leftEyeball.scale.y = 1;
                rightEyeball.scale.y = 1;
            }, 120);
        }
    }

    startSpeechAnimation() {
        this.isAnimating = true;
        console.log('🎤 开始3D说话动画');
    }

    updateSpeechAnimation() {
        // 分析音频数据
        if (this.analyser && this.audioData) {
            this.analyser.getByteFrequencyData(this.audioData);
            
            // 计算音频强度
            let sum = 0;
            for (let i = 0; i < this.audioData.length; i++) {
                sum += this.audioData[i];
            }
            this.speechIntensity = sum / this.audioData.length / 255;
        } else {
            // 如果没有音频分析，使用随机值模拟
            this.speechIntensity = 0.3 + Math.random() * 0.4;
        }

        // 应用到morph targets
        if (this.morphTargets.head) {
            const head = this.morphTargets.head;
            if (head.morphTargetDictionary) {
                // 嘴部动画
                if (head.morphTargetDictionary.mouthOpen !== undefined) {
                    const mouthIndex = head.morphTargetDictionary.mouthOpen;
                    head.morphTargetInfluences[mouthIndex] = this.speechIntensity;
                }
                
                // 其他表情
                if (head.morphTargetDictionary.mouthSmile !== undefined) {
                    const smileIndex = head.morphTargetDictionary.mouthSmile;
                    head.morphTargetInfluences[smileIndex] = this.speechIntensity * 0.3;
                }
            }
        } else if (this.fallbackParts) {
            // 备用头像的说话动画
            const { mouth } = this.fallbackParts;
            mouth.scale.y = 1 + this.speechIntensity * 0.5;
            mouth.scale.x = 2 + this.speechIntensity * 0.3;
        } else if (this.proceduralFaceParts && this.proceduralFaceParts.mouth) {
            // 程序化真实人脸的说话动画
            const { upperLip, lowerLip } = this.proceduralFaceParts.mouth;

            // 更自然的嘴部动画
            const mouthOpen = this.speechIntensity * 0.3;
            upperLip.position.y = 0.12 + mouthOpen * 0.5;
            lowerLip.position.y = 0.08 - mouthOpen * 0.5;

            // 嘴唇形状变化
            upperLip.scale.y = 0.8 + this.speechIntensity * 0.4;
            lowerLip.scale.y = 1.2 + this.speechIntensity * 0.3;
        }
    }

    stopSpeechAnimation() {
        this.isAnimating = false;
        this.speechIntensity = 0;
        
        // 重置morph targets
        if (this.morphTargets.head) {
            const head = this.morphTargets.head;
            if (head.morphTargetInfluences) {
                for (let i = 0; i < head.morphTargetInfluences.length; i++) {
                    head.morphTargetInfluences[i] = 0;
                }
            }
        } else if (this.fallbackParts) {
            // 重置备用头像
            const { mouth } = this.fallbackParts;
            mouth.scale.set(2, 1, 1);
        }
        
        console.log('✅ 3D说话动画结束');
    }

    connectAudioSource(audioElement) {
        if (this.audioContext && this.analyser && audioElement) {
            try {
                const source = this.audioContext.createMediaElementSource(audioElement);
                source.connect(this.analyser);
                this.analyser.connect(this.audioContext.destination);
                console.log('✅ 音频源已连接到分析器');
            } catch (error) {
                console.warn('⚠️ 音频源连接失败:', error);
            }
        }
    }

    setStatus(status) {
        switch (status) {
            case 'listening':
                // 听取状态 - 头部稍微前倾
                if (this.avatar) {
                    this.avatar.rotation.x = -0.1;
                }
                break;
            case 'thinking':
                // 思考状态 - 轻微摇头
                break;
            case 'speaking':
                this.startSpeechAnimation();
                break;
            case 'idle':
            default:
                this.stopSpeechAnimation();
                if (this.avatar) {
                    this.avatar.rotation.x = 0;
                }
                break;
        }
    }

    handleResize() {
        if (this.camera && this.renderer) {
            this.camera.aspect = this.canvas.clientWidth / this.canvas.clientHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        }
    }



    async changeToNextFace() {
        try {
            console.log('🔄 切换到下一个美女...');

            // 移除当前头像
            if (this.avatar) {
                this.scene.remove(this.avatar);
            }

            // 加载下一个美女
            const nextIndex = this.faceManager.getNextFace();
            this.currentAvatarData = await this.faceManager.loadRealisticFace(nextIndex);

            if (this.currentAvatarData) {
                this.setupRealisticAvatar(this.currentAvatarData);
                return this.currentAvatarData.faceInfo;
            }

        } catch (error) {
            console.error('❌ 切换美女失败:', error);
        }

        return null;
    }

    getCurrentFaceInfo() {
        return this.currentAvatarData ? this.currentAvatarData.faceInfo : null;
    }

    getAllFaces() {
        return this.faceManager.getAllFaces();
    }

    destroy() {
        if (this.renderer) {
            this.renderer.dispose();
        }
        if (this.audioContext) {
            this.audioContext.close();
        }
        console.log('🗑️ Ready Player Me引擎已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RPMAvatarEngine;
}
