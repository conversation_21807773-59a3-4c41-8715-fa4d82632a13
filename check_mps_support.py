#!/usr/bin/env python3
"""
检查Mac MPS GPU加速支持
"""

import sys
import os

def check_mps_support():
    """检查MPS支持状态"""
    print("🔍 检查Mac MPS GPU加速支持")
    print("=" * 40)
    
    # 检查PyTorch
    try:
        import torch
        print(f"✅ PyTorch已安装: {torch.__version__}")
        
        # 检查MPS可用性
        if torch.backends.mps.is_available():
            print("✅ MPS可用: 支持GPU加速")
            
            # 检查MPS构建
            if torch.backends.mps.is_built():
                print("✅ MPS构建: 正常")
            else:
                print("⚠️ MPS构建: 异常")
            
            # 测试MPS设备
            try:
                device = torch.device("mps")
                x = torch.randn(10, 10, device=device)
                y = torch.randn(10, 10, device=device)
                z = torch.mm(x, y)
                print("✅ MPS设备测试: 通过")
                print(f"📊 测试张量形状: {z.shape}")
                return True
            except Exception as e:
                print(f"❌ MPS设备测试失败: {e}")
                return False
        else:
            print("❌ MPS不可用")
            print("💡 可能原因:")
            print("  - macOS版本过低 (需要 macOS 12.3+)")
            print("  - PyTorch版本过低 (需要 1.12+)")
            print("  - 不是Apple Silicon Mac")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        print("💡 安装命令: pip install torch torchvision")
        return False

def check_system_info():
    """检查系统信息"""
    print("\n🖥️ 系统信息")
    print("=" * 20)
    
    # macOS版本
    try:
        import platform
        print(f"系统: {platform.system()} {platform.release()}")
        print(f"架构: {platform.machine()}")
        print(f"处理器: {platform.processor()}")
    except Exception as e:
        print(f"无法获取系统信息: {e}")

def check_environment_variables():
    """检查相关环境变量"""
    print("\n🔧 环境变量")
    print("=" * 15)
    
    env_vars = [
        "PYTORCH_ENABLE_MPS_FALLBACK",
        "MINERU_DEVICE",
        "CUDA_VISIBLE_DEVICES"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚪ {var}: 未设置")

def recommend_settings():
    """推荐设置"""
    print("\n💡 推荐设置")
    print("=" * 15)
    print("为了获得最佳MPS性能，建议设置以下环境变量:")
    print("")
    print("export PYTORCH_ENABLE_MPS_FALLBACK=1")
    print("export MINERU_DEVICE=mps")
    print("")
    print("或者在.env文件中添加:")
    print("PYTORCH_ENABLE_MPS_FALLBACK=1")
    print("MINERU_DEVICE=mps")

def main():
    """主函数"""
    # 检查系统信息
    check_system_info()
    
    # 检查环境变量
    check_environment_variables()
    
    # 检查MPS支持
    mps_available = check_mps_support()
    
    # 推荐设置
    recommend_settings()
    
    # 总结
    print("\n📋 总结")
    print("=" * 10)
    if mps_available:
        print("🎉 您的系统支持MPS GPU加速！")
        print("💡 使用以下命令运行RAGAnything:")
        print("   python examples/raganything_example.py your_document.pdf --device mps")
        print("   或者使用: ./run_with_mps.sh your_document.pdf")
    else:
        print("❌ 您的系统不支持MPS，将使用CPU模式")
        print("💡 使用以下命令运行RAGAnything:")
        print("   python examples/raganything_example.py your_document.pdf --device cpu")

if __name__ == "__main__":
    main()
