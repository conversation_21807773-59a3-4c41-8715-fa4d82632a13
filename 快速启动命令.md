# RAGAnything 问答模式快速启动命令

## 🚀 一键启动命令

### Windows用户
```bash
# 方法1：使用启动脚本（推荐）
run_qa_mode.bat "your_document.pdf"

# 方法2：直接Python命令
python examples/qa_mode_example.py "your_document.pdf" --api-key sk-your-api-key-here
```

### Linux/Mac用户
```bash
# 方法1：使用启动脚本（推荐）
./run_qa_mode.sh "your_document.pdf"

# 方法2：直接Python命令
python examples/qa_mode_example.py "your_document.pdf" --api-key sk-your-api-key-here
```

## 📋 环境配置（首次使用）

### 1. 安装依赖
```bash
pip install openai dashscope requests
```

### 2. 配置API密钥
```bash
# 创建.env文件
echo "DASHSCOPE_API_KEY=sk-your-api-key-here" > .env
```

### 3. 给脚本执行权限（Linux/Mac）
```bash
chmod +x run_qa_mode.sh
```

## 🎯 常用运行场景

### 场景1：处理新文档
```bash
# Windows
run_qa_mode.bat "documents/research_paper.pdf"

# Linux/Mac
./run_qa_mode.sh "documents/research_paper.pdf"
```

### 场景2：使用现有知识库
```bash
# Windows
run_qa_mode.bat --existing

# Linux/Mac
./run_qa_mode.sh --existing
```

### 场景3：加载之前的会话
```bash
# Windows
run_qa_mode.bat --session "my_session.json"

# Linux/Mac
./run_qa_mode.sh --session "my_session.json"
```

### 场景4：GPU加速模式
```bash
# 使用CUDA GPU
python examples/qa_mode_example.py "document.pdf" --device cuda --api-key sk-your-api-key-here

# 使用Apple MPS
python examples/qa_mode_example.py "document.pdf" --device mps --api-key sk-your-api-key-here
```

## 💬 问答模式使用

### 启动后的基本操作
```
[hybrid] 🤔 请输入您的问题: 这个文档的主要内容是什么？
[hybrid] 🤔 请输入您的问题: /help
[hybrid] 🤔 请输入您的问题: /mode
[hybrid] 🤔 请输入您的问题: /exit
```

### 多模态查询示例
```
[hybrid] 🤔 请输入您的问题: 分析这个数据表格
🎨 检测到可能的多模态查询，是否添加多模态内容? (y/N): y
📊 请输入表格数据: 产品,销量,价格
A,100,50
B,200,30
```

## 🔧 完整命令参数

```bash
python examples/qa_mode_example.py [文档路径] [选项]

选项:
  --working-dir, -w    工作目录 (默认: ./rag_storage)
  --output, -o         输出目录 (默认: ./output)  
  --api-key           API密钥
  --base-url          API端点
  --device            设备 (auto/cpu/mps/cuda)
  --session, -s       会话文件
  --verbose, -v       详细日志
```

## 📝 实际使用示例

### 示例1：分析学术论文
```bash
./run_qa_mode.sh "papers/machine_learning_survey.pdf"

# 问答示例：
# Q: 这篇论文的主要贡献是什么？
# Q: 文中提到了哪些机器学习算法？
# Q: 实验结果如何？
```

### 示例2：企业文档分析
```bash
./run_qa_mode.sh "reports/quarterly_report.pdf"

# 问答示例：
# Q: 本季度的营收情况如何？
# Q: 主要的业务增长点在哪里？
# Q: 分析这个财务数据表格
```

### 示例3：技术文档查询
```bash
./run_qa_mode.sh "docs/api_documentation.pdf"

# 问答示例：
# Q: 如何使用这个API？
# Q: 有哪些可用的端点？
# Q: 认证方式是什么？
```

---

## 🎉 立即开始

选择适合您的命令，替换文档路径和API密钥，即可开始使用RAGAnything的智能问答功能！

```bash
# 最简单的启动方式
./run_qa_mode.sh "your_document.pdf"
```
