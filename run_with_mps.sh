#!/bin/bash
# MPS加速运行脚本

echo "🚀 RAGAnything MPS加速运行脚本"
echo "=" * 50

# 检查PyTorch MPS支持
echo "🔍 检查MPS支持..."
python3 -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'MPS可用: {torch.backends.mps.is_available()}')
print(f'MPS构建: {torch.backends.mps.is_built()}')
if torch.backends.mps.is_available():
    print('✅ MPS支持正常')
else:
    print('❌ MPS不可用，将使用CPU')
"

# 设置MPS环境变量
export PYTORCH_ENABLE_MPS_FALLBACK=1
export MINERU_DEVICE=mps

# 设置API环境变量
export DASHSCOPE_API_KEY=sk-c7b965ee5fc64ab482174967dabd4805
export OPENAI_API_KEY=sk-c7b965ee5fc64ab482174967dabd4805
export OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

echo ""
echo "🔧 环境变量配置:"
echo "  PYTORCH_ENABLE_MPS_FALLBACK: $PYTORCH_ENABLE_MPS_FALLBACK"
echo "  MINERU_DEVICE: $MINERU_DEVICE"
echo "  API端点: $OPENAI_BASE_URL"
echo ""

# 检查文档参数
if [ $# -eq 0 ]; then
    echo "❌ 请提供文档路径"
    echo "用法: $0 <文档路径> [其他参数]"
    echo "示例: $0 RAG/附件/sample_questions.pdf"
    echo "示例: $0 test_document.md --device mps"
    exit 1
fi

DOCUMENT_PATH="$1"
shift  # 移除第一个参数，保留其他参数

echo "📄 处理文档: $DOCUMENT_PATH"
echo "🎯 使用设备: MPS (GPU加速)"
echo ""

# 运行RAGAnything
echo "🚀 启动RAGAnything..."
python examples/raganything_example.py "$DOCUMENT_PATH" --device mps "$@"

echo ""
echo "✅ 处理完成！"
