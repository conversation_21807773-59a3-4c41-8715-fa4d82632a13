#!/bin/bash

# RAGAnything 交互式问答模式启动脚本

echo "🤖 RAGAnything 交互式问答模式启动脚本"
echo "========================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: Python3未安装"
    echo "请安装Python3: https://www.python.org/downloads/"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "⚠️ 虚拟环境不存在，正在创建..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ 创建虚拟环境失败"
        exit 1
    fi
    echo "✅ 虚拟环境创建成功"
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 检查是否有.env文件
if [ ! -f ".env" ]; then
    echo "⚠️ .env文件不存在，请先配置API密钥"
    echo "创建.env文件模板..."
    cat > .env << EOF
# 阿里云百炼API配置
DASHSCOPE_API_KEY=sk-your-api-key-here
OPENAI_API_KEY=sk-your-api-key-here
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
EOF
    echo "✅ .env模板已创建，请编辑并填入您的API密钥"
    echo "📝 请编辑.env文件，将 sk-your-api-key-here 替换为您的真实API密钥"
    echo "编辑命令: nano .env 或 vim .env"
    exit 1
fi

# 加载环境变量
export $(grep -v '^#' .env | xargs)

# 检查API密钥
if [ "$DASHSCOPE_API_KEY" = "sk-your-api-key-here" ] || [ -z "$DASHSCOPE_API_KEY" ]; then
    echo "❌ 请在.env文件中配置您的真实API密钥"
    exit 1
fi

echo "🔑 API密钥: ${DASHSCOPE_API_KEY:0:8}...${DASHSCOPE_API_KEY: -4}"
echo "🌐 API端点: $OPENAI_BASE_URL"

# 安装依赖
echo "🔧 检查依赖包..."
pip install openai dashscope requests > /dev/null 2>&1

# 创建必要目录
mkdir -p rag_storage

echo "🚀 启动RAGAnything问答模式..."
echo "========================================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "📋 使用方法:"
    echo "  1. 处理新文档并启动问答: ./run_qa_mode.sh document.pdf"
    echo "  2. 使用现有知识库启动问答: ./run_qa_mode.sh --existing"
    echo "  3. 加载会话文件: ./run_qa_mode.sh --session session.json"
    echo ""
    read -p "请选择模式 (1/2/3): " choice
    
    case $choice in
        1)
            read -p "请输入文档路径: " doc_path
            if [ ! -f "$doc_path" ]; then
                echo "❌ 文档文件不存在"
                exit 1
            fi
            python3 examples/qa_mode_example.py "$doc_path" --api-key "$DASHSCOPE_API_KEY"
            ;;
        2)
            python3 examples/qa_mode_example.py --working-dir ./rag_storage --api-key "$DASHSCOPE_API_KEY"
            ;;
        3)
            read -p "请输入会话文件路径: " session_file
            python3 examples/qa_mode_example.py --working-dir ./rag_storage --session "$session_file" --api-key "$DASHSCOPE_API_KEY"
            ;;
        *)
            echo "❌ 无效选择"
            exit 1
            ;;
    esac
elif [ "$1" = "--existing" ]; then
    # 使用现有知识库
    echo "📚 使用现有知识库启动问答模式..."
    python3 examples/qa_mode_example.py --working-dir ./rag_storage --api-key "$DASHSCOPE_API_KEY"
elif [ "$1" = "--session" ]; then
    # 加载会话
    if [ -z "$2" ]; then
        echo "❌ 请提供会话文件路径"
        echo "用法: ./run_qa_mode.sh --session session.json"
        exit 1
    fi
    echo "📂 加载会话文件: $2"
    python3 examples/qa_mode_example.py --working-dir ./rag_storage --session "$2" --api-key "$DASHSCOPE_API_KEY"
else
    # 处理文档
    if [ ! -f "$1" ]; then
        echo "❌ 文档文件不存在: $1"
        exit 1
    fi
    echo "📄 处理文档: $1"
    python3 examples/qa_mode_example.py "$1" --api-key "$DASHSCOPE_API_KEY"
fi

if [ $? -eq 0 ]; then
    echo "✅ 问答模式已结束"
else
    echo "❌ 问答模式启动失败，请检查错误信息"
fi

echo "========================================"
