/**
 * Ready Player Me Web客户端 - M3优化版
 * 集成3D数字人、语音识别、WebSocket通信
 */
class RPMWebRAGClient {
    constructor() {
        // 核心组件
        this.avatar = null;
        this.websocket = null;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        
        // 状态管理
        this.isConnected = false;
        this.isListening = false;
        this.isSpeaking = false;
        this.audioContextInitialized = false;
        
        // 性能监控
        this.performanceMonitor = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now()
        };
        
        // DOM元素
        this.elements = {
            canvas: document.getElementById('avatarCanvas'),
            status: document.getElementById('avatarStatus'),
            connectionStatus: document.getElementById('connectionStatus'),
            messagesContainer: document.getElementById('messagesContainer'),
            textInput: document.getElementById('textInput'),
            voiceBtn: document.getElementById('voiceBtn'),
            sendBtn: document.getElementById('sendBtn'),
            loadingIndicator: document.getElementById('loadingIndicator'),
            resetCameraBtn: document.getElementById('resetCameraBtn'),
            toggleAnimationBtn: document.getElementById('toggleAnimationBtn'),
            changeAvatarBtn: document.getElementById('changeAvatarBtn'),
            fpsCounter: document.getElementById('fpsCounter'),
            triangleCounter: document.getElementById('triangleCounter'),
            gpuInfo: document.getElementById('gpuInfo')
        };
        
        // 初始化
        this.initialize();
    }

    async initialize() {
        try {
            console.log('🚀 初始化Ready Player Me Web客户端...');
            
            // 检测设备能力
            this.detectDeviceCapabilities();
            
            // 初始化3D数字人
            await this.initializeAvatar();
            
            // 初始化WebSocket连接
            this.initializeWebSocket();
            
            // 初始化语音系统
            this.initializeSpeechSynthesis();
            this.initializeSpeechRecognition();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化音频上下文
            this.initializeAudioContext();
            
            // 启动性能监控
            this.startPerformanceMonitoring();
            
            // 更新状态
            this.updateAvatarStatus('✅ Ready Player Me已就绪');
            
            console.log('✅ Ready Player Me Web客户端初始化成功');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.updateConnectionStatus('❌ 初始化失败', false);
            this.updateAvatarStatus('❌ 初始化失败');
        }
    }

    detectDeviceCapabilities() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
        
        if (gl) {
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'Unknown';
            
            console.log('🎮 GPU信息:', renderer);
            console.log('🔧 WebGL版本:', gl.constructor.name);
            
            // 检测是否为Apple Silicon
            if (renderer.includes('Apple') || navigator.platform.includes('Mac')) {
                console.log('🍎 检测到Apple Silicon，启用优化');
                this.elements.gpuInfo.textContent = 'Apple M3 GPU';
            }
            
            // 设置最佳渲染参数
            this.renderSettings = {
                antialias: true,
                powerPreference: 'high-performance',
                alpha: true,
                premultipliedAlpha: false
            };
        }
    }

    async initializeAvatar() {
        try {
            this.updateAvatarStatus('🎭 加载3D数字人...');
            
            // 创建Ready Player Me引擎
            this.avatar = new RPMAvatarEngine(this.elements.canvas);
            
            // 等待初始化完成
            await new Promise(resolve => {
                const checkInit = () => {
                    if (this.avatar.scene && this.avatar.renderer) {
                        resolve();
                    } else {
                        setTimeout(checkInit, 100);
                    }
                };
                checkInit();
            });
            
            console.log('✅ 3D数字人引擎初始化成功');
            
        } catch (error) {
            console.error('❌ 3D数字人初始化失败:', error);
            this.updateAvatarStatus('❌ 3D数字人加载失败');
        }
    }

    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            this.isConnected = true;
            this.updateConnectionStatus('✅ 已连接', true);
            console.log('✅ WebSocket连接成功');
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleServerMessage(data);
            } catch (error) {
                console.error('❌ 消息解析失败:', error);
            }
        };

        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            this.updateConnectionStatus('❌ 连接错误', false);
        };

        this.websocket.onclose = () => {
            this.isConnected = false;
            this.updateConnectionStatus('🔄 连接断开', false);
            console.log('🔌 WebSocket连接关闭');
            
            // 尝试重连
            setTimeout(() => {
                if (!this.isConnected) {
                    console.log('🔄 尝试重新连接...');
                    this.initializeWebSocket();
                }
            }, 3000);
        };
    }

    initializeSpeechSynthesis() {
        if (this.synthesis) {
            this.synthesis.addEventListener('voiceschanged', () => {
                const voices = this.synthesis.getVoices();
                console.log(`🎤 语音列表已加载，共 ${voices.length} 个语音`);
            });
        }
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'zh-CN';
            
            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateVoiceButton(true);
                this.avatar?.setStatus('listening');
                this.updateAvatarStatus('👂 正在听取...');
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.textInput.value = transcript;
                this.sendQuery(transcript);
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceButton(false);
                this.avatar?.setStatus('idle');
                this.updateAvatarStatus('✅ Ready Player Me已就绪');
            };

            this.recognition.onerror = (event) => {
                console.error('❌ 语音识别错误:', event.error);
                this.isListening = false;
                this.updateVoiceButton(false);
                this.avatar?.setStatus('idle');
                this.updateAvatarStatus('❌ 语音识别失败');
                
                if (event.error === 'not-allowed') {
                    alert('请允许麦克风权限以使用语音输入功能');
                }
            };

            console.log('✅ 语音识别初始化成功');
        }
    }

    setupEventListeners() {
        // 发送按钮
        this.elements.sendBtn.addEventListener('click', () => {
            const text = this.elements.textInput.value.trim();
            if (text) {
                this.sendQuery(text);
                this.elements.textInput.value = '';
            }
        });

        // 回车发送
        this.elements.textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.elements.sendBtn.click();
            }
        });

        // 语音按钮
        this.elements.voiceBtn.addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // 3D控制按钮
        this.elements.resetCameraBtn.addEventListener('click', () => {
            this.resetCamera();
        });

        this.elements.toggleAnimationBtn.addEventListener('click', () => {
            this.toggleAnimation();
        });

        this.elements.changeAvatarBtn.addEventListener('click', () => {
            this.changeAvatar();
        });

        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.avatar?.handleResize();
        });

        // 输入框焦点处理
        this.elements.textInput.addEventListener('focus', () => {
            this.avatar?.setStatus('listening');
        });

        this.elements.textInput.addEventListener('blur', () => {
            if (!this.isListening && !this.isSpeaking) {
                this.avatar?.setStatus('idle');
            }
        });
    }

    initializeAudioContext() {
        this.audioContextInitialized = false;
        
        const initAudio = () => {
            if (!this.audioContextInitialized) {
                try {
                    const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    silentAudio.volume = 0;
                    silentAudio.play().then(() => {
                        this.audioContextInitialized = true;
                        console.log('✅ 音频上下文已初始化');
                        document.removeEventListener('click', initAudio);
                        document.removeEventListener('touchstart', initAudio);
                    }).catch(() => {
                        console.log('⚠️ 音频上下文初始化失败');
                    });
                } catch (error) {
                    console.log('⚠️ 音频上下文初始化异常:', error);
                }
            }
        };
        
        document.addEventListener('click', initAudio);
        document.addEventListener('touchstart', initAudio);
    }

    startPerformanceMonitoring() {
        const updatePerformance = () => {
            const now = performance.now();
            this.performanceMonitor.frameCount++;
            
            if (now >= this.performanceMonitor.lastTime + 1000) {
                this.performanceMonitor.fps = Math.round(
                    (this.performanceMonitor.frameCount * 1000) / 
                    (now - this.performanceMonitor.lastTime)
                );
                
                this.performanceMonitor.frameCount = 0;
                this.performanceMonitor.lastTime = now;
                
                // 更新性能显示
                this.elements.fpsCounter.textContent = this.performanceMonitor.fps;
                
                if (this.avatar && this.avatar.renderer) {
                    const info = this.avatar.renderer.info;
                    this.elements.triangleCounter.textContent = info.render.triangles;
                }
            }
            
            requestAnimationFrame(updatePerformance);
        };
        
        updatePerformance();
    }

    sendQuery(text) {
        if (!this.isConnected) {
            alert('未连接到服务器');
            return;
        }

        if (!text.trim()) {
            return;
        }

        // 显示用户消息
        this.addMessage('user', text);
        
        // 显示加载状态
        this.showLoading(true);
        this.avatar?.setStatus('thinking');
        this.updateAvatarStatus('🧠 正在思考...');

        // 设置超时处理
        const queryTimeout = setTimeout(() => {
            console.warn('⚠️ 查询超时');
            this.showLoading(false);
            this.avatar?.setStatus('idle');
            this.updateAvatarStatus('⏰ 查询超时，请重试');
            this.addMessage('system', '查询超时，请尝试更简短的问题或重新提问');
        }, 30000);

        const message = {
            type: 'query',
            text: text.trim(),
            timestamp: Date.now()
        };

        try {
            this.websocket.send(JSON.stringify(message));
            console.log('📤 发送查询:', text);
            this.currentQueryTimeout = queryTimeout;
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
            clearTimeout(queryTimeout);
            this.showLoading(false);
            this.avatar?.setStatus('idle');
            this.updateAvatarStatus('❌ 发送失败');
        }
    }

    handleServerMessage(data) {
        console.log('📥 收到服务器消息:', data);

        switch (data.type) {
            case 'response':
                this.handleResponse(data);
                break;
            case 'error':
                this.handleError(data);
                break;
            default:
                console.warn('⚠️ 未知消息类型:', data.type);
        }
    }

    handleResponse(data) {
        // 清除查询超时
        if (this.currentQueryTimeout) {
            clearTimeout(this.currentQueryTimeout);
            this.currentQueryTimeout = null;
        }
        
        // 隐藏加载状态
        this.showLoading(false);
        
        // 显示回答
        this.addMessage('assistant', data.text);
        
        // 调试信息
        console.log('📥 收到响应数据:', {
            hasAudio: !!data.audio,
            audioType: data.audio_type,
            audioLength: data.audio ? data.audio.length : 0,
            textLength: data.text.length
        });
        
        // 播放语音并同步3D动画
        if (data.audio && data.audio_type === 'edge-tts') {
            console.log('🎤 检测到Edge-TTS音频，开始3D播放');
            this.playEdgeTTSAudio(data.audio, data.text);
        } else {
            console.log('🔄 使用浏览器TTS播放');
            this.speakResponse(data.text, data.language || 'zh-CN');
        }
    }

    handleError(data) {
        if (this.currentQueryTimeout) {
            clearTimeout(this.currentQueryTimeout);
            this.currentQueryTimeout = null;
        }
        
        this.showLoading(false);
        this.avatar?.setStatus('idle');
        this.updateAvatarStatus('❌ 处理失败');
        
        this.addMessage('system', `错误: ${data.message || '未知错误'}`);
        console.error('❌ 服务器错误:', data);
    }

    playEdgeTTSAudio(audioBase64, text) {
        try {
            console.log('🎤 开始处理3D Edge-TTS音频');
            
            const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;
            const audio = new Audio();
            audio.preload = 'auto';
            audio.volume = 0.9;
            
            audio.addEventListener('loadstart', () => {
                this.isSpeaking = true;
                this.avatar?.setStatus('speaking');
                this.updateAvatarStatus('🎭 正在播放3D语音...');
            });
            
            audio.addEventListener('play', () => {
                console.log('🎤 3D语音开始播放');
                // 连接音频到3D引擎进行分析
                this.avatar?.connectAudioSource(audio);
            });
            
            audio.addEventListener('ended', () => {
                console.log('✅ 3D语音播放完成');
                this.isSpeaking = false;
                this.avatar?.setStatus('idle');
                this.updateAvatarStatus('✅ Ready Player Me已就绪');
            });
            
            audio.addEventListener('error', (event) => {
                console.error('❌ 3D音频播放失败:', event);
                this.isSpeaking = false;
                this.avatar?.setStatus('idle');
                this.updateAvatarStatus('❌ 语音播放失败');
                
                // 降级到浏览器TTS
                setTimeout(() => {
                    this.speakResponse(text, 'zh-CN');
                }, 500);
            });
            
            audio.src = audioDataUrl;
            
            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('✅ 3D音频播放启动成功');
                }).catch(error => {
                    console.error('❌ 3D音频播放启动失败:', error);
                    this.updateAvatarStatus('请点击页面后重试');
                    setTimeout(() => {
                        this.speakResponse(text, 'zh-CN');
                    }, 1000);
                });
            }
            
        } catch (error) {
            console.error('❌ 3D Edge-TTS音频处理失败:', error);
            this.speakResponse(text, 'zh-CN');
        }
    }

    speakResponse(text, language = 'zh-CN') {
        if (!this.synthesis) {
            console.warn('⚠️ 浏览器不支持语音合成');
            return;
        }

        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language;
        utterance.rate = 0.85;
        utterance.pitch = 1.1;
        utterance.volume = 0.9;

        utterance.onstart = () => {
            this.isSpeaking = true;
            this.avatar?.setStatus('speaking');
            this.updateAvatarStatus('🎤 正在播放语音...');
        };

        utterance.onend = () => {
            this.isSpeaking = false;
            this.avatar?.setStatus('idle');
            this.updateAvatarStatus('✅ Ready Player Me已就绪');
        };

        utterance.onerror = (event) => {
            console.error('❌ 语音合成错误:', event);
            this.isSpeaking = false;
            this.avatar?.setStatus('idle');
            this.updateAvatarStatus('❌ 语音播放失败');
        };

        this.synthesis.speak(utterance);
    }

    toggleVoiceInput() {
        if (!this.recognition) {
            alert('您的浏览器不支持语音识别功能');
            return;
        }

        if (this.isListening) {
            this.recognition.stop();
        } else {
            try {
                this.recognition.start();
            } catch (error) {
                console.error('❌ 启动语音识别失败:', error);
                alert('启动语音识别失败，请检查麦克风权限');
            }
        }
    }

    resetCamera() {
        if (this.avatar && this.avatar.camera) {
            // 重置到头部特写视角
            this.avatar.camera.position.set(0, 0, 2.5);
            this.avatar.camera.lookAt(0, 0, 0);
            console.log('📷 相机视角已重置到头部特写');
            this.updateAvatarStatus('📷 视角已重置');

            setTimeout(() => {
                this.updateAvatarStatus('✅ Ready Player Me已就绪');
            }, 1500);
        }
    }

    toggleAnimation() {
        // 切换动画状态
        const btn = this.elements.toggleAnimationBtn;
        if (btn.classList.contains('active')) {
            btn.classList.remove('active');
            btn.textContent = '🎭 启用动画';
            // 停止额外动画
        } else {
            btn.classList.add('active');
            btn.textContent = '🎭 停用动画';
            // 启用额外动画
        }
    }

    async changeAvatar() {
        try {
            console.log('🌟 切换美女头像...');
            this.updateAvatarStatus('🔄 正在切换美女...');

            const newFaceInfo = await this.avatar.changeToNextFace();

            if (newFaceInfo) {
                this.updateAvatarStatus(`✨ 已切换到: ${newFaceInfo.name}`);
                this.addMessage('system', `🌟 已切换到美女: ${newFaceInfo.name} - ${newFaceInfo.description}`);

                setTimeout(() => {
                    this.updateAvatarStatus('✅ Ready Player Me已就绪');
                }, 3000);
            } else {
                this.updateAvatarStatus('❌ 切换失败');
                setTimeout(() => {
                    this.updateAvatarStatus('✅ Ready Player Me已就绪');
                }, 2000);
            }

        } catch (error) {
            console.error('❌ 切换美女失败:', error);
            this.updateAvatarStatus('❌ 切换失败');
            setTimeout(() => {
                this.updateAvatarStatus('✅ Ready Player Me已就绪');
            }, 2000);
        }
    }

    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        let roleText = '';
        switch (role) {
            case 'user':
                roleText = '您';
                break;
            case 'assistant':
                roleText = 'AI助手';
                break;
            case 'system':
                roleText = '系统';
                break;
        }

        messageDiv.innerHTML = `
            <div class="message-content">
                <strong>${roleText}:</strong><br>
                ${content.replace(/\n/g, '<br>')}
            </div>
            <div class="message-time">${timeString}</div>
        `;

        this.elements.messagesContainer.appendChild(messageDiv);
        this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
    }

    showLoading(show) {
        if (show) {
            this.elements.loadingIndicator.classList.add('show');
        } else {
            this.elements.loadingIndicator.classList.remove('show');
        }
    }

    updateVoiceButton(isRecording) {
        if (isRecording) {
            this.elements.voiceBtn.classList.add('recording');
            this.elements.voiceBtn.innerHTML = '🔴 停止';
        } else {
            this.elements.voiceBtn.classList.remove('recording');
            this.elements.voiceBtn.innerHTML = '🎤';
        }
    }

    updateConnectionStatus(message, isConnected) {
        this.elements.connectionStatus.textContent = message;
        this.elements.connectionStatus.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
    }

    updateAvatarStatus(status) {
        this.elements.status.textContent = status;
    }

    destroy() {
        if (this.websocket) {
            this.websocket.close();
        }
        if (this.recognition) {
            this.recognition.stop();
        }
        if (this.synthesis) {
            this.synthesis.cancel();
        }
        if (this.avatar) {
            this.avatar.destroy();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 初始化Ready Player Me Web RAG客户端...');
    window.rpmWebRAGClient = new RPMWebRAGClient();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.rpmWebRAGClient) {
        window.rpmWebRAGClient.destroy();
    }
});
