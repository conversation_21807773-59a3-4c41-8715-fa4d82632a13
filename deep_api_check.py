#!/usr/bin/env python3
"""
深度检查API调用情况 - 查找为什么没有扣费的真正原因
"""

import os
import sys
import json
import requests
import asyncio
from pathlib import Path

sys.path.append(str(Path(__file__).parent))

def check_api_key_validity():
    """检查API密钥是否真的有效"""
    print("🔍 深度检查API密钥有效性")
    print("=" * 35)
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    
    # 直接调用阿里云API检查
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    test_data = {
        "model": "qwen-turbo",
        "messages": [
            {"role": "user", "content": "测试API密钥有效性，请回复'有效'"}
        ],
        "max_tokens": 10
    }
    
    try:
        print("📤 直接调用阿里云API...")
        response = requests.post(
            "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API密钥有效！")
            print(f"📝 响应: {result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')}")
            
            # 检查是否有使用量信息
            if 'usage' in result:
                usage = result['usage']
                print(f"📊 Token使用量: {usage}")
                return True, usage
            else:
                print("⚠️ 响应中没有usage信息")
                return True, None
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📝 错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 直接API调用异常: {e}")
        return False, None

def check_possible_free_tier():
    """检查是否在使用免费额度"""
    print("\n🆓 检查免费额度情况")
    print("=" * 25)
    
    print("可能的免费使用情况:")
    print("1. 阿里云百炼新用户免费额度")
    print("2. 测试环境免费调用")
    print("3. 开发者试用额度")
    print("4. 特殊活动免费额度")
    
    print("\n💡 建议检查:")
    print("- 登录阿里云控制台查看账单详情")
    print("- 查看百炼服务的用量统计")
    print("- 检查是否有免费额度剩余")

def analyze_cache_creation_time():
    """分析缓存创建时间，看是否真的调用过API"""
    print("\n🕐 分析缓存创建时间")
    print("=" * 25)
    
    cache_file = "./rag_storage/kv_store_llm_response_cache.json"
    
    if os.path.exists(cache_file):
        try:
            # 获取文件修改时间
            import datetime
            mtime = os.path.getmtime(cache_file)
            creation_time = datetime.datetime.fromtimestamp(mtime)
            
            print(f"📅 缓存文件创建时间: {creation_time}")
            
            # 读取缓存内容
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            print(f"📊 缓存条目数量: {len(cache_data)}")
            
            # 分析缓存内容
            for i, (key, value) in enumerate(list(cache_data.items())[:3]):
                print(f"\n缓存项 {i+1}:")
                print(f"  键: {key[:80]}...")
                if isinstance(value, dict):
                    print(f"  类型: {type(value)}")
                    if 'response' in str(value):
                        print("  ✅ 包含API响应数据")
                    else:
                        print("  ⚠️ 可能不是API响应")
                
            return True
            
        except Exception as e:
            print(f"❌ 分析缓存失败: {e}")
            return False
    else:
        print("❌ 缓存文件不存在")
        return False

def check_local_model_usage():
    """检查是否意外使用了本地模型"""
    print("\n🏠 检查本地模型使用情况")
    print("=" * 30)
    
    # 检查OLLAMA配置
    ollama_tag = os.getenv("OLLAMA_EMULATING_MODEL_TAG")
    if ollama_tag:
        print(f"⚠️ 发现OLLAMA配置: {ollama_tag}")
        
        # 检查是否有ollama进程
        try:
            import subprocess
            result = subprocess.run(['pgrep', 'ollama'], capture_output=True, text=True)
            if result.returncode == 0:
                print("⚠️ 检测到OLLAMA进程正在运行！")
                print("💡 可能在使用本地模型而不是云端API")
                return True
            else:
                print("✅ OLLAMA进程未运行")
        except:
            print("❓ 无法检查OLLAMA进程")
    
    # 检查其他本地模型配置
    local_indicators = [
        "HUGGINGFACE_HUB_CACHE",
        "TRANSFORMERS_CACHE", 
        "HF_HOME"
    ]
    
    for indicator in local_indicators:
        if os.getenv(indicator):
            print(f"⚠️ 发现本地模型缓存配置: {indicator}")
    
    return False

def check_api_endpoint_routing():
    """检查API端点路由是否正确"""
    print("\n🌐 检查API端点路由")
    print("=" * 25)
    
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # 测试端点连通性
    try:
        print("📡 测试端点连通性...")
        response = requests.get(f"{base_url.rstrip('/v1')}", timeout=10)
        print(f"📊 端点响应状态: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ 端点正常（404是预期的，因为GET不被支持）")
        else:
            print(f"⚠️ 端点响应异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 端点连接失败: {e}")
    
    # 检查DNS解析
    try:
        import socket
        ip = socket.gethostbyname("dashscope.aliyuncs.com")
        print(f"🌐 DNS解析结果: {ip}")
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")

def check_billing_configuration():
    """检查计费相关配置"""
    print("\n💳 检查计费配置")
    print("=" * 20)
    
    print("需要在阿里云控制台检查的项目:")
    print("1. 百炼服务是否已开通")
    print("2. API密钥是否绑定到正确的账户")
    print("3. 账户是否有有效的付费方式")
    print("4. 是否在免费试用期内")
    print("5. 服务是否有欠费停用")
    
    print("\n🔗 阿里云控制台链接:")
    print("- 百炼控制台: https://bailian.console.aliyun.com/")
    print("- 费用中心: https://expense.console.aliyun.com/")
    print("- API调用记录: 在百炼控制台查看")

async def comprehensive_api_test():
    """综合API测试"""
    print("\n🧪 综合API测试")
    print("=" * 20)
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # 测试不同的API调用
    tests = [
        ("LLM调用", "qwen-turbo", "这是计费测试"),
        ("Embedding调用", "text-embedding-v1", "embedding测试")
    ]
    
    total_calls = 0
    successful_calls = 0
    
    for test_name, model, content in tests:
        try:
            print(f"\n📤 {test_name}...")
            
            if "embedding" in model:
                # Embedding调用
                from lightrag.llm.openai import openai_embed
                result = await openai_embed(
                    [content],
                    model=model,
                    api_key=api_key,
                    base_url=base_url
                )
                print(f"✅ {test_name}成功，向量维度: {len(result[0])}")
            else:
                # LLM调用
                from lightrag.llm.openai import openai_complete_if_cache
                result = await openai_complete_if_cache(
                    model,
                    content,
                    api_key=api_key,
                    base_url=base_url,
                    max_tokens=20
                )
                print(f"✅ {test_name}成功: {result}")
            
            successful_calls += 1
            total_calls += 1
            
        except Exception as e:
            print(f"❌ {test_name}失败: {e}")
            total_calls += 1
    
    print(f"\n📊 测试结果: {successful_calls}/{total_calls} 成功")
    return successful_calls, total_calls

async def main():
    """主函数"""
    print("🔍 深度API调用分析")
    print("=" * 30)
    
    # 1. 检查API密钥有效性
    api_valid, usage = check_api_key_validity()
    
    # 2. 检查免费额度
    check_possible_free_tier()
    
    # 3. 分析缓存
    has_cache = analyze_cache_creation_time()
    
    # 4. 检查本地模型
    using_local = check_local_model_usage()
    
    # 5. 检查端点路由
    check_api_endpoint_routing()
    
    # 6. 检查计费配置
    check_billing_configuration()
    
    # 7. 综合测试
    success_count, total_count = await comprehensive_api_test()
    
    # 总结分析
    print("\n" + "=" * 50)
    print("🎯 深度分析结论")
    
    if not api_valid:
        print("❌ 主要问题: API密钥无效或端点错误")
    elif using_local:
        print("⚠️ 主要问题: 可能在使用本地模型")
    elif success_count == total_count and not usage:
        print("🆓 最可能原因: 在免费试用期或有免费额度")
        print("💡 建议: 检查阿里云控制台的免费额度使用情况")
    elif has_cache:
        print("🗄️ 可能原因: 所有调用都被缓存了")
        print("💡 建议: 清空缓存后重新测试")
    else:
        print("❓ 需要进一步调查")
    
    print("\n🔍 下一步行动:")
    print("1. 登录阿里云控制台查看百炼服务用量")
    print("2. 检查账户余额和付费方式")
    print("3. 查看API调用日志和计费记录")
    print("4. 如果确实没有计费，联系阿里云技术支持")

if __name__ == "__main__":
    asyncio.run(main())
