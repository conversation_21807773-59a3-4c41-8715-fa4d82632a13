#!/usr/bin/env python
"""
调试交通方式检测问题
"""

import asyncio
from map_rag_integration import MapRAGIntegration

async def debug_transport_detection():
    """调试交通方式检测"""
    
    map_integration = MapRAGIntegration()
    
    test_queries = [
        "步行从王府井到北京西站",
        "步行从王府井到北京西站怎么走",
        "开车从王府井到北京西站",
        "开车从王府井到北京西站怎么走",
        "从王府井到北京西站怎么走",
    ]
    
    print("🔍 调试交通方式检测")
    print("=" * 60)
    
    for query in test_queries:
        print(f"\n🔍 测试查询: '{query}'")
        print("-" * 40)
        
        # 1. 检测交通方式
        transport_mode = map_integration._detect_transport_mode(query)
        print(f"🚗 检测到的交通方式: {transport_mode}")
        
        # 2. 测试完整的路线规划
        try:
            result = await map_integration._handle_route_query_with_data(query, "北京")
            
            if result and result.get('map_data'):
                map_data = result['map_data']
                route_info = map_data.get('route_info', {})
                
                print(f"✅ 路线规划成功:")
                print(f"   🚗 实际交通方式: {route_info.get('transport_mode', 'unknown')}")
                print(f"   📏 距离: {route_info.get('distance', 'N/A')}")
                print(f"   ⏱️ 时间: {route_info.get('duration', 'N/A')}")
                print(f"   🗺️ 路径点数: {len(map_data.get('route_path', []))}")
                
                # 检查是否真的调用了不同的API
                if route_info.get('transport_mode') == 'walking':
                    print(f"   👟 步行路线特征: 无过路费")
                elif route_info.get('transport_mode') == 'driving':
                    print(f"   🚗 驾车路线特征: 过路费 {route_info.get('tolls', 'N/A')}")
                    
            else:
                print("❌ 路线规划失败")
                
        except Exception as e:
            print(f"❌ 路线规划异常: {e}")

async def test_api_calls():
    """测试API调用差异"""
    
    print("\n🔍 测试API调用差异")
    print("=" * 60)
    
    from amap_api import AmapAPI
    
    async with AmapAPI() as amap:
        origin_coord = "116.41122,39.915458"  # 王府井
        dest_coord = "116.322056,39.893312"   # 北京西站
        
        print(f"📍 起点: 王府井 ({origin_coord})")
        print(f"📍 终点: 北京西站 ({dest_coord})")
        
        # 测试驾车路线
        print(f"\n🚗 测试驾车路线:")
        driving_result = await amap.driving_route(origin_coord, dest_coord)
        if driving_result:
            print(f"   📏 距离: {driving_result.distance}")
            print(f"   ⏱️ 时间: {driving_result.duration}")
            print(f"   💰 费用: {driving_result.tolls}")
            print(f"   🚦 红绿灯: {driving_result.traffic_lights}")
            print(f"   🗺️ Polyline长度: {len(driving_result.polyline)}")
        else:
            print("   ❌ 驾车路线规划失败")
        
        # 测试步行路线
        print(f"\n🚶 测试步行路线:")
        walking_result = await amap.walking_route(origin_coord, dest_coord)
        if walking_result:
            print(f"   📏 距离: {walking_result.distance}")
            print(f"   ⏱️ 时间: {walking_result.duration}")
            print(f"   💰 费用: {walking_result.tolls}")
            print(f"   🚦 红绿灯: {walking_result.traffic_lights}")
            print(f"   🗺️ Polyline长度: {len(walking_result.polyline)}")
        else:
            print("   ❌ 步行路线规划失败")
        
        # 比较结果
        if driving_result and walking_result:
            print(f"\n📊 结果比较:")
            print(f"   距离相同: {driving_result.distance == walking_result.distance}")
            print(f"   时间相同: {driving_result.duration == walking_result.duration}")
            print(f"   Polyline相同: {driving_result.polyline == walking_result.polyline}")

if __name__ == "__main__":
    asyncio.run(debug_transport_detection())
    asyncio.run(test_api_calls())
