/**
 * 真实人脸动画引擎
 * 基于MediaPipe面部关键点检测，驱动真实人脸动画
 */
class RealFaceAnimationEngine {
    constructor() {
        // 核心组件
        this.faceMesh = null;
        this.canvas = null;
        this.ctx = null;
        this.image = null;
        this.websocket = null;
        this.recognition = null;
        
        // 面部关键点
        this.landmarks = null;
        this.eyeLandmarks = {
            leftEye: [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246],
            rightEye: [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398]
        };
        // 更精确的嘴部关键点 - 包含完整的嘴唇轮廓
        this.mouthLandmarks = {
            // 外嘴唇轮廓 (更准确的嘴部边界)
            outerLips: [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318],
            // 内嘴唇轮廓 (嘴唇内侧)
            innerLips: [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308],
            // 嘴角关键点
            corners: [61, 291],
            // 上下唇中心点
            upperLip: [13, 82, 18, 313, 406],
            lowerLip: [14, 87, 178, 317, 402],
            // 嘴唇中心线
            centerLine: [12, 15]
        };
        
        // 动画状态
        this.isBlinking = false;
        this.isSpeaking = false;
        this.isListening = false;
        this.blinkProgress = 0;
        this.speechIntensity = 0;
        
        // DOM元素
        this.elements = {
            canvas: document.getElementById('faceCanvas'),
            status: document.getElementById('status'),
            initBtn: document.getElementById('initBtn'),
            blinkBtn: document.getElementById('blinkBtn'),
            speakBtn: document.getElementById('speakBtn'),
            resetBtn: document.getElementById('resetBtn'),
            messagesContainer: document.getElementById('messagesContainer'),
            textInput: document.getElementById('textInput'),
            voiceBtn: document.getElementById('voiceBtn'),
            sendBtn: document.getElementById('sendBtn'),
            loadingIndicator: document.getElementById('loadingIndicator'),
            debugStatus: document.getElementById('debugStatus'),
            debugLandmarks: document.getElementById('debugLandmarks'),
            debugEyes: document.getElementById('debugEyes'),
            debugMouth: document.getElementById('debugMouth')
        };
        
        this.initialize();
    }

    async initialize() {
        try {
            console.log('🎭 初始化真实人脸动画引擎...');
            
            // 设置Canvas
            this.setupCanvas();
            
            // 加载美女图片
            await this.loadBeautyImage();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化WebSocket
            this.initializeWebSocket();

            // 初始化音频上下文
            this.initializeAudioContext();

            // 初始化语音识别
            this.initializeSpeechRecognition();

            this.updateStatus('✅ 引擎已就绪，点击"初始化AI"开始');
            this.updateDebugStatus('已就绪');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.updateStatus('❌ 初始化失败');
        }
    }

    setupCanvas() {
        this.canvas = this.elements.canvas;
        this.ctx = this.canvas.getContext('2d');
        
        // 设置Canvas尺寸
        const container = this.canvas.parentElement;
        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight;
        
        console.log('✅ Canvas设置完成');
    }

    async loadBeautyImage() {
        return new Promise((resolve, reject) => {
            this.image = new Image();
            this.image.crossOrigin = 'anonymous';
            
            this.image.onload = () => {
                console.log('✅ 美女图片加载成功');
                this.drawImage();
                resolve();
            };
            
            this.image.onerror = (error) => {
                console.error('❌ 图片加载失败:', error);
                reject(error);
            };
            
            // 加载图片
            this.image.src = 'my.webp';
        });
    }

    drawImage() {
        if (!this.image || !this.ctx) return;
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 计算图片缩放比例，保持宽高比
        const canvasRatio = this.canvas.width / this.canvas.height;
        const imageRatio = this.image.width / this.image.height;
        
        let drawWidth, drawHeight, offsetX, offsetY;
        
        if (imageRatio > canvasRatio) {
            // 图片更宽，以高度为准
            drawHeight = this.canvas.height;
            drawWidth = drawHeight * imageRatio;
            offsetX = (this.canvas.width - drawWidth) / 2;
            offsetY = 0;
        } else {
            // 图片更高，以宽度为准
            drawWidth = this.canvas.width;
            drawHeight = drawWidth / imageRatio;
            offsetX = 0;
            offsetY = (this.canvas.height - drawHeight) / 2;
        }
        
        // 绘制图片
        this.ctx.drawImage(this.image, offsetX, offsetY, drawWidth, drawHeight);
        
        // 保存绘制参数用于关键点映射
        this.imageDrawParams = { offsetX, offsetY, drawWidth, drawHeight };
    }

    async initializeFaceMesh() {
        try {
            this.updateStatus('🤖 正在初始化AI面部检测...');
            this.updateDebugStatus('初始化中');
            
            // 检查MediaPipe是否加载
            if (typeof FaceMesh === 'undefined') {
                throw new Error('MediaPipe FaceMesh未加载');
            }
            
            // 创建FaceMesh实例
            this.faceMesh = new FaceMesh({
                locateFile: (file) => {
                    return `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`;
                }
            });
            
            // 配置FaceMesh
            this.faceMesh.setOptions({
                maxNumFaces: 1,
                refineLandmarks: true,
                minDetectionConfidence: 0.5,
                minTrackingConfidence: 0.5
            });
            
            // 设置结果回调
            this.faceMesh.onResults((results) => {
                this.onFaceMeshResults(results);
            });
            
            // 处理图片
            await this.faceMesh.send({ image: this.image });
            
            console.log('✅ MediaPipe FaceMesh初始化成功');
            
        } catch (error) {
            console.error('❌ FaceMesh初始化失败:', error);
            this.updateStatus('❌ AI初始化失败: ' + error.message);
            this.updateDebugStatus('初始化失败');
        }
    }

    onFaceMeshResults(results) {
        if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
            this.landmarks = results.multiFaceLandmarks[0];
            
            // 更新调试信息
            this.updateDebugStatus('检测成功');
            this.updateDebugLandmarks(this.landmarks.length);
            
            // 检测眼睛和嘴巴
            this.detectEyesAndMouth();
            
            // 重绘图像和关键点
            this.drawImageWithLandmarks();
            
            this.updateStatus('🎭 AI面部检测成功！可以开始动画');
            
        } else {
            console.warn('⚠️ 未检测到人脸');
            this.updateStatus('⚠️ 未检测到人脸，请确保图片中有清晰的人脸');
            this.updateDebugStatus('未检测到人脸');
        }
    }

    detectEyesAndMouth() {
        if (!this.landmarks) return;

        // 检测眼睛位置
        const leftEyePoints = this.eyeLandmarks.leftEye.map(i => this.landmarks[i]);
        const rightEyePoints = this.eyeLandmarks.rightEye.map(i => this.landmarks[i]);

        // 计算眼睛中心
        const leftEyeCenter = this.calculateCenter(leftEyePoints);
        const rightEyeCenter = this.calculateCenter(rightEyePoints);

        // 精确检测嘴部 - 使用多个关键点集合
        const mouthAnalysis = this.analyzeMouthFeatures();

        // 更新调试信息
        this.updateDebugEyes(`左眼: (${leftEyeCenter.x.toFixed(3)}, ${leftEyeCenter.y.toFixed(3)}), 右眼: (${rightEyeCenter.x.toFixed(3)}, ${rightEyeCenter.y.toFixed(3)})`);
        this.updateDebugMouth(`中心: (${mouthAnalysis.center.x.toFixed(3)}, ${mouthAnalysis.center.y.toFixed(3)}), 质量: ${(mouthAnalysis.quality.score * 100).toFixed(1)}%, 问题: ${mouthAnalysis.quality.issues.length}`);

        // 保存关键点位置
        this.faceFeatures = {
            leftEye: { center: leftEyeCenter, points: leftEyePoints },
            rightEye: { center: rightEyeCenter, points: rightEyePoints },
            mouth: mouthAnalysis
        };
    }

    analyzeMouthFeatures() {
        // 获取各个嘴部关键点集合
        const outerLips = this.mouthLandmarks.outerLips.map(i => this.landmarks[i]);
        const innerLips = this.mouthLandmarks.innerLips.map(i => this.landmarks[i]);
        const corners = this.mouthLandmarks.corners.map(i => this.landmarks[i]);
        const upperLip = this.mouthLandmarks.upperLip.map(i => this.landmarks[i]);
        const lowerLip = this.mouthLandmarks.lowerLip.map(i => this.landmarks[i]);
        const centerLine = this.mouthLandmarks.centerLine.map(i => this.landmarks[i]);

        // 计算嘴部中心 - 使用多种方法取平均
        const outerCenter = this.calculateCenter(outerLips);
        const innerCenter = this.calculateCenter(innerLips);
        const cornerCenter = this.calculateCenter(corners);

        // 加权平均计算最终中心点
        const center = {
            x: (outerCenter.x * 0.4 + innerCenter.x * 0.4 + cornerCenter.x * 0.2),
            y: (outerCenter.y * 0.4 + innerCenter.y * 0.4 + cornerCenter.y * 0.2)
        };

        // 计算嘴部尺寸
        const leftCorner = this.landmarks[this.mouthLandmarks.corners[0]];
        const rightCorner = this.landmarks[this.mouthLandmarks.corners[1]];
        const width = Math.abs(rightCorner.x - leftCorner.x);

        // 计算嘴部高度 - 上下唇距离
        const upperLipCenter = this.calculateCenter(upperLip);
        const lowerLipCenter = this.calculateCenter(lowerLip);
        const height = Math.abs(lowerLipCenter.y - upperLipCenter.y);

        // 计算嘴部开合程度
        const openness = this.calculateMouthOpenness(upperLip, lowerLip);

        // 评估检测质量
        const quality = this.assessMouthDetectionQuality(outerLips, innerLips, corners);

        return {
            center: center,
            width: width,
            height: height,
            openness: openness,
            outerLips: outerLips,
            innerLips: innerLips,
            corners: corners,
            upperLip: upperLip,
            lowerLip: lowerLip,
            centerLine: centerLine,
            leftCorner: leftCorner,
            rightCorner: rightCorner,
            upperLipCenter: upperLipCenter,
            lowerLipCenter: lowerLipCenter,
            quality: quality
        };
    }

    assessMouthDetectionQuality(outerLips, innerLips, corners) {
        // 评估嘴部检测质量的多个指标
        let qualityScore = 1.0;
        let issues = [];

        // 1. 检查嘴角距离是否合理
        const cornerDistance = Math.abs(corners[1].x - corners[0].x);
        if (cornerDistance < 0.02 || cornerDistance > 0.15) {
            qualityScore *= 0.7;
            issues.push('嘴角距离异常');
        }

        // 2. 检查上下唇是否合理分布
        const upperLipY = Math.min(...outerLips.slice(0, 7).map(p => p.y));
        const lowerLipY = Math.max(...outerLips.slice(7).map(p => p.y));
        const lipHeight = lowerLipY - upperLipY;

        if (lipHeight < 0.005 || lipHeight > 0.08) {
            qualityScore *= 0.8;
            issues.push('唇部高度异常');
        }

        // 3. 检查关键点是否过于分散
        const outerBounds = this.calculateBounds(outerLips);
        const aspectRatio = outerBounds.width / outerBounds.height;

        if (aspectRatio < 1.5 || aspectRatio > 6) {
            qualityScore *= 0.6;
            issues.push('嘴部比例异常');
        }

        // 4. 检查内外唇轮廓一致性
        const outerCenter = this.calculateCenter(outerLips);
        const innerCenter = this.calculateCenter(innerLips);
        const centerDistance = Math.sqrt(
            Math.pow(outerCenter.x - innerCenter.x, 2) +
            Math.pow(outerCenter.y - innerCenter.y, 2)
        );

        if (centerDistance > 0.02) {
            qualityScore *= 0.9;
            issues.push('内外唇中心偏差');
        }

        return {
            score: qualityScore,
            issues: issues,
            cornerDistance: cornerDistance,
            lipHeight: lipHeight,
            aspectRatio: aspectRatio,
            centerDistance: centerDistance
        };
    }

    calculateBounds(points) {
        const xs = points.map(p => p.x);
        const ys = points.map(p => p.y);

        return {
            minX: Math.min(...xs),
            maxX: Math.max(...xs),
            minY: Math.min(...ys),
            maxY: Math.max(...ys),
            width: Math.max(...xs) - Math.min(...xs),
            height: Math.max(...ys) - Math.min(...ys)
        };
    }

    calculateMouthOpenness(upperLip, lowerLip) {
        // 计算上下唇之间的平均距离
        const upperCenter = this.calculateCenter(upperLip);
        const lowerCenter = this.calculateCenter(lowerLip);

        // 垂直距离表示开合程度
        const openness = Math.abs(lowerCenter.y - upperCenter.y);

        // 归一化到0-1范围
        return Math.min(openness * 20, 1.0); // 乘以20是经验值，可调整
    }

    calculateCenter(points) {
        const sum = points.reduce((acc, point) => ({
            x: acc.x + point.x,
            y: acc.y + point.y
        }), { x: 0, y: 0 });
        
        return {
            x: sum.x / points.length,
            y: sum.y / points.length
        };
    }

    drawImageWithLandmarks() {
        // 重绘原图
        this.drawImage();

        if (!this.landmarks || !this.faceFeatures) return;

        const { offsetX, offsetY, drawWidth, drawHeight } = this.imageDrawParams;

        // 绘制眼睛关键点 (红色)
        this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
        this.drawFeaturePoints(this.faceFeatures.leftEye.points, offsetX, offsetY, drawWidth, drawHeight, 2);
        this.drawFeaturePoints(this.faceFeatures.rightEye.points, offsetX, offsetY, drawWidth, drawHeight, 2);

        // 绘制嘴部关键点 - 分层显示
        this.drawMouthLandmarks(offsetX, offsetY, drawWidth, drawHeight);

        // 应用动画效果
        this.applyAnimationEffects();
    }

    drawMouthLandmarks(offsetX, offsetY, drawWidth, drawHeight) {
        const mouth = this.faceFeatures.mouth;

        // 外嘴唇轮廓 (绿色)
        this.ctx.fillStyle = 'rgba(0, 255, 0, 0.9)';
        this.drawFeaturePoints(mouth.outerLips, offsetX, offsetY, drawWidth, drawHeight, 3);

        // 内嘴唇轮廓 (蓝色)
        this.ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
        this.drawFeaturePoints(mouth.innerLips, offsetX, offsetY, drawWidth, drawHeight, 2);

        // 嘴角 (黄色，更大)
        this.ctx.fillStyle = 'rgba(255, 255, 0, 1.0)';
        this.drawFeaturePoints(mouth.corners, offsetX, offsetY, drawWidth, drawHeight, 4);

        // 上下唇中心 (紫色)
        this.ctx.fillStyle = 'rgba(255, 0, 255, 0.9)';
        this.drawFeaturePoints([mouth.upperLipCenter, mouth.lowerLipCenter], offsetX, offsetY, drawWidth, drawHeight, 3);

        // 嘴部中心 (白色，最大)
        this.ctx.fillStyle = 'rgba(255, 255, 255, 1.0)';
        const centerX = offsetX + mouth.center.x * drawWidth;
        const centerY = offsetY + mouth.center.y * drawHeight;
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, 5, 0, 2 * Math.PI);
        this.ctx.fill();

        // 绘制嘴部边界框 (调试用)
        this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.5)';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(
            centerX - mouth.width * drawWidth / 2,
            centerY - mouth.height * drawHeight / 2,
            mouth.width * drawWidth,
            mouth.height * drawHeight
        );

        // 显示开合程度
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.font = '12px Arial';
        this.ctx.fillText(
            `开合: ${(mouth.openness * 100).toFixed(1)}%`,
            centerX + 20,
            centerY - 10
        );
    }

    drawFeaturePoints(points, offsetX, offsetY, drawWidth, drawHeight, radius = 2) {
        points.forEach(point => {
            const x = offsetX + point.x * drawWidth;
            const y = offsetY + point.y * drawHeight;

            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
            this.ctx.fill();
        });
    }

    applyAnimationEffects() {
        if (!this.faceFeatures) return;
        
        const { offsetX, offsetY, drawWidth, drawHeight } = this.imageDrawParams;
        
        // 眨眼效果
        if (this.isBlinking) {
            this.applyBlinkEffect(offsetX, offsetY, drawWidth, drawHeight);
        }
        
        // 说话效果
        if (this.isSpeaking) {
            this.applySpeechEffect(offsetX, offsetY, drawWidth, drawHeight);
        }
    }

    applyBlinkEffect(offsetX, offsetY, drawWidth, drawHeight) {
        // 在眼睛位置绘制眨眼效果
        this.ctx.fillStyle = `rgba(255, 192, 203, ${this.blinkProgress})`;
        
        // 左眼
        const leftEyeX = offsetX + this.faceFeatures.leftEye.center.x * drawWidth;
        const leftEyeY = offsetY + this.faceFeatures.leftEye.center.y * drawHeight;
        this.ctx.fillRect(leftEyeX - 15, leftEyeY - 3, 30, 6);
        
        // 右眼
        const rightEyeX = offsetX + this.faceFeatures.rightEye.center.x * drawWidth;
        const rightEyeY = offsetY + this.faceFeatures.rightEye.center.y * drawHeight;
        this.ctx.fillRect(rightEyeX - 15, rightEyeY - 3, 30, 6);
    }

    applySpeechEffect(offsetX, offsetY, drawWidth, drawHeight) {
        const mouth = this.faceFeatures.mouth;

        // 基于真实嘴部尺寸的说话效果
        const baseWidth = mouth.width * drawWidth;
        const baseHeight = mouth.height * drawHeight;

        // 动态调整嘴部开合
        const animatedWidth = baseWidth * (1 + this.speechIntensity * 0.3);
        const animatedHeight = baseHeight * (1 + this.speechIntensity * 0.8);

        const mouthX = offsetX + mouth.center.x * drawWidth;
        const mouthY = offsetY + mouth.center.y * drawHeight;

        // 绘制动态嘴形
        this.ctx.fillStyle = `rgba(255, 100, 100, ${this.speechIntensity * 0.6})`;

        // 椭圆形嘴形
        this.ctx.beginPath();
        this.ctx.ellipse(
            mouthX,
            mouthY,
            animatedWidth / 2,
            animatedHeight / 2,
            0, 0, 2 * Math.PI
        );
        this.ctx.fill();

        // 添加嘴唇高光效果
        this.ctx.fillStyle = `rgba(255, 200, 200, ${this.speechIntensity * 0.4})`;
        this.ctx.beginPath();
        this.ctx.ellipse(
            mouthX,
            mouthY - animatedHeight * 0.2,
            animatedWidth * 0.8 / 2,
            animatedHeight * 0.3 / 2,
            0, 0, 2 * Math.PI
        );
        this.ctx.fill();

        // 绘制说话强度指示器
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.font = '10px Arial';
        this.ctx.fillText(
            `说话强度: ${(this.speechIntensity * 100).toFixed(0)}%`,
            mouthX + animatedWidth / 2 + 10,
            mouthY
        );
    }

    async animateBlink() {
        if (this.isBlinking) return;
        
        this.isBlinking = true;
        this.blinkProgress = 0;
        
        // 眨眼动画
        const blinkDuration = 300; // 300ms
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / blinkDuration;
            
            if (progress < 0.5) {
                this.blinkProgress = progress * 2; // 0 -> 1
            } else {
                this.blinkProgress = 2 - progress * 2; // 1 -> 0
            }
            
            this.drawImageWithLandmarks();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.isBlinking = false;
                this.blinkProgress = 0;
                this.drawImageWithLandmarks();
            }
        };
        
        animate();
    }

    startSpeechAnimation() {
        if (this.isSpeaking) return;

        this.isSpeaking = true;
        this.speechIntensity = 0;
        this.speechPhase = 0;

        // 更自然的说话动画 - 结合周期性和随机性
        this.speechAnimationInterval = setInterval(() => {
            // 基础周期性变化
            this.speechPhase += 0.3;
            const baseIntensity = (Math.sin(this.speechPhase) + 1) / 2; // 0-1

            // 添加随机变化模拟真实说话
            const randomVariation = Math.random() * 0.4 - 0.2; // -0.2 到 0.2

            // 组合强度
            this.speechIntensity = Math.max(0.1, Math.min(1.0,
                baseIntensity * 0.7 + 0.3 + randomVariation
            ));

            this.drawImageWithLandmarks();
        }, 80); // 更快的更新频率，更流畅
    }

    stopSpeechAnimation() {
        this.isSpeaking = false;
        this.speechIntensity = 0;
        
        if (this.speechAnimationInterval) {
            clearInterval(this.speechAnimationInterval);
            this.speechAnimationInterval = null;
        }
        
        this.drawImageWithLandmarks();
    }

    setupEventListeners() {
        // 初始化AI按钮
        this.elements.initBtn.addEventListener('click', () => {
            this.initializeFaceMesh();
        });
        
        // 眨眼测试按钮
        this.elements.blinkBtn.addEventListener('click', () => {
            if (this.landmarks) {
                this.animateBlink();
            } else {
                alert('请先初始化AI面部检测');
            }
        });
        
        // 说话测试按钮
        this.elements.speakBtn.addEventListener('click', () => {
            if (this.landmarks) {
                if (this.isSpeaking) {
                    this.stopSpeechAnimation();
                    this.elements.speakBtn.textContent = '💋 说话动画';
                } else {
                    this.startSpeechAnimation();
                    this.elements.speakBtn.textContent = '🔇 停止说话';
                }
            } else {
                alert('请先初始化AI面部检测');
            }
        });
        
        // 重置按钮
        this.elements.resetBtn.addEventListener('click', () => {
            this.reset();
        });
        
        // 语音按钮
        this.elements.voiceBtn.addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // 发送按钮
        this.elements.sendBtn.addEventListener('click', () => {
            const text = this.elements.textInput.value.trim();
            if (text) {
                this.sendQuery(text);
                this.elements.textInput.value = '';
            }
        });
        
        // 回车发送
        this.elements.textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.elements.sendBtn.click();
            }
        });
        
        // 输入框焦点处理
        this.elements.textInput.addEventListener('focus', () => {
            if (this.landmarks) {
                this.updateStatus('💭 等待您的问题...');
            }
        });

        this.elements.textInput.addEventListener('blur', () => {
            if (!this.isListening && !this.isSpeaking) {
                if (this.landmarks) {
                    this.updateStatus('🎭 AI面部检测成功！可以开始动画');
                } else {
                    this.updateStatus('✅ 引擎已就绪，点击"初始化AI"开始');
                }
            }
        });

        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.setupCanvas();
            this.drawImage();
        });
    }

    initializeAudioContext() {
        this.audioContextInitialized = false;

        const initAudio = () => {
            if (!this.audioContextInitialized) {
                try {
                    const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    silentAudio.volume = 0;
                    silentAudio.play().then(() => {
                        this.audioContextInitialized = true;
                        console.log('✅ 音频上下文已初始化');
                        document.removeEventListener('click', initAudio);
                        document.removeEventListener('touchstart', initAudio);
                    }).catch(() => {
                        console.log('⚠️ 音频上下文初始化失败');
                    });
                } catch (error) {
                    console.log('⚠️ 音频上下文初始化异常:', error);
                }
            }
        };

        document.addEventListener('click', initAudio);
        document.addEventListener('touchstart', initAudio);

        console.log('🎤 音频上下文初始化监听器已设置');
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();

            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'zh-CN';

            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateVoiceButton(true);
                this.updateStatus('👂 正在听取您的问题...');
                console.log('🎤 语音识别开始');
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                console.log('🎤 识别结果:', transcript);
                this.elements.textInput.value = transcript;

                // 自动发送识别的文本
                setTimeout(() => {
                    this.sendQuery(transcript);
                }, 500);
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceButton(false);
                if (this.landmarks) {
                    this.updateStatus('🎭 AI面部检测成功！可以开始动画');
                } else {
                    this.updateStatus('✅ 引擎已就绪，点击"初始化AI"开始');
                }
                console.log('🎤 语音识别结束');
            };

            this.recognition.onerror = (event) => {
                console.error('❌ 语音识别错误:', event.error);
                this.isListening = false;
                this.updateVoiceButton(false);
                this.updateStatus('❌ 语音识别失败: ' + event.error);

                if (event.error === 'not-allowed') {
                    alert('请允许麦克风权限以使用语音输入功能');
                } else if (event.error === 'no-speech') {
                    alert('没有检测到语音，请重试');
                } else if (event.error === 'network') {
                    alert('网络错误，请检查网络连接');
                }
            };

            console.log('✅ 语音识别初始化成功');
        } else {
            console.warn('⚠️ 浏览器不支持语音识别');
        }
    }

    toggleVoiceInput() {
        if (!this.recognition) {
            alert('您的浏览器不支持语音识别功能');
            return;
        }

        if (this.isListening) {
            this.recognition.stop();
            console.log('🛑 停止语音识别');
        } else {
            try {
                this.recognition.start();
                console.log('🎤 开始语音识别');
            } catch (error) {
                console.error('❌ 启动语音识别失败:', error);
                alert('启动语音识别失败，请检查麦克风权限');
            }
        }
    }

    updateVoiceButton(isRecording) {
        if (isRecording) {
            this.elements.voiceBtn.classList.add('recording');
            this.elements.voiceBtn.innerHTML = '🔴 停止';
            this.elements.voiceBtn.title = '点击停止录音';
        } else {
            this.elements.voiceBtn.classList.remove('recording');
            this.elements.voiceBtn.innerHTML = '🎤';
            this.elements.voiceBtn.title = '点击开始语音输入';
        }
    }

    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('✅ WebSocket连接成功');
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleServerMessage(data);
            } catch (error) {
                console.error('❌ 消息解析失败:', error);
            }
        };

        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
        };

        this.websocket.onclose = () => {
            console.log('🔌 WebSocket连接关闭');
        };
    }

    sendQuery(text) {
        if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
            alert('未连接到服务器');
            return;
        }

        this.addMessage('user', text);
        this.showLoading(true);
        this.updateStatus('🧠 正在思考...');

        // 设置超时处理
        const queryTimeout = setTimeout(() => {
            console.warn('⚠️ 查询超时');
            this.showLoading(false);
            this.updateStatus('⏰ 查询超时，请重试');
            this.addMessage('system', '查询超时，请尝试更简短的问题或重新提问');
        }, 30000);

        const message = {
            type: 'query',
            text: text.trim(),
            timestamp: Date.now()
        };

        try {
            this.websocket.send(JSON.stringify(message));
            console.log('📤 发送查询:', text);
            this.currentQueryTimeout = queryTimeout;
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
            clearTimeout(queryTimeout);
            this.showLoading(false);
            this.updateStatus('❌ 发送失败');
        }
    }

    handleServerMessage(data) {
        if (data.type === 'response') {
            // 清除查询超时
            if (this.currentQueryTimeout) {
                clearTimeout(this.currentQueryTimeout);
                this.currentQueryTimeout = null;
            }

            this.showLoading(false);
            this.addMessage('assistant', data.text);

            // 播放语音并同步面部动画
            if (data.audio && data.audio_type === 'edge-tts') {
                console.log('🎤 检测到Edge-TTS音频，开始播放');
                this.playEdgeTTSAudio(data.audio, data.text);
            } else {
                console.log('🔄 使用浏览器TTS播放');
                this.speakResponse(data.text, data.language || 'zh-CN');
            }
        } else if (data.type === 'error') {
            // 清除查询超时
            if (this.currentQueryTimeout) {
                clearTimeout(this.currentQueryTimeout);
                this.currentQueryTimeout = null;
            }

            this.showLoading(false);
            this.updateStatus('❌ 处理失败');
            this.addMessage('system', `错误: ${data.message || '未知错误'}`);
            console.error('❌ 服务器错误:', data);
        }
    }

    playEdgeTTSAudio(audioBase64, text) {
        try {
            console.log('🎤 开始播放Edge-TTS音频');

            const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;
            const audio = new Audio();
            audio.preload = 'auto';
            audio.volume = 0.9;

            audio.addEventListener('loadstart', () => {
                if (this.landmarks) {
                    this.startSpeechAnimation();
                }
                this.updateStatus('🎤 正在播放语音...');
            });

            audio.addEventListener('play', () => {
                console.log('🎤 音频开始播放');
            });

            audio.addEventListener('ended', () => {
                console.log('✅ Edge-TTS语音播放完成');
                if (this.landmarks) {
                    this.stopSpeechAnimation();
                }
                this.updateStatus('🎭 AI面部检测成功！可以开始动画');
            });

            audio.addEventListener('error', (event) => {
                console.error('❌ 音频播放失败:', event);
                if (this.landmarks) {
                    this.stopSpeechAnimation();
                }
                this.updateStatus('❌ 语音播放失败');

                // 降级到浏览器TTS
                setTimeout(() => {
                    this.speakResponse(text, 'zh-CN');
                }, 500);
            });

            audio.src = audioDataUrl;

            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('✅ 音频播放启动成功');
                }).catch(error => {
                    console.error('❌ 音频播放启动失败:', error);
                    this.updateStatus('请点击页面后重试');
                    setTimeout(() => {
                        this.speakResponse(text, 'zh-CN');
                    }, 1000);
                });
            }

        } catch (error) {
            console.error('❌ Edge-TTS音频处理失败:', error);
            this.speakResponse(text, 'zh-CN');
        }
    }

    speakResponse(text, language = 'zh-CN') {
        if (!window.speechSynthesis) {
            console.warn('⚠️ 浏览器不支持语音合成');
            return;
        }

        window.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language;
        utterance.rate = 0.85;
        utterance.pitch = 1.1;
        utterance.volume = 0.9;

        utterance.onstart = () => {
            if (this.landmarks) {
                this.startSpeechAnimation();
            }
            this.updateStatus('🎤 正在播放语音...');
        };

        utterance.onend = () => {
            if (this.landmarks) {
                this.stopSpeechAnimation();
            }
            this.updateStatus('🎭 AI面部检测成功！可以开始动画');
        };

        utterance.onerror = (event) => {
            console.error('❌ 语音合成错误:', event);
            if (this.landmarks) {
                this.stopSpeechAnimation();
            }
            this.updateStatus('❌ 语音播放失败');
        };

        window.speechSynthesis.speak(utterance);
    }

    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const roleText = role === 'user' ? '您' : 'AI助手';
        messageDiv.innerHTML = `
            <div class="message-content">
                <strong>${roleText}:</strong><br>
                ${content.replace(/\n/g, '<br>')}
            </div>
        `;

        this.elements.messagesContainer.appendChild(messageDiv);
        this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
    }

    showLoading(show) {
        if (show) {
            this.elements.loadingIndicator.classList.add('show');
        } else {
            this.elements.loadingIndicator.classList.remove('show');
        }
    }

    reset() {
        this.landmarks = null;
        this.faceFeatures = null;
        this.isBlinking = false;
        this.isSpeaking = false;
        
        if (this.speechAnimationInterval) {
            clearInterval(this.speechAnimationInterval);
        }
        
        this.drawImage();
        this.updateStatus('🔄 已重置，点击"初始化AI"重新开始');
        this.updateDebugStatus('已重置');
        this.updateDebugLandmarks(0);
        this.updateDebugEyes('未检测');
        this.updateDebugMouth('未检测');
    }

    updateStatus(message) {
        this.elements.status.textContent = message;
    }

    updateDebugStatus(status) {
        this.elements.debugStatus.textContent = status;
    }

    updateDebugLandmarks(count) {
        this.elements.debugLandmarks.textContent = count;
    }

    updateDebugEyes(info) {
        this.elements.debugEyes.textContent = info;
    }

    updateDebugMouth(info) {
        this.elements.debugMouth.textContent = info;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎭 初始化真实人脸动画引擎...');
    window.realFaceEngine = new RealFaceAnimationEngine();
});
