#!/bin/bash

# RAGAnything 项目上传脚本
# 使用方法: bash upload_project.sh

set -e

# 服务器信息
SERVER_HOST="fvcdxpgy4r6hwa0usnow.deepln.com"
SERVER_PORT="54953"
SERVER_USER="root"
SERVER_PASS="Xsf1e2HyeGgJBWj6pS79CqZausXSwFOg"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v scp &> /dev/null; then
        log_error "scp 未安装，请安装 openssh-client"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        log_error "ssh 未安装，请安装 openssh-client"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 准备项目文件
prepare_project() {
    log_info "准备项目文件..."
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    PROJECT_DIR="$TEMP_DIR/RAG-Anything"
    
    # 复制项目文件
    cp -r . "$PROJECT_DIR"
    
    # 清理不需要的文件
    cd "$PROJECT_DIR"
    rm -rf .git
    rm -rf __pycache__
    rm -rf *.pyc
    rm -rf .DS_Store
    rm -rf node_modules
    rm -rf venv
    rm -rf .env
    
    # 创建requirements.txt
    cat > requirements.txt << EOF
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
transformers>=4.30.0
openai>=1.0.0
dashscope>=1.14.0
magic-pdf>=0.7.0
pymupdf>=1.23.0
pillow>=9.0.0
numpy>=1.21.0
pandas>=1.5.0
networkx>=3.0
faiss-cpu>=1.7.0
fastapi>=0.100.0
uvicorn>=0.20.0
streamlit>=1.28.0
requests>=2.28.0
python-dotenv>=1.0.0
lightrag>=0.1.0
EOF
    
    log_info "项目文件准备完成: $PROJECT_DIR"
    echo "$PROJECT_DIR"
}

# 上传部署脚本
upload_deploy_script() {
    log_info "上传部署脚本..."
    
    # 使用sshpass上传部署脚本
    scp -P $SERVER_PORT -o StrictHostKeyChecking=no \
        deploy.sh \
        $SERVER_USER@$SERVER_HOST:/tmp/deploy.sh
    
    log_info "部署脚本上传完成"
}

# 执行部署
execute_deployment() {
    log_info "执行服务器部署..."
    
    ssh -p $SERVER_PORT -o StrictHostKeyChecking=no \
        $SERVER_USER@$SERVER_HOST << 'EOF'
        
        # 执行部署脚本
        chmod +x /tmp/deploy.sh
        bash /tmp/deploy.sh
        
EOF
    
    log_info "服务器部署完成"
}

# 上传项目代码
upload_project_code() {
    local project_dir=$1
    
    log_info "上传项目代码..."
    
    # 压缩项目
    cd $(dirname $project_dir)
    tar -czf RAG-Anything.tar.gz RAG-Anything/
    
    # 上传压缩包
    scp -P $SERVER_PORT -o StrictHostKeyChecking=no \
        RAG-Anything.tar.gz \
        $SERVER_USER@$SERVER_HOST:/tmp/
    
    # 在服务器上解压
    ssh -p $SERVER_PORT -o StrictHostKeyChecking=no \
        $SERVER_USER@$SERVER_HOST << 'EOF'
        
        # 解压项目代码
        cd /opt/raganything
        tar -xzf /tmp/RAG-Anything.tar.gz
        
        # 设置权限
        chown -R root:root RAG-Anything/
        chmod -R 755 RAG-Anything/
        
        # 安装项目依赖
        source venv/bin/activate
        cd RAG-Anything
        pip install -r requirements.txt
        
        # 清理临时文件
        rm -f /tmp/RAG-Anything.tar.gz
        
EOF
    
    log_info "项目代码上传完成"
}

# 配置项目
configure_project() {
    log_info "配置项目..."
    
    ssh -p $SERVER_PORT -o StrictHostKeyChecking=no \
        $SERVER_USER@$SERVER_HOST << 'EOF'
        
        # 更新Web服务以集成RAGAnything
        cat > /opt/raganything/web_server.py << 'WEBEOF'
#!/usr/bin/env python3
"""
RAGAnything Web服务
"""

import os
import sys
import asyncio
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# 添加项目路径
sys.path.append('/opt/raganything/RAG-Anything')

# 加载环境变量
load_dotenv("/opt/raganything/.env")

app = FastAPI(title="RAGAnything API", version="1.0.0")

# 添加CORS支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "RAGAnything API Server", 
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "raganything"}

@app.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """上传文档并处理"""
    try:
        upload_dir = "/opt/raganything/uploads"
        file_path = os.path.join(upload_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        return {"message": "文件上传成功", "file_path": file_path}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process")
async def process_document(file_path: str, working_dir: str = None):
    """处理文档"""
    try:
        # 这里集成RAGAnything处理逻辑
        if not working_dir:
            working_dir = "/opt/raganything/data"
        
        # TODO: 集成实际的RAGAnything处理逻辑
        return {
            "message": "文档处理完成", 
            "file_path": file_path,
            "working_dir": working_dir
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query")
async def query_document(query: str, working_dir: str = None):
    """查询文档"""
    try:
        if not working_dir:
            working_dir = "/opt/raganything/data"
        
        # TODO: 集成实际的RAGAnything查询逻辑
        response = f"模拟回答: {query}"
        return {"query": query, "response": response}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    
    uvicorn.run(
        "web_server:app",
        host=host,
        port=port,
        reload=False,
        workers=1
    )
WEBEOF
        
        # 重启服务
        systemctl restart raganything
        
EOF
    
    log_info "项目配置完成"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 获取服务器IP
    SERVER_IP=$(ssh -p $SERVER_PORT -o StrictHostKeyChecking=no \
        $SERVER_USER@$SERVER_HOST "curl -s ifconfig.me")
    
    # 测试健康检查
    if curl -s "http://$SERVER_IP/health" > /dev/null; then
        log_info "✅ 服务部署成功"
        echo "🌐 访问地址: http://$SERVER_IP"
        echo "🔍 健康检查: http://$SERVER_IP/health"
    else
        log_error "❌ 服务部署失败"
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    echo "🎉 RAGAnything 项目部署完成！"
    echo "=================================="
    echo ""
    echo "📋 API端点:"
    echo "  GET  /          - 服务信息"
    echo "  GET  /health    - 健康检查"
    echo "  POST /upload    - 上传文档"
    echo "  POST /process   - 处理文档"
    echo "  POST /query     - 查询文档"
    echo ""
    echo "🔧 服务管理:"
    echo "  查看状态: ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST 'systemctl status raganything'"
    echo "  查看日志: ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST 'journalctl -u raganything -f'"
    echo "  重启服务: ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST 'systemctl restart raganything'"
    echo ""
    echo "📁 项目路径: /opt/raganything/RAG-Anything"
    echo "⚙️  配置文件: /opt/raganything/.env"
    echo ""
}

# 主函数
main() {
    log_info "开始RAGAnything项目部署"
    
    check_dependencies
    
    # 准备项目
    PROJECT_DIR=$(prepare_project)
    
    # 上传部署脚本并执行
    upload_deploy_script
    execute_deployment
    
    # 上传项目代码
    upload_project_code "$PROJECT_DIR"
    
    # 配置项目
    configure_project
    
    # 测试部署
    test_deployment
    
    # 清理临时文件
    rm -rf $(dirname "$PROJECT_DIR")
    
    # 显示使用说明
    show_usage
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
