<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-Anything项目完整技术流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .description {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .mermaid {
            text-align: center;
            margin: 30px 0;
        }
        .tech-summary {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .tech-summary h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .tech-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
        }
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: #e74c3c;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 RAG-Anything项目完整技术流程图</h1>
        
        <div class="description">
            <h3>📋 项目概述</h3>
            <p>RAG-Anything是一个全能型多模态RAG智能问答系统，集成了文档解析、知识图谱、空间智能和多模态理解等先进技术。该系统支持17种文档格式，实现了从文档输入到智能答案输出的完整技术链路。</p>
        </div>

        <div class="mermaid">
graph TD
    A[📄 文档输入] --> B{文件格式检测}
    
    B -->|.txt/.md| C[📝 TXT处理器]
    B -->|.doc/.docx/.ppt/.xlsx| D[📊 Office处理器]
    B -->|.jpg/.png/.pdf| E[📑 PDF/图像处理器]
    
    C --> C1[编码检测算法]
    C1 --> C2[ReportLab PDF转换]
    C2 --> F[MinerU解析引擎]
    
    D --> D1[LibreOffice可用性检测]
    D1 --> D2[LibreOffice PDF转换]
    D2 --> F
    
    E --> F[MinerU解析引擎]
    
    F --> F1[🤖 布局分析模型<br/>CNN+Transformer]
    F1 --> F2[🎯 目标检测算法<br/>YOLO/R-CNN]
    F2 --> F3[📋 内容分类器]
    
    F3 --> G1[📝 OCR文字识别<br/>CRNN+Transformer]
    F3 --> G2[🖼️ 图像提取算法]
    F3 --> G3[📊 表格识别模型<br/>结构分析+单元格分割]
    F3 --> G4[🧮 公式识别模型<br/>符号识别+LaTeX转换]
    
    G1 --> H[📋 content_list生成]
    G2 --> H
    G3 --> H
    G4 --> H
    
    H --> I[🔄 内容分离算法]
    I --> I1[📝 纯文本内容]
    I --> I2[🎨 多模态内容]
    I --> I3[🗺️ 地理信息]
    
    I1 --> J[🧠 LightRAG知识图谱构建]
    J --> J1[🏷️ NER实体识别<br/>BERT序列标注]
    J1 --> J2[🔗 关系抽取算法<br/>Transformer分类器]
    J2 --> J3[📊 图嵌入算法<br/>TransE/ComplEx]
    
    I2 --> K[🎨 多模态处理器]
    K --> K1[👁️ 视觉理解模型<br/>Vision Transformer]
    K1 --> K2[📝 图像描述生成<br/>VL-BERT/CLIP]
    K2 --> K3[🔍 上下文提取算法]
    
    I3 --> L[🗺️ 空间信息处理]
    L --> L1[🎯 地理查询路由<br/>关键词匹配+语义分析]
    L1 --> L2[🌍 高德地图API<br/>POI搜索/路线规划]
    L2 --> L3[📍 地理编码算法]
    
    J3 --> M[📊 向量化存储]
    K3 --> M
    L3 --> M
    
    M --> M1[🔤 文本嵌入模型<br/>BERT/RoBERTa Encoder]
    M1 --> M2[🗄️ 向量数据库<br/>FAISS索引]
    
    N[❓ 用户查询] --> O{查询类型识别}
    O -->|文本查询| P1[🔍 Local检索<br/>余弦相似度]
    O -->|复杂查询| P2[🌐 Global检索<br/>图遍历算法BFS]
    O -->|混合查询| P3[🔄 Hybrid检索<br/>加权融合算法]
    O -->|地理查询| P4[🗺️ 空间检索<br/>地理路由+API调用]
    
    M2 --> P1
    M2 --> P2
    M2 --> P3
    M2 --> P4
    
    P1 --> Q[🔗 结果融合算法]
    P2 --> Q
    P3 --> Q
    P4 --> Q
    
    Q --> R[🧠 LLM答案生成]
    R --> R1[🤖 通义千问模型<br/>Transformer Decoder]
    R1 --> R2[📝 提示工程优化]
    R2 --> S[✅ 最终答案输出]
    
    T[🌐 Web界面] --> T1[⚡ FastAPI服务器]
    T1 --> T2[🔌 WebSocket实时通信]
    T2 --> T3[🎤 语音交互<br/>Whisper+Edge-TTS]
    T3 --> T4[👤 3D数字人渲染]
    
    S --> T2
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style J fill:#e8f5e8
    style K fill:#fff3e0
    style L fill:#fce4ec
    style R1 fill:#e3f2fd
    style S fill:#e8f5e8
        </div>

        <div class="tech-summary">
            <h3>🔧 核心技术组件</h3>
            <div class="tech-list">
                <div class="tech-item">
                    <h4>📄 文档解析技术</h4>
                    <p>MinerU多模态解析引擎，支持17种格式，集成CNN+Transformer布局分析、YOLO目标检测、OCR文字识别等先进算法。</p>
                </div>
                <div class="tech-item">
                    <h4>🧠 知识图谱构建</h4>
                    <p>基于LightRAG框架，使用BERT实体识别、Transformer关系抽取、TransE图嵌入等技术构建智能知识图谱。</p>
                </div>
                <div class="tech-item">
                    <h4>🎨 多模态处理</h4>
                    <p>Vision Transformer视觉理解、VL-BERT图像描述生成、智能上下文提取，实现多模态内容的深度理解。</p>
                </div>
                <div class="tech-item">
                    <h4>🗺️ 空间智能集成</h4>
                    <p>高德地图API深度集成，地理查询智能路由，POI搜索、路线规划、地理编码等空间服务。</p>
                </div>
                <div class="tech-item">
                    <h4>🔍 智能检索系统</h4>
                    <p>Local/Global/Hybrid/空间四种检索策略，FAISS向量索引、图遍历算法、加权融合等技术。</p>
                </div>
                <div class="tech-item">
                    <h4>🤖 LLM答案生成</h4>
                    <p>通义千问Transformer Decoder模型，提示工程优化，支持长上下文理解和高质量答案生成。</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>📊 RAG-Anything项目技术流程图 | 生成时间：2025年1月</p>
            <p>🚀 全能型多模态RAG智能问答系统 - 从文档到智慧的完整技术链路</p>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
