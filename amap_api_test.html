<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图API功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .api-section h3 {
            color: #007AFF;
            margin-top: 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: linear-gradient(45deg, #007AFF, #0056CC);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-top: 10px;
        }
        
        button:hover {
            background: linear-gradient(45deg, #0056CC, #003D99);
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 5px;
            border-left: 4px solid #007AFF;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .error {
            background: #ffe8e8;
            border-left-color: #ff4444;
            color: #cc0000;
        }
        
        .success {
            background: #e8ffe8;
            border-left-color: #44ff44;
            color: #006600;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 高德地图API功能测试</h1>
        
        <div class="grid">
            <!-- 周边搜索 -->
            <div class="api-section">
                <h3>🔍 周边搜索</h3>
                <div class="form-group">
                    <label>位置:</label>
                    <input type="text" id="search-location" placeholder="北京市朝阳区" value="北京市朝阳区">
                </div>
                <div class="form-group">
                    <label>关键词:</label>
                    <input type="text" id="search-keywords" placeholder="餐厅" value="餐厅">
                </div>
                <div class="form-group">
                    <label>搜索半径(米):</label>
                    <input type="number" id="search-radius" value="1000" min="1" max="50000">
                </div>
                <button onclick="testSearchAround()">搜索周边</button>
                <div id="search-result" class="result" style="display:none;"></div>
            </div>
            
            <!-- 天气查询 -->
            <div class="api-section">
                <h3>🌤️ 天气查询</h3>
                <div class="form-group">
                    <label>城市:</label>
                    <input type="text" id="weather-city" placeholder="北京" value="北京">
                </div>
                <div class="form-group">
                    <label>查询类型:</label>
                    <select id="weather-extensions">
                        <option value="base">实况天气</option>
                        <option value="all">预报天气</option>
                    </select>
                </div>
                <button onclick="testWeather()">查询天气</button>
                <div id="weather-result" class="result" style="display:none;"></div>
            </div>
        </div>
        
        <!-- 路径规划 -->
        <div class="api-section">
            <h3>🛣️ 路径规划</h3>
            <div class="grid">
                <div class="form-group">
                    <label>起点:</label>
                    <input type="text" id="route-origin" placeholder="北京市朝阳区" value="北京市朝阳区">
                </div>
                <div class="form-group">
                    <label>终点:</label>
                    <input type="text" id="route-destination" placeholder="故宫" value="故宫">
                </div>
            </div>
            <div class="form-group">
                <label>路径类型:</label>
                <select id="route-type">
                    <option value="driving">驾车</option>
                    <option value="walking">步行</option>
                    <option value="bicycling">骑行</option>
                    <option value="transit">公交</option>
                </select>
            </div>
            <button onclick="testRoute()">规划路径</button>
            <div id="route-result" class="result" style="display:none;"></div>
        </div>
        
        <!-- 距离测量 -->
        <div class="api-section">
            <h3>📏 距离测量</h3>
            <div class="grid">
                <div class="form-group">
                    <label>起点列表 (用|分隔):</label>
                    <input type="text" id="distance-origins" placeholder="北京市朝阳区|北京市海淀区" value="北京市朝阳区">
                </div>
                <div class="form-group">
                    <label>终点列表 (用|分隔):</label>
                    <input type="text" id="distance-destinations" placeholder="故宫|颐和园" value="故宫">
                </div>
            </div>
            <div class="form-group">
                <label>计算方式:</label>
                <select id="distance-type">
                    <option value="1">直线距离</option>
                    <option value="3">驾车导航距离</option>
                </select>
            </div>
            <button onclick="testDistance()">测量距离</button>
            <div id="distance-result" class="result" style="display:none;"></div>
        </div>
        
        <!-- IP定位 -->
        <div class="api-section">
            <h3>📍 IP定位</h3>
            <div class="form-group">
                <label>IP地址 (留空使用当前IP):</label>
                <input type="text" id="ip-address" placeholder="留空自动检测">
            </div>
            <button onclick="testIPLocation()">IP定位</button>
            <div id="ip-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // 周边搜索测试
        async function testSearchAround() {
            const location = document.getElementById('search-location').value;
            const keywords = document.getElementById('search-keywords').value;
            const radius = document.getElementById('search-radius').value;
            
            const resultDiv = document.getElementById('search-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 正在搜索...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`/api/amap/search-around?location=${encodeURIComponent(location)}&keywords=${encodeURIComponent(keywords)}&radius=${radius}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 找到 ${data.data.length} 个结果:\n\n` + 
                        data.data.map(poi => 
                            `📍 ${poi.name}\n` +
                            `   地址: ${poi.address}\n` +
                            `   类型: ${poi.type}\n` +
                            `   距离: ${poi.distance}米\n` +
                            `   坐标: ${poi.location.lng}, ${poi.location.lat}\n`
                        ).join('\n');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 搜索失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
        
        // 天气查询测试
        async function testWeather() {
            const city = document.getElementById('weather-city').value;
            const extensions = document.getElementById('weather-extensions').value;
            
            const resultDiv = document.getElementById('weather-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 正在查询天气...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`/api/amap/weather?city=${encodeURIComponent(city)}&extensions=${extensions}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    const weather = data.data;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 天气查询成功:\n\n` +
                        `🏙️ 城市: ${weather.city}\n` +
                        `🌤️ 天气: ${weather.weather}\n` +
                        `🌡️ 温度: ${weather.temperature}°C\n` +
                        `💨 风向: ${weather.winddirection}\n` +
                        `💨 风力: ${weather.windpower}级\n` +
                        `💧 湿度: ${weather.humidity}%\n` +
                        `⏰ 更新时间: ${weather.reporttime}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 天气查询失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
        
        // 路径规划测试
        async function testRoute() {
            const origin = document.getElementById('route-origin').value;
            const destination = document.getElementById('route-destination').value;
            const routeType = document.getElementById('route-type').value;
            
            const resultDiv = document.getElementById('route-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 正在规划路径...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`/api/amap/route/${routeType}?origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    const route = data.data;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 路径规划成功:\n\n` +
                        `📏 距离: ${route.distance}\n` +
                        `⏱️ 时间: ${route.duration}\n` +
                        `💰 过路费: ${route.tolls}\n` +
                        `🚦 红绿灯: ${route.traffic_lights}个\n\n` +
                        `📋 路线步骤:\n${route.steps.slice(0, 5).map((step, i) => `${i+1}. ${step}`).join('\n')}` +
                        (route.steps.length > 5 ? `\n... 还有${route.steps.length - 5}个步骤` : '');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 路径规划失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
        
        // 距离测量测试
        async function testDistance() {
            const origins = document.getElementById('distance-origins').value;
            const destinations = document.getElementById('distance-destinations').value;
            const type = document.getElementById('distance-type').value;
            
            const resultDiv = document.getElementById('distance-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 正在测量距离...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`/api/amap/distance?origins=${encodeURIComponent(origins)}&destinations=${encodeURIComponent(destinations)}&type=${type}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 距离测量成功:\n\n` +
                        data.data.distances.map((dist, i) => 
                            `📏 路线${i+1}: ${dist.distance}` + 
                            (dist.duration !== 'N/A' ? `, ${dist.duration}` : '')
                        ).join('\n');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 距离测量失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
        
        // IP定位测试
        async function testIPLocation() {
            const ip = document.getElementById('ip-address').value;
            
            const resultDiv = document.getElementById('ip-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 正在定位...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`/api/amap/ip-location?ip=${encodeURIComponent(ip)}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    const location = data.data;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ IP定位成功:\n\n` +
                        `📍 位置: ${location.address}\n` +
                        `🌐 坐标: ${location.longitude}, ${location.latitude}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ IP定位失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
