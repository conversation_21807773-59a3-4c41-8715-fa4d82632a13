#!/usr/bin/env python3
"""
检查阿里云百炼计费模式和调用记录
"""

import requests
import json
import asyncio
from datetime import datetime

async def test_api_with_usage_tracking():
    """测试API调用并跟踪使用量"""
    print("🧪 测试API调用并跟踪Token使用量")
    print("=" * 40)
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试LLM调用
    print("📤 测试LLM调用...")
    llm_data = {
        "model": "qwen-turbo",
        "messages": [
            {"role": "user", "content": "请详细解释什么是人工智能，包括其发展历史、主要技术和应用领域。"}
        ],
        "max_tokens": 500
    }
    
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=llm_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            usage = result.get('usage', {})
            
            print("✅ LLM调用成功")
            print(f"📊 Token使用量:")
            print(f"  输入Token: {usage.get('prompt_tokens', 'N/A')}")
            print(f"  输出Token: {usage.get('completion_tokens', 'N/A')}")
            print(f"  总Token: {usage.get('total_tokens', 'N/A')}")
            
            # 估算费用（qwen-turbo的大概价格）
            total_tokens = usage.get('total_tokens', 0)
            estimated_cost = total_tokens * 0.0014 / 1000  # 大概价格
            print(f"💰 预估费用: ¥{estimated_cost:.6f}")
            
        else:
            print(f"❌ LLM调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ LLM调用异常: {e}")
    
    # 测试Embedding调用
    print(f"\n📤 测试Embedding调用...")
    embedding_data = {
        "model": "text-embedding-v1",
        "input": [
            "这是第一个测试文本，用于生成embedding向量",
            "这是第二个测试文本，同样用于embedding生成",
            "人工智能技术正在快速发展，改变着我们的生活方式"
        ]
    }
    
    try:
        response = requests.post(
            f"{base_url}/embeddings",
            headers=headers,
            json=embedding_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            usage = result.get('usage', {})
            
            print("✅ Embedding调用成功")
            print(f"📊 Token使用量:")
            print(f"  总Token: {usage.get('total_tokens', 'N/A')}")
            
            # 估算费用
            total_tokens = usage.get('total_tokens', 0)
            estimated_cost = total_tokens * 0.0005 / 1000  # 大概价格
            print(f"💰 预估费用: ¥{estimated_cost:.6f}")
            
            # 检查返回的向量
            data = result.get('data', [])
            if data:
                print(f"📊 生成向量数量: {len(data)}")
                print(f"📊 向量维度: {len(data[0].get('embedding', []))}")
            
        else:
            print(f"❌ Embedding调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ Embedding调用异常: {e}")

def explain_billing_mode():
    """解释阿里云百炼的计费模式"""
    print("\n💰 阿里云百炼计费模式说明")
    print("=" * 35)
    
    print("🕐 计费周期:")
    print("  • 按量付费（后付费模式）")
    print("  • 每日凌晨结算前一天用量")
    print("  • 月初生成账单并扣费")
    print("  • 不是实时扣费！")
    
    print("\n📊 计费单位:")
    print("  • LLM调用: 按Token数量计费")
    print("  • Embedding: 按Token数量计费")
    print("  • 不同模型价格不同")
    
    print("\n💳 扣费时间:")
    print("  • 今天使用 → 明天结算 → 月初扣费")
    print("  • 可能延迟1-3天显示费用")
    
    print("\n🔍 查看方法:")
    print("  1. 阿里云控制台 → 百炼服务 → 用量统计")
    print("  2. 费用中心 → 消费记录")
    print("  3. 账单管理 → 账单详情")

def check_console_instructions():
    """提供控制台检查指导"""
    print("\n🔗 阿里云控制台检查步骤")
    print("=" * 35)
    
    print("📋 步骤1: 检查API调用记录")
    print("  1. 登录 https://bailian.console.aliyun.com/")
    print("  2. 选择您的API密钥对应的应用")
    print("  3. 查看'调用统计'或'用量监控'")
    print("  4. 确认是否有今天的调用记录")
    
    print("\n📋 步骤2: 检查费用记录")
    print("  1. 进入 https://expense.console.aliyun.com/")
    print("  2. 点击'消费记录' → '消费明细'")
    print("  3. 筛选产品: '通义千问'或'百炼'")
    print("  4. 查看是否有相关消费记录")
    
    print("\n📋 步骤3: 检查账单详情")
    print("  1. 费用中心 → '账单管理' → '账单详情'")
    print("  2. 选择当前月份")
    print("  3. 查看是否有百炼相关费用")
    
    print("\n💡 如果没有记录:")
    print("  • 可能在免费试用期")
    print("  • 可能有免费额度")
    print("  • 可能API密钥配置有问题")
    print("  • 联系阿里云技术支持确认")

async def main():
    """主函数"""
    print("🔍 阿里云百炼计费模式检查")
    print("=" * 40)
    
    # 当前时间
    now = datetime.now()
    print(f"📅 当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💡 如果今天有API调用，明天凌晨会结算费用")
    
    # 解释计费模式
    explain_billing_mode()
    
    # 执行API测试
    await test_api_with_usage_tracking()
    
    # 提供检查指导
    check_console_instructions()
    
    print("\n🎯 总结")
    print("=" * 10)
    print("✅ 您的API调用是正常的")
    print("⏰ 阿里云百炼采用按天结算，不是实时扣费")
    print("📅 请明天或后天检查控制台的费用记录")
    print("💰 如果确实没有产生费用，可能在免费试用期内")

if __name__ == "__main__":
    asyncio.run(main())
