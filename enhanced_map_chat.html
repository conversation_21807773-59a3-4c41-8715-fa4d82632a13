<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能地图交互系统 - 实时路线规划</title>
    
    <!-- 高德地图API -->
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJSCode: 'e4203c466e89130a43daedd0ae9f4368',
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=e4203c466e89130a43daedd0ae9f4368&plugin=AMap.PlaceSearch,AMap.Driving,AMap.Geocoder,AMap.Geolocation" async defer></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            height: 100vh;
            gap: 0;
        }

        /* 左侧地图区域 */
        .map-section {
            position: relative;
            background: #000;
            border-right: 3px solid #007AFF;
        }

        #mapContainer {
            width: 100%;
            height: 100%;
        }

        .map-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }

        .map-status {
            font-size: 16px;
            font-weight: 600;
            color: #007AFF;
            text-align: center;
            margin-bottom: 10px;
        }

        .route-info {
            display: none;
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            padding: 10px;
            border-radius: 10px;
            margin-top: 10px;
            font-size: 14px;
        }

        .route-info.show {
            display: block;
            animation: slideDown 0.5s ease-out;
        }

        .destination-image {
            display: none;
            position: absolute;
            bottom: 20px;
            left: 20px;
            width: 200px;
            height: 150px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            border: 3px solid #007AFF;
        }

        .destination-image.show {
            display: block;
            animation: slideUp 0.5s ease-out;
        }

        .destination-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .destination-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 10px;
            font-size: 12px;
            font-weight: 600;
        }

        /* 右侧聊天区域 */
        .chat-section {
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }

        .chat-header {
            padding: 20px;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 20px;
            background: #f8f9fa;
            max-height: calc(100vh - 200px);
            scroll-behavior: smooth;
        }

        .messages-container::-webkit-scrollbar {
            width: 8px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #007AFF;
            border-radius: 4px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #0056CC;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 20px;
            max-width: 85%;
            word-wrap: break-word;
            word-break: break-word;
            white-space: pre-wrap;
            line-height: 1.6;
            animation: messageSlideIn 0.4s ease-out;
            overflow-wrap: break-word;
        }

        .message.user {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 8px;
        }

        .message.assistant {
            background: linear-gradient(45deg, #E5E5EA, #F2F2F7);
            color: #1d1d1f;
            border-bottom-left-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .message.map-action {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            text-align: center;
            border-radius: 15px;
            font-weight: 600;
        }

        .input-section {
            padding: 20px;
            background: white;
            border-top: 2px solid #e5e5ea;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .text-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e5e5ea;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .text-input:focus {
            border-color: #007AFF;
            background: white;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        /* 地图标记样式 */
        .custom-marker {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .poi-marker {
            background: linear-gradient(45deg, #FF3B30, #FF6B6B);
            color: white;
            padding: 8px;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);
            border: 2px solid white;
            min-width: 40px;
            text-align: center;
        }

        .poi-marker .marker-number {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .poi-marker .marker-icon {
            font-size: 16px;
        }

        .weather-marker {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            padding: 10px;
            border-radius: 25px;
            box-shadow: 0 4px 12px rgba(52, 199, 89, 0.4);
            border: 2px solid white;
            text-align: center;
            min-width: 60px;
        }

        .weather-marker .weather-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .weather-marker .weather-temp {
            font-size: 12px;
            font-weight: bold;
        }

        /* 信息窗口样式 */
        .info-window {
            padding: 15px;
            max-width: 250px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .info-window h4 {
            margin: 0 0 10px 0;
            color: #007AFF;
            font-size: 16px;
        }

        .info-window p {
            margin: 5px 0;
            font-size: 14px;
            color: #333;
        }

        .weather-info p {
            margin: 8px 0;
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .weather-info p:last-child {
            border-bottom: none;
        }

        /* 路线标记样式 */
        .start-marker {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(52, 199, 89, 0.4);
            border: 2px solid white;
            text-align: center;
            min-width: 50px;
        }

        .end-marker {
            background: linear-gradient(45deg, #FF3B30, #FF6B6B);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);
            border: 2px solid white;
            text-align: center;
            min-width: 50px;
        }

        .start-marker .marker-icon,
        .end-marker .marker-icon {
            font-size: 18px;
            margin-bottom: 2px;
        }

        .start-marker .marker-text,
        .end-marker .marker-text {
            font-size: 11px;
            font-weight: bold;
        }

        /* 路线信息窗口样式 */
        .route-info {
            min-width: 200px;
        }

        .route-info hr {
            margin: 10px 0;
            border: none;
            border-top: 1px solid #e0e0e0;
        }

        .route-info p {
            margin: 6px 0;
            font-size: 13px;
        }

        .btn-voice {
            background: linear-gradient(45deg, #34C759, #30D158);
            min-width: 60px;
        }

        .btn-voice:hover {
            box-shadow: 0 8px 25px rgba(52, 199, 89, 0.3);
        }

        .btn-voice.recording {
            background: linear-gradient(45deg, #FF3B30, #FF6B35);
            animation: pulse 1.5s infinite;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 15px;
            color: #007AFF;
            font-weight: 600;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 15px;
            margin: 10px 20px;
        }

        .loading.show {
            display: block;
            animation: loadingPulse 1.5s infinite;
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes loadingPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr 1fr;
            }
            
            .map-section {
                border-right: none;
                border-bottom: 3px solid #007AFF;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 左侧实时地图区域 -->
        <div class="map-section">
            <div id="mapContainer"></div>
            
            <!-- 地图状态覆盖层 -->
            <div class="map-overlay">
                <div class="map-status" id="mapStatus">🗺️ 正在初始化高德地图...</div>
                <div class="route-info" id="routeInfo">
                    <div id="routeDetails"></div>
                </div>
            </div>
            
            <!-- 目的地图片展示 -->
            <div class="destination-image" id="destinationImage">
                <img id="destinationImg" src="" alt="目的地">
                <div class="destination-label" id="destinationLabel">目的地</div>
            </div>
        </div>

        <!-- 右侧智能交互区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>🗺️ 智能地图助手</h1>
                <p>实时路线规划 · 语音交互 · 目的地展示</p>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>地图助手:</strong><br>
                        您好！我是您的智能地图助手。<br><br>
                        🗺️ <strong>我的功能：</strong><br>
                        • 🛣️ 实时路线规划和导航<br>
                        • 🎤 语音和文字智能交互<br>
                        • 📍 地点搜索和POI查询<br>
                        • 🖼️ 目的地实景图片展示<br>
                        • 🚗 多种出行方式推荐<br><br>
                        请告诉我您的出发地和目的地，我会为您规划最佳路线！
                    </div>
                </div>
            </div>

            <div class="loading" id="loadingIndicator">
                <span>🧠 正在处理中...</span>
            </div>

            <div class="input-section">
                <input 
                    type="text" 
                    id="textInput" 
                    class="text-input" 
                    placeholder="例如：从北京站到故宫怎么走？"
                >
                <button id="voiceBtn" class="btn btn-voice" title="语音输入">
                    🎤
                </button>
                <button id="sendBtn" class="btn">发送</button>
            </div>
        </div>
    </div>

    <script>
        /**
         * 增强版智能地图交互系统
         * 实现实时路线规划、语音交互、目的地图片展示
         */
        class EnhancedMapChat {
            constructor() {
                // 核心组件
                this.map = null;
                this.websocket = null;
                this.recognition = null;
                this.driving = null;
                this.placeSearch = null;
                this.geocoder = null;
                
                // 状态管理
                this.isListening = false;
                this.currentRoute = null;
                this.lastDestination = null;
                
                // DOM元素
                this.elements = {
                    mapContainer: document.getElementById('mapContainer'),
                    mapStatus: document.getElementById('mapStatus'),
                    routeInfo: document.getElementById('routeInfo'),
                    routeDetails: document.getElementById('routeDetails'),
                    destinationImage: document.getElementById('destinationImage'),
                    destinationImg: document.getElementById('destinationImg'),
                    destinationLabel: document.getElementById('destinationLabel'),
                    messagesContainer: document.getElementById('messagesContainer'),
                    textInput: document.getElementById('textInput'),
                    voiceBtn: document.getElementById('voiceBtn'),
                    sendBtn: document.getElementById('sendBtn'),
                    loadingIndicator: document.getElementById('loadingIndicator')
                };
                
                this.initialize();
            }

            initialize() {
                console.log('🗺️ 初始化增强版智能地图交互系统...');

                // 检查高德地图API加载状态
                this.checkAmapApiStatus();

                // 设置事件监听器
                this.setupEventListeners();

                // 初始化WebSocket
                this.initializeWebSocket();

                // 初始化语音识别
                this.initializeSpeechRecognition();

                // 初始化音频上下文
                this.initializeAudioContext();
            }

            initializeAudioContext() {
                this.audioContextInitialized = false;

                const initAudio = () => {
                    if (!this.audioContextInitialized) {
                        try {
                            // 播放一个静音音频来初始化音频上下文
                            const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                            silentAudio.volume = 0;
                            silentAudio.play().then(() => {
                                this.audioContextInitialized = true;
                                console.log('✅ 音频上下文已初始化，Edge-TTS可以正常播放');
                                document.removeEventListener('click', initAudio);
                                document.removeEventListener('touchstart', initAudio);
                            }).catch(() => {
                                console.log('⚠️ 音频上下文初始化失败');
                            });
                        } catch (error) {
                            console.log('⚠️ 音频上下文初始化异常:', error);
                        }
                    }
                };

                // 监听用户交互事件来初始化音频
                document.addEventListener('click', initAudio);
                document.addEventListener('touchstart', initAudio);

                console.log('🎤 音频上下文初始化监听器已设置');
            }

            checkAmapApiStatus() {
                let retryCount = 0;
                const maxRetries = 10;

                const checkApi = () => {
                    if (typeof AMap !== 'undefined') {
                        console.log('✅ 高德地图API已加载');
                        this.initializeMap();
                    } else {
                        retryCount++;
                        if (retryCount < maxRetries) {
                            console.log(`⏳ 等待高德地图API加载... (${retryCount}/${maxRetries})`);
                            this.updateMapStatus(`⏳ 正在加载地图API... (${retryCount}/${maxRetries})`);
                            setTimeout(checkApi, 1000);
                        } else {
                            console.error('❌ 高德地图API加载超时');
                            this.updateMapStatus('❌ 地图API加载失败，请检查网络连接并刷新页面');
                            this.showMapLoadError();
                        }
                    }
                };

                checkApi();
            }

            showMapLoadError() {
                const mapContainer = this.elements.mapContainer;
                mapContainer.innerHTML = `
                    <div style="
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        height: 100%;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        text-align: center;
                        padding: 40px;
                    ">
                        <div style="font-size: 48px; margin-bottom: 20px;">🗺️</div>
                        <h2 style="margin-bottom: 15px;">地图加载失败</h2>
                        <p style="margin-bottom: 20px; opacity: 0.9;">
                            高德地图API无法加载，可能的原因：<br>
                            • 网络连接问题<br>
                            • API密钥配置问题<br>
                            • 浏览器兼容性问题
                        </p>
                        <button onclick="location.reload()" style="
                            padding: 12px 24px;
                            background: #007AFF;
                            color: white;
                            border: none;
                            border-radius: 20px;
                            font-size: 16px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#0056CC'" onmouseout="this.style.background='#007AFF'">
                            🔄 重新加载
                        </button>
                    </div>
                `;
            }

            initializeMap() {
                try {
                    this.updateMapStatus('🗺️ 正在初始化高德地图...');

                    // 等待高德地图API加载
                    if (typeof AMap === 'undefined') {
                        this.updateMapStatus('⏳ 等待高德地图API加载...');
                        setTimeout(() => {
                            this.initializeMap();
                        }, 1000);
                        return;
                    }

                    console.log('✅ 高德地图API已加载，版本:', AMap.version);

                    // 创建地图实例
                    this.map = new AMap.Map('mapContainer', {
                        zoom: 11,
                        center: [116.397428, 39.90923], // 北京天安门
                        mapStyle: 'amap://styles/normal',
                        resizeEnable: true,
                        rotateEnable: true,
                        pitchEnable: false, // 先禁用3D倾斜，避免兼容性问题
                        viewMode: '2D' // 先使用2D模式
                    });

                    // 地图加载完成事件
                    this.map.on('complete', () => {
                        console.log('✅ 地图加载完成');
                        this.updateMapStatus('✅ 地图初始化成功，等待路线规划...');

                        // 添加控件
                        try {
                            this.map.addControl(new AMap.Scale());
                            this.map.addControl(new AMap.ToolBar());
                            console.log('✅ 地图控件添加成功');
                        } catch (controlError) {
                            console.warn('⚠️ 地图控件添加失败:', controlError);
                        }

                        // 初始化地图插件
                        this.initializeMapPlugins();
                    });

                    // 地图加载失败事件
                    this.map.on('error', (error) => {
                        console.error('❌ 地图加载失败:', error);
                        this.updateMapStatus('❌ 地图加载失败，请检查网络连接');
                    });

                } catch (error) {
                    console.error('❌ 地图初始化异常:', error);
                    this.updateMapStatus('❌ 地图初始化失败: ' + error.message);

                    // 尝试降级到简单模式
                    this.initializeSimpleMap();
                }
            }

            initializeSimpleMap() {
                try {
                    console.log('🔄 尝试简单模式初始化地图...');
                    this.updateMapStatus('🔄 尝试简单模式...');

                    this.map = new AMap.Map('mapContainer', {
                        zoom: 10,
                        center: [116.397428, 39.90923],
                        mapStyle: 'amap://styles/normal'
                    });

                    this.map.on('complete', () => {
                        console.log('✅ 简单模式地图初始化成功');
                        this.updateMapStatus('✅ 地图初始化成功（简单模式）');
                        this.initializeMapPlugins();
                    });

                } catch (simpleError) {
                    console.error('❌ 简单模式也失败:', simpleError);
                    this.updateMapStatus('❌ 地图初始化完全失败，请刷新页面重试');
                }
            }

            initializeMapPlugins() {
                try {
                    console.log('🔧 开始初始化地图插件...');

                    this.map.plugin(['AMap.Driving', 'AMap.PlaceSearch', 'AMap.Geocoder', 'AMap.Geolocation'], () => {
                        try {
                            // 驾车路线规划
                            this.driving = new AMap.Driving({
                                map: this.map,
                                showTraffic: true,
                                hideMarkers: false,
                                autoFitView: true,
                                policy: AMap.DrivingPolicy.LEAST_TIME,
                                // 自定义路线样式
                                lineOptions: {
                                    strokeColor: '#007AFF',
                                    strokeWeight: 6,
                                    strokeOpacity: 0.8,
                                    strokeStyle: 'solid'
                                },
                                // 自定义标记样式
                                startMarkerOptions: {
                                    content: '<div style="background: #34C759; color: white; padding: 8px 12px; border-radius: 20px; font-size: 14px; font-weight: bold;">🚩 起点</div>'
                                },
                                endMarkerOptions: {
                                    content: '<div style="background: #FF3B30; color: white; padding: 8px 12px; border-radius: 20px; font-size: 14px; font-weight: bold;">🎯 终点</div>'
                                }
                            });
                            console.log('✅ 路线规划插件初始化成功');

                            // POI搜索
                            this.placeSearch = new AMap.PlaceSearch({
                                pageSize: 10,
                                pageIndex: 1,
                                city: '010',
                                map: this.map
                            });
                            console.log('✅ POI搜索插件初始化成功');

                            // 地理编码
                            this.geocoder = new AMap.Geocoder({
                                city: '010'
                            });
                            console.log('✅ 地理编码插件初始化成功');

                            // 定位服务
                            this.geolocation = new AMap.Geolocation({
                                enableHighAccuracy: true,
                                timeout: 10000,
                                maximumAge: 0,
                                convert: true,
                                showButton: false, // 不显示定位按钮，我们自己控制
                                showMarker: true,
                                showCircle: true,
                                panToLocation: true,
                                zoomToAccuracy: true
                            });
                            console.log('✅ 定位服务初始化成功');

                            // 自动获取用户位置
                            this.getCurrentUserLocation();

                            console.log('✅ 所有地图插件初始化完成');
                            this.updateMapStatus('✅ 地图系统就绪，正在定位您的位置...');

                        } catch (pluginError) {
                            console.error('❌ 地图插件初始化失败:', pluginError);
                            this.updateMapStatus('⚠️ 地图插件初始化失败，部分功能可能不可用');
                        }
                    });

                } catch (error) {
                    console.error('❌ 地图插件加载失败:', error);
                    this.updateMapStatus('❌ 地图插件加载失败');

                    // 尝试基础功能
                    this.initializeBasicMapFeatures();
                }
            }

            getCurrentUserLocation() {
                if (this.geolocation) {
                    console.log('📍 开始获取用户当前位置...');
                    this.updateMapStatus('📍 正在定位您的位置...');

                    this.geolocation.getCurrentPosition((status, result) => {
                        if (status === 'complete') {
                            const position = result.position;
                            const address = result.formattedAddress;

                            this.userCurrentLocation = {
                                longitude: position.lng,
                                latitude: position.lat,
                                address: address || '当前位置'
                            };

                            console.log('✅ 用户位置获取成功:', this.userCurrentLocation);
                            this.updateMapStatus(`📍 已定位到您的位置: ${address || '当前位置'}`);

                            // 在地图上添加当前位置标记
                            this.addCurrentLocationMarker(position);

                            // 移动地图到用户位置
                            this.map.setCenter([position.lng, position.lat]);
                            this.map.setZoom(15);

                        } else {
                            console.warn('⚠️ 定位失败:', result);
                            this.updateMapStatus('⚠️ 定位失败，将使用默认位置');

                            // 使用默认位置（天安门）
                            this.userCurrentLocation = {
                                longitude: 116.397428,
                                latitude: 39.90923,
                                address: '北京市中心'
                            };

                            this.addCurrentLocationMarker(new AMap.LngLat(116.397428, 39.90923));
                        }
                    });
                } else {
                    console.warn('⚠️ 定位服务不可用');
                    this.updateMapStatus('⚠️ 定位服务不可用，使用默认位置');

                    // 使用默认位置
                    this.userCurrentLocation = {
                        longitude: 116.397428,
                        latitude: 39.90923,
                        address: '北京市中心'
                    };
                }
            }

            addCurrentLocationMarker(position) {
                // 创建当前位置标记
                const currentMarker = new AMap.Marker({
                    position: position,
                    title: '您的当前位置',
                    content: `
                        <div style="
                            background: linear-gradient(45deg, #007AFF, #5856D6);
                            color: white;
                            padding: 10px 15px;
                            border-radius: 25px;
                            font-size: 14px;
                            font-weight: bold;
                            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
                            border: 3px solid white;
                        ">
                            📍 您的位置
                        </div>
                    `,
                    anchor: 'center'
                });

                // 添加信息窗体
                const infoWindow = new AMap.InfoWindow({
                    content: `
                        <div style="padding: 15px; text-align: center;">
                            <h4 style="margin: 0 0 10px 0; color: #007AFF;">📍 您的当前位置</h4>
                            <p style="margin: 0; color: #666;">${this.userCurrentLocation.address}</p>
                            <p style="margin: 5px 0 0 0; color: #999; font-size: 12px;">
                                坐标: ${position.lng.toFixed(6)}, ${position.lat.toFixed(6)}
                            </p>
                        </div>
                    `
                });

                currentMarker.on('click', () => {
                    infoWindow.open(this.map, currentMarker.getPosition());
                });

                this.map.add(currentMarker);
                this.currentLocationMarker = currentMarker;

                console.log('✅ 当前位置标记已添加到地图');
            }

            initializeBasicMapFeatures() {
                try {
                    console.log('🔄 初始化基础地图功能...');

                    // 基础地理编码（不依赖插件）
                    this.geocoder = new AMap.Geocoder({
                        city: '010'
                    });

                    this.updateMapStatus('✅ 基础地图功能已就绪');
                    console.log('✅ 基础地图功能初始化完成');

                } catch (basicError) {
                    console.error('❌ 基础地图功能初始化失败:', basicError);
                    this.updateMapStatus('❌ 地图功能初始化失败');
                }
            }

            setupEventListeners() {
                // 语音按钮
                this.elements.voiceBtn.addEventListener('click', () => {
                    this.toggleVoiceInput();
                });

                // 发送按钮
                this.elements.sendBtn.addEventListener('click', () => {
                    const text = this.elements.textInput.value.trim();
                    if (text) {
                        this.sendQuery(text);
                        this.elements.textInput.value = '';
                    }
                });

                // 回车发送
                this.elements.textInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.elements.sendBtn.click();
                    }
                });
            }

            initializeWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;

                this.websocket = new WebSocket(wsUrl);

                this.websocket.onopen = () => {
                    console.log('✅ WebSocket连接成功');
                };

                this.websocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleServerMessage(data);
                    } catch (error) {
                        console.error('❌ 消息解析失败:', error);
                    }
                };

                this.websocket.onerror = (error) => {
                    console.error('❌ WebSocket错误:', error);
                };

                this.websocket.onclose = () => {
                    console.log('🔌 WebSocket连接关闭');
                };
            }

            initializeSpeechRecognition() {
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();

                    this.recognition.continuous = false;
                    this.recognition.interimResults = false;
                    this.recognition.lang = 'zh-CN';

                    this.recognition.onstart = () => {
                        this.isListening = true;
                        this.updateVoiceButton(true);
                    };

                    this.recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        this.elements.textInput.value = transcript;
                        setTimeout(() => {
                            this.sendQuery(transcript);
                        }, 500);
                    };

                    this.recognition.onend = () => {
                        this.isListening = false;
                        this.updateVoiceButton(false);
                    };

                    this.recognition.onerror = (event) => {
                        console.error('❌ 语音识别错误:', event.error);
                        this.isListening = false;
                        this.updateVoiceButton(false);
                    };
                }
            }

            toggleVoiceInput() {
                if (!this.recognition) {
                    alert('您的浏览器不支持语音识别功能');
                    return;
                }

                if (this.isListening) {
                    this.recognition.stop();
                } else {
                    try {
                        this.recognition.start();
                    } catch (error) {
                        console.error('❌ 启动语音识别失败:', error);
                        alert('启动语音识别失败，请检查麦克风权限');
                    }
                }
            }

            updateVoiceButton(isRecording) {
                if (isRecording) {
                    this.elements.voiceBtn.classList.add('recording');
                    this.elements.voiceBtn.innerHTML = '🔴';
                } else {
                    this.elements.voiceBtn.classList.remove('recording');
                    this.elements.voiceBtn.innerHTML = '🎤';
                }
            }

            sendQuery(text) {
                if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                    alert('未连接到服务器');
                    return;
                }

                // 记录用户查询，用于地理位置提取
                this.lastUserQuery = text.trim();
                console.log('📝 记录用户查询:', this.lastUserQuery);

                this.addMessage('user', text);
                this.showLoading(true);
                this.updateMapStatus('🤔 正在思考中...');

                const message = {
                    type: 'query',
                    text: text.trim(),
                    timestamp: Date.now()
                };

                this.websocket.send(JSON.stringify(message));
            }

            containsLocationKeywords(text) {
                const locationKeywords = [
                    '北京', '上海', '广州', '深圳', '天津', '重庆',
                    '故宫', '天安门', '颐和园', '长城', '鸟巢', '水立方',
                    '机场', '火车站', '地铁站', '医院', '学校', '商场',
                    '怎么走', '路线', '导航', '地址', '位置', '在哪里'
                ];

                return locationKeywords.some(keyword => text.includes(keyword));
            }

            tryExtractImplicitLocation(userQuery) {
                // 尝试从用户问题中提取隐含的地理信息
                const locations = this.extractLocationsFromText(userQuery);
                if (locations.origin && locations.destination) {
                    console.log('🔍 从用户问题中提取到路线信息:', locations);
                    this.addMapActionMessage(`🗺️ 检测到路线查询，正在地图上显示：${locations.origin} → ${locations.destination}`);
                    this.planRouteOnMap(locations.origin, locations.destination);
                } else if (locations.destination) {
                    console.log('🔍 从用户问题中提取到目的地:', locations.destination);
                    this.addMapActionMessage(`📍 检测到地点查询，正在地图上显示：${locations.destination}`);
                    this.showLocationOnMap(locations.destination);
                }
            }

            handleServerMessage(data) {
                if (data.type === 'response') {
                    this.showLoading(false);

                    // 显示RAG系统的回答
                    this.addMessage('assistant', data.text);

                    // 处理地图数据（如果有）
                    if (data.map_data && data.map_action && data.map_action !== 'none') {
                        this.handleMapData(data.map_data, data.map_action);
                    } else {
                        // 检查是否包含地理位置信息并在地图上显示（原有逻辑）
                        this.processLocationQuery(data.text);
                    }

                    // 播放语音（优先使用Edge-TTS）
                    console.log('🎤 语音数据检查:', {
                        hasAudio: !!data.audio,
                        audioType: data.audio_type,
                        audioLength: data.audio ? data.audio.length : 0,
                        textLength: data.text ? data.text.length : 0
                    });

                    if (data.audio && data.audio_type === 'edge-tts') {
                        console.log('✅ 使用Edge-TTS高质量女声 (zh-CN-XiaoxiaoNeural)');
                        this.addMapActionMessage('🎤 正在播放Edge-TTS高质量女声...');
                        this.playEdgeTTSAudio(data.audio, data.text);
                    } else {
                        console.log('⚠️ Edge-TTS不可用，降级使用浏览器TTS');
                        console.log('原因:', !data.audio ? '无音频数据' : '音频类型不匹配: ' + data.audio_type);
                        this.addMapActionMessage('🔊 使用浏览器TTS播放...');
                        this.speakText(data.text);
                    }

                } else if (data.type === 'error') {
                    this.showLoading(false);
                    this.addMessage('assistant', `错误: ${data.message || '未知错误'}`);
                }
            }

            handleMapData(mapData, mapAction) {
                console.log('🗺️ 处理地图数据:', mapData, '动作:', mapAction);

                try {
                    if (mapAction === 'show_pois') {
                        this.showPOIsOnMap(mapData);
                    } else if (mapAction === 'show_weather') {
                        this.showWeatherOnMap(mapData);
                    } else if (mapAction === 'show_route') {
                        this.showRouteOnMap(mapData);
                    }
                } catch (error) {
                    console.error('❌ 地图数据处理失败:', error);
                    this.addMapActionMessage('❌ 地图显示失败，请稍后再试');
                }
            }

            showPOIsOnMap(mapData) {
                console.log('📍 在地图上显示POI标记:', mapData);

                // 清除之前的标记
                this.clearMapMarkers();

                if (mapData.markers && mapData.markers.length > 0) {
                    const markers = mapData.markers;
                    const center = mapData.center;

                    this.addMapActionMessage(`📍 正在地图上显示${markers.length}个${mapData.search_keyword}...`);

                    // 添加标记到地图
                    markers.forEach((markerData, index) => {
                        const marker = new AMap.Marker({
                            position: [markerData.lng, markerData.lat],
                            title: markerData.title,
                            content: `<div class="custom-marker poi-marker">
                                <div class="marker-number">${index + 1}</div>
                                <div class="marker-icon">📍</div>
                            </div>`,
                            offset: new AMap.Pixel(-15, -30)
                        });

                        // 添加信息窗口
                        const infoWindow = new AMap.InfoWindow({
                            content: `<div class="info-window">
                                <h4>${markerData.title}</h4>
                                <p>${markerData.content}</p>
                            </div>`,
                            offset: new AMap.Pixel(0, -30)
                        });

                        marker.on('click', () => {
                            infoWindow.open(this.map, marker.getPosition());
                        });

                        this.map.add(marker);
                        this.mapMarkers.push(marker);
                    });

                    // 移动地图视角到中心位置
                    if (center) {
                        this.map.setCenter([center.lng, center.lat]);
                        this.map.setZoom(mapData.zoom || 13);

                        this.addMapActionMessage(`🎯 地图视角已移动到${mapData.search_location}`);
                    }

                    // 自动适应视野以显示所有标记
                    setTimeout(() => {
                        if (this.mapMarkers.length > 1) {
                            this.map.setFitView(this.mapMarkers, false, [20, 20, 20, 20]);
                        }
                    }, 500);

                    this.addMapActionMessage(`✅ 已在地图上标记${markers.length}个${mapData.search_keyword}`);
                }
            }

            showWeatherOnMap(mapData) {
                console.log('🌤️ 在地图上显示天气信息:', mapData);

                // 清除之前的标记
                this.clearMapMarkers();

                if (mapData.markers && mapData.markers.length > 0) {
                    const weatherMarker = mapData.markers[0];
                    const center = mapData.center;

                    this.addMapActionMessage(`🌤️ 正在地图上显示${mapData.search_location}天气...`);

                    // 添加天气标记到地图
                    const marker = new AMap.Marker({
                        position: [weatherMarker.lng, weatherMarker.lat],
                        title: weatherMarker.title,
                        content: `<div class="custom-marker weather-marker">
                            <div class="weather-icon">🌤️</div>
                            <div class="weather-temp">${weatherMarker.weather_data.temperature}°C</div>
                        </div>`,
                        offset: new AMap.Pixel(-25, -40)
                    });

                    // 添加天气信息窗口
                    const infoWindow = new AMap.InfoWindow({
                        content: `<div class="info-window weather-info">
                            <h4>🌤️ ${weatherMarker.weather_data.city}天气</h4>
                            <p><strong>天气：</strong>${weatherMarker.weather_data.weather}</p>
                            <p><strong>温度：</strong>${weatherMarker.weather_data.temperature}°C</p>
                            <p><strong>湿度：</strong>${weatherMarker.weather_data.humidity}%</p>
                            <p><strong>风力：</strong>${weatherMarker.weather_data.windpower}级</p>
                        </div>`,
                        offset: new AMap.Pixel(0, -40)
                    });

                    marker.on('click', () => {
                        infoWindow.open(this.map, marker.getPosition());
                    });

                    this.map.add(marker);
                    this.mapMarkers.push(marker);

                    // 移动地图视角到天气位置
                    if (center) {
                        this.map.setCenter([center.lng, center.lat]);
                        this.map.setZoom(mapData.zoom || 10);

                        this.addMapActionMessage(`🎯 地图视角已移动到${mapData.search_location}`);
                    }

                    // 自动打开信息窗口显示天气
                    setTimeout(() => {
                        infoWindow.open(this.map, marker.getPosition());
                    }, 1000);

                    this.addMapActionMessage(`✅ 已在地图上显示${mapData.search_location}天气信息`);
                }
            }

            showRouteOnMap(mapData) {
                console.log('🛣️ 在地图上显示导航路线:', mapData);

                // 清除之前的标记和路线
                this.clearMapMarkers();
                this.clearMapRoutes();

                const { origin, destination, route_path, route_info } = mapData;

                this.addMapActionMessage(`🛣️ 正在显示从${origin.name}到${destination.name}的导航路线...`);

                // 1. 添加起点标记
                const startMarker = new AMap.Marker({
                    position: [origin.lng, origin.lat],
                    title: `起点: ${origin.name}`,
                    content: `<div class="custom-marker start-marker">
                        <div class="marker-icon">🚩</div>
                        <div class="marker-text">起点</div>
                    </div>`,
                    offset: new AMap.Pixel(-20, -40)
                });

                // 2. 添加终点标记
                const endMarker = new AMap.Marker({
                    position: [destination.lng, destination.lat],
                    title: `终点: ${destination.name}`,
                    content: `<div class="custom-marker end-marker">
                        <div class="marker-icon">🏁</div>
                        <div class="marker-text">终点</div>
                    </div>`,
                    offset: new AMap.Pixel(-20, -40)
                });

                // 3. 添加起点和终点信息窗口
                const startInfoWindow = new AMap.InfoWindow({
                    content: `<div class="info-window route-info">
                        <h4>🚩 起点</h4>
                        <p><strong>地点:</strong> ${origin.name}</p>
                        <p><strong>地址:</strong> ${origin.address}</p>
                    </div>`,
                    offset: new AMap.Pixel(0, -40)
                });

                const endInfoWindow = new AMap.InfoWindow({
                    content: `<div class="info-window route-info">
                        <h4>🏁 终点</h4>
                        <p><strong>地点:</strong> ${destination.name}</p>
                        <p><strong>地址:</strong> ${destination.address}</p>
                        <hr>
                        <p><strong>📏 距离:</strong> ${route_info.distance}</p>
                        <p><strong>⏱️ 时间:</strong> ${route_info.duration}</p>
                        <p><strong>💰 费用:</strong> ${route_info.tolls}</p>
                        <p><strong>🚦 红绿灯:</strong> ${route_info.traffic_lights}个</p>
                    </div>`,
                    offset: new AMap.Pixel(0, -40)
                });

                // 4. 绑定点击事件
                startMarker.on('click', () => {
                    endInfoWindow.close();
                    startInfoWindow.open(this.map, startMarker.getPosition());
                });

                endMarker.on('click', () => {
                    startInfoWindow.close();
                    endInfoWindow.open(this.map, endMarker.getPosition());
                });

                // 5. 添加标记到地图
                this.map.add([startMarker, endMarker]);
                this.mapMarkers.push(startMarker, endMarker);

                // 6. 绘制路线
                if (route_path && route_path.length > 0) {
                    const routeLine = new AMap.Polyline({
                        path: route_path,
                        strokeColor: '#007AFF',
                        strokeWeight: 6,
                        strokeOpacity: 0.8,
                        strokeStyle: 'solid',
                        lineJoin: 'round',
                        lineCap: 'round'
                    });

                    this.map.add(routeLine);
                    this.mapRoutes.push(routeLine);

                    this.addMapActionMessage(`✅ 路线绘制成功，共${route_path.length}个路径点`);
                } else {
                    // 如果没有详细路径，绘制直线
                    const straightLine = new AMap.Polyline({
                        path: [[origin.lng, origin.lat], [destination.lng, destination.lat]],
                        strokeColor: '#FF3B30',
                        strokeWeight: 4,
                        strokeOpacity: 0.6,
                        strokeStyle: 'dashed',
                        lineJoin: 'round',
                        lineCap: 'round'
                    });

                    this.map.add(straightLine);
                    this.mapRoutes.push(straightLine);

                    this.addMapActionMessage('⚠️ 显示直线路径，详细路线数据不可用');
                }

                // 7. 调整地图视野以显示整个路线
                const bounds = new AMap.Bounds(
                    [Math.min(origin.lng, destination.lng), Math.min(origin.lat, destination.lat)],
                    [Math.max(origin.lng, destination.lng), Math.max(origin.lat, destination.lat)]
                );

                this.map.setBounds(bounds, false, [50, 50, 50, 50]);

                // 8. 自动显示终点信息
                setTimeout(() => {
                    endInfoWindow.open(this.map, endMarker.getPosition());
                }, 1500);

                this.addMapActionMessage(`🎯 导航路线已显示: ${route_info.distance}, ${route_info.duration}`);
            }

            clearMapMarkers() {
                // 清除之前的标记
                if (this.mapMarkers && this.mapMarkers.length > 0) {
                    this.mapMarkers.forEach(marker => {
                        this.map.remove(marker);
                    });
                    this.mapMarkers = [];
                }

                // 初始化标记数组（如果不存在）
                if (!this.mapMarkers) {
                    this.mapMarkers = [];
                }
            }

            clearMapRoutes() {
                // 清除之前的路线
                if (this.mapRoutes && this.mapRoutes.length > 0) {
                    this.mapRoutes.forEach(route => {
                        this.map.remove(route);
                    });
                    this.mapRoutes = [];
                }

                // 初始化路线数组（如果不存在）
                if (!this.mapRoutes) {
                    this.mapRoutes = [];
                }
            }

            processLocationQuery(responseText) {
                // 同时检查用户的原始问题和AI的回答
                const userQuery = this.lastUserQuery || '';
                const combinedText = userQuery + ' ' + responseText;

                console.log('🔍 处理地理查询:', { userQuery, responseText, combinedText });

                // 检查是否包含路线规划相关内容
                if (this.isRouteQuery(combinedText)) {
                    const locations = this.extractLocationsFromText(combinedText);
                    if (locations.origin && locations.destination) {
                        this.addMapActionMessage(`🗺️ 正在地图上规划路线：${locations.origin} → ${locations.destination}`);
                        this.planRouteOnMap(locations.origin, locations.destination);
                        return;
                    }
                }

                // 检查是否包含地点查询
                if (this.isLocationQuery(combinedText)) {
                    const location = this.extractSingleLocation(combinedText);
                    if (location) {
                        this.addMapActionMessage(`📍 正在地图上显示位置：${location}`);
                        this.showLocationOnMap(location);
                        return;
                    }
                }

                // 如果没有明确的地理信息，但用户问题可能包含地点，尝试提取
                if (this.containsLocationKeywords(userQuery)) {
                    console.log('🔍 用户问题可能包含地理信息，尝试提取...');
                    this.tryExtractImplicitLocation(userQuery);
                }
            }

            isRouteQuery(text) {
                const routeKeywords = ['路线', '导航', '怎么走', '路径', '开车', '驾车', '从.*到', '去.*怎么走'];
                return routeKeywords.some(keyword => new RegExp(keyword).test(text));
            }

            isLocationQuery(text) {
                const locationKeywords = ['位置', '地址', '在哪里', '坐标', '地点'];
                return locationKeywords.some(keyword => text.includes(keyword));
            }

            extractLocationsFromText(text) {
                const locations = { origin: null, destination: null };

                console.log('🔍 正在解析文本:', text);

                // 匹配"我在...想去..."模式
                const currentToMatch = text.match(/我在(.{2,30}?)，?想去(.{2,30})|我在(.{2,30}?)，?去(.{2,30})/);
                if (currentToMatch) {
                    locations.origin = (currentToMatch[1] || currentToMatch[3]).trim();
                    locations.destination = (currentToMatch[2] || currentToMatch[4]).trim();
                    console.log('✅ 匹配到"我在...想去..."模式:', locations);
                    return locations;
                }

                // 匹配"从...到..."模式
                const fromToMatch = text.match(/从(.{2,30}?)到(.{2,30}?)的?路线|从(.{2,30}?)到(.{2,30}?)怎么走|从(.{2,30}?)去(.{2,30})/);
                if (fromToMatch) {
                    locations.origin = (fromToMatch[1] || fromToMatch[3] || fromToMatch[5]).trim();
                    locations.destination = (fromToMatch[2] || fromToMatch[4] || fromToMatch[6]).trim();
                    console.log('✅ 匹配到"从...到..."模式:', locations);
                    return locations;
                }

                // 匹配"去..."模式
                const goToMatch = text.match(/去(.{2,30}?)的?路线|去(.{2,30}?)怎么走|到(.{2,30}?)怎么走/);
                if (goToMatch && !locations.destination) {
                    locations.destination = (goToMatch[1] || goToMatch[2] || goToMatch[3]).trim();
                    locations.origin = this.userCurrentLocation ? this.userCurrentLocation.address : "当前位置";
                    console.log('✅ 匹配到"去..."模式:', locations);
                    return locations;
                }

                // 匹配单独的地点名称（默认从当前位置出发）
                const singleLocationMatch = text.match(/^(.{2,30}?)$/);
                if (singleLocationMatch && !locations.origin && !locations.destination) {
                    const location = singleLocationMatch[1].trim();
                    // 排除一些明显不是地点的词汇
                    if (!location.includes('怎么') && !location.includes('什么') && !location.includes('哪里')) {
                        locations.destination = location;
                        locations.origin = this.userCurrentLocation ? this.userCurrentLocation.address : "当前位置";
                        console.log('✅ 匹配到单独地点，默认从当前位置出发:', locations);
                        return locations;
                    }
                }

                // 匹配"...到...路线规划"模式
                const routePlanMatch = text.match(/(.{2,30}?)到(.{2,30}?)路线|(.{2,30}?)到(.{2,30}?)规划/);
                if (routePlanMatch) {
                    locations.origin = (routePlanMatch[1] || routePlanMatch[3]).trim();
                    locations.destination = (routePlanMatch[2] || routePlanMatch[4]).trim();
                    console.log('✅ 匹配到路线规划模式:', locations);
                    return locations;
                }

                console.log('⚠️ 未能匹配到地理位置信息');
                return locations;
            }

            extractSingleLocation(text) {
                const locationMatch = text.match(/(.{2,20}?)在哪里|(.{2,20}?)的位置|(.{2,20}?)地址/);
                if (locationMatch) {
                    return (locationMatch[1] || locationMatch[2] || locationMatch[3]).trim();
                }
                return null;
            }

            async planRouteOnMap(origin, destination) {
                try {
                    console.log('🛣️ 开始规划路线:', origin, '->', destination);
                    this.updateMapStatus('🛣️ 正在规划路线...');
                    this.addMapActionMessage(`🔍 正在解析地址: ${origin} → ${destination}`);

                    // 清除之前的路线
                    if (this.driving) {
                        this.driving.clear();
                    }
                    this.map.clearMap();

                    // 地理编码获取坐标
                    console.log('🔍 开始地理编码...');
                    const originCoord = await this.geocodeLocation(origin);
                    const destCoord = await this.geocodeLocation(destination);

                    if (!originCoord) {
                        const errorMsg = `❌ 无法找到起点位置: ${origin}`;
                        console.error(errorMsg);
                        this.updateMapStatus(errorMsg);
                        this.addMapActionMessage(errorMsg);
                        return;
                    }

                    if (!destCoord) {
                        const errorMsg = `❌ 无法找到终点位置: ${destination}`;
                        console.error(errorMsg);
                        this.updateMapStatus(errorMsg);
                        this.addMapActionMessage(errorMsg);
                        return;
                    }

                    console.log('✅ 地理编码完成，开始路线规划...');
                    this.addMapActionMessage(`✅ 地址解析成功，正在规划最佳路线...`);

                    // 规划路线
                    this.driving.search(originCoord, destCoord, (status, result) => {
                        if (status === 'complete') {
                            console.log('✅ 路线规划成功:', result);

                            const route = result.routes[0];
                            const distance = (route.distance / 1000).toFixed(1);
                            const duration = Math.round(route.time / 60);

                            this.currentRoute = {
                                origin: origin,
                                destination: destination,
                                distance: distance,
                                duration: duration
                            };

                            this.updateMapStatus(`✅ 路线规划完成: ${distance}公里, ${duration}分钟`);
                            this.showRouteInfo(distance, duration, origin, destination);
                            this.addMapActionMessage(`🎉 路线规划成功！距离${distance}公里，预计${duration}分钟`);

                            // 获取并显示目的地图片
                            this.fetchDestinationImage(destination);

                            // 自动适应视野
                            setTimeout(() => {
                                this.map.setFitView();
                            }, 1000);

                        } else {
                            const errorMsg = `❌ 路线规划失败: ${status}`;
                            console.error(errorMsg, result);
                            this.updateMapStatus(errorMsg);
                            this.addMapActionMessage(errorMsg + '，请检查地址是否正确');
                        }
                    });

                } catch (error) {
                    const errorMsg = `❌ 路线规划异常: ${error.message}`;
                    console.error(errorMsg, error);
                    this.updateMapStatus(errorMsg);
                    this.addMapActionMessage(errorMsg);
                }
            }

            async geocodeLocation(locationName) {
                return new Promise((resolve) => {
                    if (locationName === "当前位置" || locationName === "我的位置" || locationName === "这里") {
                        // 使用用户的实际当前位置
                        if (this.userCurrentLocation) {
                            console.log('✅ 使用用户实际位置作为起点:', this.userCurrentLocation.address);
                            resolve(new AMap.LngLat(this.userCurrentLocation.longitude, this.userCurrentLocation.latitude));
                        } else {
                            console.log('⚠️ 用户位置未获取，使用默认位置');
                            resolve(new AMap.LngLat(116.397428, 39.90923));
                        }
                        return;
                    }

                    console.log('🔍 正在地理编码:', locationName);

                    // 清理地址名称
                    let cleanedName = this.cleanLocationName(locationName);
                    console.log('🧹 清理后的地址:', cleanedName);

                    this.geocoder.getLocation(cleanedName, (status, result) => {
                        if (status === 'complete' && result.geocodes.length > 0) {
                            const location = result.geocodes[0];
                            console.log('✅ 地理编码成功:', cleanedName, '->', location.formattedAddress);
                            resolve(location.location);
                        } else {
                            console.error('❌ 地理编码失败:', cleanedName, 'status:', status);

                            // 尝试模糊匹配
                            this.tryFuzzyGeocode(locationName, resolve);
                        }
                    });
                });
            }

            cleanLocationName(locationName) {
                console.log('🧹 开始清理地址:', locationName);

                // 移除可能干扰地理编码的词汇
                let cleaned = locationName
                    .replace(/，?请规划路线?/g, '')
                    .replace(/，?怎么走$/g, '') // 只移除结尾的"怎么走"
                    .replace(/的?路线$/g, '') // 只移除结尾的"路线"
                    .replace(/，?规划$/g, '') // 只移除结尾的"规划"
                    .replace(/，/g, '')
                    .trim();

                console.log('🧹 初步清理后:', cleaned);

                // 处理特殊地名和标准化
                if (cleaned.includes('南口镇') || cleaned.includes('南口')) {
                    cleaned = '北京市昌平区南口镇';
                } else if (cleaned.includes('故宫')) {
                    cleaned = '北京故宫博物院';
                } else if (cleaned.includes('天安门')) {
                    cleaned = '北京天安门广场';
                } else if (cleaned.includes('颐和园')) {
                    cleaned = '北京颐和园';
                } else if (cleaned.includes('北京站')) {
                    cleaned = '北京站';
                } else if (cleaned.includes('北京西站')) {
                    cleaned = '北京西站';
                } else if (cleaned.includes('首都机场')) {
                    cleaned = '北京首都国际机场';
                } else if (cleaned.includes('大兴机场')) {
                    cleaned = '北京大兴国际机场';
                }

                // 如果地址太短或包含明显的查询词，进一步处理
                if (cleaned.length < 2 || cleaned.includes('哪里') || cleaned.includes('位置')) {
                    console.warn('⚠️ 地址可能不完整:', cleaned);
                }

                console.log('✅ 最终清理结果:', cleaned);
                return cleaned;
            }

            tryFuzzyGeocode(originalName, resolve) {
                console.log('🔄 尝试模糊匹配:', originalName);

                // 尝试不同的地址格式
                const alternatives = [
                    originalName.replace(/市|区|县|镇|街道/g, ''),
                    '北京' + originalName,
                    originalName + '北京',
                    originalName.split(/[，,]/)[0] // 取第一部分
                ];

                let attemptIndex = 0;
                const tryNext = () => {
                    if (attemptIndex >= alternatives.length) {
                        console.error('❌ 所有地理编码尝试都失败了');
                        resolve(null);
                        return;
                    }

                    const alternative = alternatives[attemptIndex++];
                    console.log('🔄 尝试备选地址:', alternative);

                    this.geocoder.getLocation(alternative, (status, result) => {
                        if (status === 'complete' && result.geocodes.length > 0) {
                            const location = result.geocodes[0];
                            console.log('✅ 模糊匹配成功:', alternative, '->', location.formattedAddress);
                            resolve(location.location);
                        } else {
                            tryNext();
                        }
                    });
                };

                tryNext();
            }

            showLocationOnMap(locationName) {
                this.geocoder.getLocation(locationName, (status, result) => {
                    if (status === 'complete' && result.geocodes.length > 0) {
                        const location = result.geocodes[0];
                        const lnglat = location.location;

                        // 清除地图
                        this.map.clearMap();

                        // 创建标记
                        const marker = new AMap.Marker({
                            position: lnglat,
                            title: locationName,
                            content: `<div style="background: #FF3B30; color: white; padding: 8px 12px; border-radius: 20px; font-size: 14px; font-weight: bold;">📍 ${locationName}</div>`
                        });

                        this.map.add(marker);
                        this.map.setCenter(lnglat);
                        this.map.setZoom(15);

                        this.updateMapStatus(`✅ 已定位到: ${locationName}`);

                        // 获取并显示位置图片
                        this.fetchDestinationImage(locationName);

                    } else {
                        this.updateMapStatus(`❌ 未找到位置: ${locationName}`);
                    }
                });
            }

            async fetchDestinationImage(locationName) {
                try {
                    console.log('🖼️ 正在获取目的地图片:', locationName);

                    // 使用高德API搜索POI获取图片
                    this.placeSearch.search(locationName, (status, result) => {
                        if (status === 'complete' && result.poiList.pois.length > 0) {
                            const poi = result.poiList.pois[0];
                            console.log('✅ 找到POI:', poi);

                            // 使用默认的地点类型图片
                            this.showDefaultDestinationImage(locationName, poi.type || '地点');
                        } else {
                            // 使用通用默认图片
                            this.showDefaultDestinationImage(locationName, '地点');
                        }
                    });

                } catch (error) {
                    console.error('❌ 获取目的地图片失败:', error);
                    this.showDefaultDestinationImage(locationName, '地点');
                }
            }

            showDestinationImage(imageUrl, locationName) {
                const img = this.elements.destinationImg;
                const label = this.elements.destinationLabel;
                const container = this.elements.destinationImage;

                img.onload = () => {
                    label.textContent = `📍 ${locationName}`;
                    container.classList.add('show');
                    console.log('✅ 目的地图片显示成功');
                };

                img.onerror = () => {
                    console.warn('⚠️ 图片加载失败，使用默认图片');
                    this.showDefaultDestinationImage(locationName, '地点');
                };

                img.src = imageUrl;
            }

            showDefaultDestinationImage(locationName, type) {
                // 根据地点类型生成默认图片URL
                const defaultImages = {
                    '景点': 'https://via.placeholder.com/200x150/007AFF/FFFFFF?text=🏛️+景点',
                    '餐厅': 'https://via.placeholder.com/200x150/34C759/FFFFFF?text=🍽️+餐厅',
                    '酒店': 'https://via.placeholder.com/200x150/FF9500/FFFFFF?text=🏨+酒店',
                    '商场': 'https://via.placeholder.com/200x150/5856D6/FFFFFF?text=🛍️+商场',
                    '医院': 'https://via.placeholder.com/200x150/FF3B30/FFFFFF?text=🏥+医院',
                    '学校': 'https://via.placeholder.com/200x150/30D158/FFFFFF?text=🏫+学校',
                    '地点': 'https://via.placeholder.com/200x150/007AFF/FFFFFF?text=📍+地点'
                };

                const imageUrl = defaultImages[type] || defaultImages['地点'];
                this.showDestinationImage(imageUrl, locationName);
            }

            showRouteInfo(distance, duration, origin, destination) {
                const routeDetails = this.elements.routeDetails;
                const routeInfo = this.elements.routeInfo;

                routeDetails.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>🛣️ ${origin} → ${destination}</strong><br>
                            <span style="font-size: 12px;">距离: ${distance}公里 · 时间: ${duration}分钟</span>
                        </div>
                        <div style="font-size: 24px;">🚗</div>
                    </div>
                `;

                routeInfo.classList.add('show');

                // 8秒后自动隐藏
                setTimeout(() => {
                    routeInfo.classList.remove('show');
                }, 8000);
            }

            addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;

                const roleText = role === 'user' ? '您' : '地图助手';

                // 清理内容，移除多余的换行和空格
                const cleanContent = content
                    .replace(/\n\s*\n/g, '\n') // 移除多余的空行
                    .replace(/^\s+|\s+$/g, '') // 移除首尾空格
                    .trim();

                messageDiv.innerHTML = `
                    <div class="message-content">
                        <strong>${roleText}:</strong><br>
                        ${cleanContent.replace(/\n/g, '<br>')}
                    </div>
                `;

                this.elements.messagesContainer.appendChild(messageDiv);

                // 确保滚动到底部
                setTimeout(() => {
                    this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
                }, 100);
            }

            addMapActionMessage(content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message map-action';
                messageDiv.innerHTML = `<div class="message-content">${content}</div>`;

                this.elements.messagesContainer.appendChild(messageDiv);
                this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
            }

            showLoading(show) {
                if (show) {
                    this.elements.loadingIndicator.classList.add('show');
                } else {
                    this.elements.loadingIndicator.classList.remove('show');
                }
            }

            updateMapStatus(message) {
                this.elements.mapStatus.textContent = message;
            }

            playEdgeTTSAudio(audioBase64, text) {
                try {
                    console.log('🎤 开始播放Edge-TTS高质量女声 (zh-CN-XiaoxiaoNeural)');
                    console.log('📊 音频数据长度:', audioBase64.length, '字符');
                    console.log('📝 播放文本:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
                    this.updateMapStatus('🎤 正在播放Edge-TTS高质量女声 (zh-CN-XiaoxiaoNeural)...');

                    const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;
                    const audio = new Audio();
                    audio.preload = 'auto';
                    audio.volume = 0.95; // 稍微提高音量

                    audio.addEventListener('loadstart', () => {
                        console.log('🎤 Edge-TTS音频开始加载');
                    });

                    audio.addEventListener('canplay', () => {
                        console.log('🎤 Edge-TTS音频可以播放');
                    });

                    audio.addEventListener('play', () => {
                        console.log('🎤 Edge-TTS高质量女声开始播放');
                        this.updateMapStatus('🎤 正在播放智能助手回答...');
                    });

                    audio.addEventListener('ended', () => {
                        console.log('✅ Edge-TTS高质量女声播放完成');
                        this.updateMapStatus('✅ 语音播放完成');
                    });

                    audio.addEventListener('error', (event) => {
                        console.error('❌ Edge-TTS音频播放失败:', event);
                        this.updateMapStatus('❌ 高质量语音播放失败');

                        // 降级到浏览器TTS，但使用清理后的文本
                        console.log('🔄 降级到浏览器TTS');
                        setTimeout(() => {
                            this.speakText(text);
                        }, 500);
                    });

                    audio.src = audioDataUrl;

                    // 尝试自动播放
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            console.log('✅ Edge-TTS高质量女声播放启动成功');
                        }).catch(error => {
                            console.error('❌ Edge-TTS音频播放启动失败:', error);
                            this.updateMapStatus('⚠️ 请点击页面后重试语音播放');

                            // 降级到浏览器TTS
                            setTimeout(() => {
                                this.speakText(text);
                            }, 1000);
                        });
                    }

                } catch (error) {
                    console.error('❌ Edge-TTS音频处理失败:', error);
                    this.updateMapStatus('❌ 音频处理失败');
                    this.speakText(text);
                }
            }

            speakText(text) {
                if (!window.speechSynthesis) {
                    console.warn('⚠️ 浏览器不支持语音合成');
                    return;
                }

                // 清理文本，移除符号和特殊字符
                const cleanText = this.cleanTextForTTS(text);
                console.log('🔊 使用浏览器TTS播放:', cleanText.substring(0, 50) + '...');
                this.updateMapStatus('🔊 正在播放语音...');

                window.speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(cleanText);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.9; // 稍微快一点
                utterance.pitch = 1.2; // 更高的音调，更像女声
                utterance.volume = 0.9;

                // 尝试选择更好的女声
                const voices = window.speechSynthesis.getVoices();
                const femaleVoice = voices.find(voice =>
                    voice.lang.includes('zh') &&
                    (voice.name.includes('Female') || voice.name.includes('女') || voice.name.includes('Xiaoxiao'))
                );

                if (femaleVoice) {
                    utterance.voice = femaleVoice;
                    console.log('🎤 使用女声:', femaleVoice.name);
                }

                utterance.onstart = () => {
                    console.log('🔊 浏览器TTS开始播放');
                    this.updateMapStatus('🔊 正在播放语音回答...');
                };

                utterance.onend = () => {
                    console.log('✅ 浏览器TTS播放完成');
                    this.updateMapStatus('✅ 语音播放完成');
                };

                utterance.onerror = (event) => {
                    console.error('❌ 浏览器TTS错误:', event);
                    this.updateMapStatus('❌ 语音播放失败');
                };

                window.speechSynthesis.speak(utterance);
            }

            cleanTextForTTS(text) {
                console.log('🧹 开始清理TTS文本:', text.substring(0, 100) + '...');

                // 清理文本，移除不适合语音播放的内容
                let cleanText = text
                    // 移除所有emoji和特殊符号（更全面的清理）
                    .replace(/[\u{1F600}-\u{1F64F}]/gu, '') // 表情符号
                    .replace(/[\u{1F300}-\u{1F5FF}]/gu, '') // 杂项符号和象形文字
                    .replace(/[\u{1F680}-\u{1F6FF}]/gu, '') // 交通和地图符号
                    .replace(/[\u{1F1E0}-\u{1F1FF}]/gu, '') // 国旗
                    .replace(/[\u{2600}-\u{26FF}]/gu, '')   // 杂项符号
                    .replace(/[\u{2700}-\u{27BF}]/gu, '')   // 装饰符号
                    .replace(/[🗺️🛣️📍🎉✅❌⚠️🔍🚗🎤🔊💬📏⏱️💰🚦🏛️🍽️🏨🛍️🏥🏫📱🌟🎯🔧💄🤖]/g, '')

                    // 移除markdown和格式符号
                    .replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
                    .replace(/\*(.*?)\*/g, '$1') // 斜体
                    .replace(/__(.*?)__/g, '$1') // 下划线粗体
                    .replace(/_(.*?)_/g, '$1') // 下划线斜体
                    .replace(/`(.*?)`/g, '$1') // 代码
                    .replace(/```[\s\S]*?```/g, '') // 代码块

                    // 移除特殊标点和符号
                    .replace(/[•·▪▫◦‣⁃]/g, '') // 各种项目符号
                    .replace(/[【】〖〗〔〕]/g, '') // 中文方括号
                    .replace(/[（）]/g, '') // 中文圆括号
                    .replace(/[""'']/g, '') // 中文引号
                    .replace(/[→←↑↓↔]/g, '到') // 箭头符号替换为"到"
                    .replace(/[①②③④⑤⑥⑦⑧⑨⑩]/g, '') // 圆圈数字
                    .replace(/[⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑]/g, '') // 括号数字

                    // 移除特殊格式标记
                    .replace(/^\s*[-*+]\s+/gm, '') // 列表标记
                    .replace(/^\s*\d+\.\s*/gm, '') // 数字列表
                    .replace(/^\s*[a-zA-Z]\.\s*/gm, '') // 字母列表
                    .replace(/^\s*[ivxlcdm]+\.\s*/gmi, '') // 罗马数字列表

                    // 移除开发术语和技术词汇
                    .replace(/知识图谱|KG|Knowledge Graph/g, '')
                    .replace(/数据库|Database|DB/g, '')
                    .replace(/向量数据库|Vector Database/g, '')
                    .replace(/RAG|检索增强生成/g, '')
                    .replace(/LLM|大语言模型/g, '')
                    .replace(/API|接口/g, '')
                    .replace(/JSON|XML|HTML/g, '')
                    .replace(/SQL|查询语句/g, '')
                    .replace(/实体|关系|Entity|Relationship/g, '')
                    .replace(/嵌入|Embedding|向量/g, '')
                    .replace(/索引|Index|检索/g, '')
                    .replace(/算法|Algorithm/g, '')
                    .replace(/模型|Model/g, '')
                    .replace(/训练|Training/g, '')
                    .replace(/推理|Inference/g, '')
                    .replace(/参数|Parameter/g, '')
                    .replace(/配置|Config|Configuration/g, '')
                    .replace(/框架|Framework/g, '')
                    .replace(/库|Library/g, '')
                    .replace(/依赖|Dependency/g, '')
                    .replace(/基于.*?的/g, '基于智能分析的')

                    // 移除网址和邮箱
                    .replace(/https?:\/\/[^\s]+/g, '')
                    .replace(/www\.[^\s]+/g, '')
                    .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '')

                    // 移除HTML标签
                    .replace(/<[^>]*>/g, '')

                    // 清理空格和换行
                    .replace(/\s+/g, ' ') // 多个空格变成一个
                    .replace(/\n+/g, '。') // 换行变成句号
                    .replace(/\r/g, '') // 移除回车符

                    // 移除多余的标点
                    .replace(/[。]{2,}/g, '。') // 多个句号变成一个
                    .replace(/[，]{2,}/g, '，') // 多个逗号变成一个
                    .replace(/[！]{2,}/g, '！') // 多个感叹号变成一个
                    .replace(/[？]{2,}/g, '？') // 多个问号变成一个

                    .trim();

                // 移除开头和结尾的特殊字符
                cleanText = cleanText.replace(/^[^\u4e00-\u9fa5a-zA-Z0-9]+/, '');
                cleanText = cleanText.replace(/[^\u4e00-\u9fa5a-zA-Z0-9。！？，]+$/, '');

                // 如果文本太长，智能截取
                if (cleanText.length > 400) {
                    // 找到最后一个句号的位置
                    const lastPeriod = cleanText.lastIndexOf('。', 400);
                    if (lastPeriod > 200) {
                        cleanText = cleanText.substring(0, lastPeriod + 1);
                    } else {
                        cleanText = cleanText.substring(0, 400) + '。';
                    }
                    cleanText += '详细信息请查看文字回答。';
                }

                // 如果清理后文本太短或为空，提供默认文本
                if (cleanText.length < 5) {
                    cleanText = '已为您处理完成，请查看详细信息。';
                }

                console.log('✅ TTS文本清理完成:', cleanText.substring(0, 100) + '...');
                return cleanText;
            }

            playAudio(audioBase64) {
                // 兼容旧的调用方式
                this.playEdgeTTSAudio(audioBase64, '');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🗺️ 启动增强版智能地图交互系统...');
            window.enhancedMapChat = new EnhancedMapChat();
        });
    </script>
</body>
</html>
