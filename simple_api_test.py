#!/usr/bin/env python
"""
简单的阿里云百炼API测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed

async def test_llm_api():
    """测试LLM API"""
    print("🧪 测试LLM API...")
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    try:
        response = await openai_complete_if_cache(
            "qwen-turbo",
            "你好，请简单介绍一下人工智能",
            api_key=api_key,
            base_url=base_url,
            max_tokens=100
        )
        print(f"✅ LLM API测试成功!")
        print(f"📝 响应: {response}")
        return True
    except Exception as e:
        print(f"❌ LLM API测试失败: {e}")
        return False

async def test_embedding_api():
    """测试Embedding API"""
    print("\n🧪 测试Embedding API...")
    
    api_key = "sk-c7b965ee5fc64ab482174967dabd4805"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    try:
        embeddings = await openai_embed(
            ["人工智能", "机器学习"],
            model="text-embedding-v1",
            api_key=api_key,
            base_url=base_url
        )
        print(f"✅ Embedding API测试成功!")
        print(f"📝 嵌入向量维度: {len(embeddings[0])}")
        print(f"📝 向量数量: {len(embeddings)}")
        return True
    except Exception as e:
        print(f"❌ Embedding API测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 简单API测试")
    print("=" * 30)
    
    # 测试LLM
    llm_success = await test_llm_api()
    
    # 测试Embedding
    embedding_success = await test_embedding_api()
    
    # 总结
    print("\n" + "=" * 30)
    print("📊 测试结果:")
    print(f"  🤖 LLM API: {'✅ 成功' if llm_success else '❌ 失败'}")
    print(f"  🔢 Embedding API: {'✅ 成功' if embedding_success else '❌ 失败'}")
    
    if llm_success and embedding_success:
        print("\n🎉 所有API测试通过！您的配置正确。")
    else:
        print("\n❌ 部分API测试失败，请检查配置。")

if __name__ == "__main__":
    asyncio.run(main())
