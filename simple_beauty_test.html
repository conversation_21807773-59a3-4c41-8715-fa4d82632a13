<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化美女测试</title>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        #canvas {
            width: 600px;
            height: 400px;
            border: 2px solid #007AFF;
            border-radius: 10px;
            background: #000;
            margin: 20px auto;
            display: block;
        }
        
        .info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007AFF;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056CC;
        }
        
        #log {
            text-align: left;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 简化美女3D测试</h1>
        
        <div class="info">
            <p>这是一个简化的3D美女测试页面，用于诊断显示问题</p>
            <p>状态: <span id="status">初始化中...</span></p>
        </div>
        
        <canvas id="canvas"></canvas>
        
        <div>
            <button onclick="createBeauty()">创建美女</button>
            <button onclick="resetCamera()">重置视角</button>
            <button onclick="toggleAnimation()">切换动画</button>
        </div>
        
        <div id="log"></div>
    </div>

    <script>
        let scene, camera, renderer, beauty, animationId;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function init() {
            log('🚀 开始初始化Three.js...');
            
            const canvas = document.getElementById('canvas');
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(45, canvas.width / canvas.height, 0.1, 1000);
            camera.position.set(0, 0, 3);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(canvas.width, canvas.height);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // 添加灯光
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            log('✅ Three.js初始化完成');
            updateStatus('Three.js已就绪');
            
            // 自动创建美女
            createBeauty();
            
            // 开始渲染循环
            animate();
        }
        
        function createBeauty() {
            log('🌟 开始创建3D美女...');
            updateStatus('正在创建美女...');
            
            // 移除现有美女
            if (beauty) {
                scene.remove(beauty);
            }
            
            // 创建美女组
            beauty = new THREE.Group();
            
            // 头部
            const headGeometry = new THREE.SphereGeometry(0.4, 64, 64);
            const headMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xffdbac,
                roughness: 0.6,
                metalness: 0.0,
                transmission: 0.05,
                thickness: 0.5
            });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 0;
            head.castShadow = true;
            beauty.add(head);
            
            // 眼睛
            const eyeGeometry = new THREE.SphereGeometry(0.05, 32, 32);
            const eyeMaterial = new THREE.MeshStandardMaterial({ color: 0x4a90e2 });
            
            const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            leftEye.position.set(-0.12, 0.1, 0.35);
            beauty.add(leftEye);
            
            const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            rightEye.position.set(0.12, 0.1, 0.35);
            beauty.add(rightEye);
            
            // 嘴巴
            const mouthGeometry = new THREE.SphereGeometry(0.03, 16, 16);
            const mouthMaterial = new THREE.MeshStandardMaterial({ color: 0xd4756b });
            const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
            mouth.position.set(0, -0.1, 0.35);
            mouth.scale.set(2, 1, 1);
            beauty.add(mouth);
            
            // 鼻子
            const noseGeometry = new THREE.ConeGeometry(0.02, 0.06, 8);
            const noseMaterial = new THREE.MeshStandardMaterial({ color: 0xffdbac });
            const nose = new THREE.Mesh(noseGeometry, noseMaterial);
            nose.position.set(0, 0, 0.38);
            nose.rotation.x = Math.PI;
            beauty.add(nose);
            
            // 头发
            const hairGeometry = new THREE.SphereGeometry(0.45, 32, 32);
            const hairMaterial = new THREE.MeshStandardMaterial({ color: 0x8b4513 });
            const hair = new THREE.Mesh(hairGeometry, hairMaterial);
            hair.position.set(0, 0.1, -0.1);
            hair.scale.set(1, 1.2, 0.8);
            beauty.add(hair);
            
            // 设置位置
            beauty.position.set(0, 0, 0);
            beauty.scale.set(1, 1, 1);
            
            // 添加到场景
            scene.add(beauty);
            
            log('✅ 3D美女创建完成');
            updateStatus('美女已创建');
            
            // 保存引用用于动画
            beauty.userData = {
                head: head,
                leftEye: leftEye,
                rightEye: rightEye,
                mouth: mouth,
                hair: hair
            };
        }
        
        function resetCamera() {
            camera.position.set(0, 0, 3);
            camera.lookAt(0, 0, 0);
            log('📷 相机视角已重置');
        }
        
        function toggleAnimation() {
            if (beauty) {
                // 简单的旋转动画
                beauty.rotation.y += 0.1;
                log('🎭 美女旋转了一下');
            }
        }
        
        function animate() {
            animationId = requestAnimationFrame(animate);
            
            // 简单的呼吸动画
            if (beauty) {
                const time = Date.now() * 0.001;
                beauty.scale.y = 1 + Math.sin(time * 2) * 0.02;
                beauty.rotation.y = Math.sin(time * 0.5) * 0.05;
            }
            
            renderer.render(scene, camera);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            log('📱 页面加载完成');
            init();
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('canvas');
            camera.aspect = canvas.width / canvas.height;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.width, canvas.height);
        });
    </script>
</body>
</html>
