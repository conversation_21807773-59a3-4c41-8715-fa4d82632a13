#!/usr/bin/env python3
"""
临时目录修复脚本

解决MinerU运行时的临时目录权限问题
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path


def check_temp_directories():
    """检查系统临时目录状态"""
    print("🔍 检查系统临时目录状态...")
    
    temp_dirs = [
        os.environ.get('TMPDIR', ''),
        '/tmp',
        '/var/tmp', 
        '/usr/tmp',
        os.getcwd()
    ]
    
    for temp_dir in temp_dirs:
        if not temp_dir:
            continue
            
        print(f"检查目录: {temp_dir}")
        
        if os.path.exists(temp_dir):
            try:
                # 测试写入权限
                test_file = os.path.join(temp_dir, 'test_write_permission.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                print(f"  ✅ 可写入: {temp_dir}")
            except Exception as e:
                print(f"  ❌ 无法写入: {temp_dir} - {e}")
        else:
            print(f"  ❌ 不存在: {temp_dir}")


def fix_temp_directory():
    """修复临时目录问题"""
    print("\n🛠️ 开始修复临时目录...")
    
    try:
        # 方案1: 创建项目专用临时目录
        project_temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(project_temp_dir, exist_ok=True)
        
        # 设置权限
        os.chmod(project_temp_dir, 0o755)
        
        print(f"✅ 创建项目临时目录: {project_temp_dir}")
        
        # 方案2: 设置环境变量
        env_vars = {
            'TMPDIR': project_temp_dir,
            'TMP': project_temp_dir, 
            'TEMP': project_temp_dir,
            'TEMPDIR': project_temp_dir
        }
        
        for var, value in env_vars.items():
            os.environ[var] = value
            print(f"✅ 设置环境变量: {var}={value}")
        
        # 方案3: 验证Python tempfile模块
        try:
            test_file = tempfile.NamedTemporaryFile(delete=True)
            test_file.close()
            print("✅ Python tempfile模块工作正常")
        except Exception as e:
            print(f"❌ Python tempfile模块测试失败: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 临时目录修复失败: {e}")
        return False


def create_temp_fix_script():
    """创建临时目录修复的shell脚本"""
    script_content = '''#!/bin/bash
# 临时目录修复脚本

echo "🛠️ 修复临时目录权限问题..."

# 创建项目临时目录
PROJECT_TEMP_DIR="$(pwd)/temp"
mkdir -p "$PROJECT_TEMP_DIR"
chmod 755 "$PROJECT_TEMP_DIR"

# 设置环境变量
export TMPDIR="$PROJECT_TEMP_DIR"
export TMP="$PROJECT_TEMP_DIR"
export TEMP="$PROJECT_TEMP_DIR"
export TEMPDIR="$PROJECT_TEMP_DIR"

echo "✅ 临时目录已设置: $PROJECT_TEMP_DIR"

# 运行RAGAnything
echo "🚀 启动RAGAnything..."
python examples/raganything_example.py "$@"
'''
    
    script_path = "run_with_temp_fix.sh"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    print(f"✅ 创建修复脚本: {script_path}")


def clean_temp_directories():
    """清理临时目录"""
    print("\n🧹 清理临时目录...")
    
    temp_dir = os.path.join(os.getcwd(), "temp")
    if os.path.exists(temp_dir):
        try:
            shutil.rmtree(temp_dir)
            print(f"✅ 清理完成: {temp_dir}")
        except Exception as e:
            print(f"❌ 清理失败: {e}")


def main():
    """主函数"""
    print("RAGAnything 临时目录修复工具")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "check":
            check_temp_directories()
        elif command == "fix":
            if fix_temp_directory():
                print("\n✅ 临时目录修复完成！")
                print("现在可以运行 RAGAnything 了")
            else:
                print("\n❌ 临时目录修复失败")
                sys.exit(1)
        elif command == "script":
            create_temp_fix_script()
        elif command == "clean":
            clean_temp_directories()
        else:
            print(f"未知命令: {command}")
            print("可用命令: check, fix, script, clean")
    else:
        print("用法:")
        print("  python fix_temp_directory.py check   # 检查临时目录状态")
        print("  python fix_temp_directory.py fix     # 修复临时目录问题")
        print("  python fix_temp_directory.py script  # 创建修复脚本")
        print("  python fix_temp_directory.py clean   # 清理临时目录")


if __name__ == "__main__":
    main()
