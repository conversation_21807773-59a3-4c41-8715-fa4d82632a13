@echo off
chcp 65001 >nul
echo 🚀 RAGAnything Windows运行脚本
echo 基于阿里云百炼API的多模态RAG系统
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Python未安装或未添加到PATH
    echo 请从 https://www.python.org/downloads/ 下载并安装Python
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo ⚠️ 虚拟环境不存在，正在创建...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查是否有.env文件
if not exist ".env" (
    echo ⚠️ .env文件不存在，请先配置API密钥
    echo 创建.env文件模板...
    (
        echo # 阿里云百炼API配置
        echo DASHSCOPE_API_KEY=sk-your-api-key-here
        echo OPENAI_API_KEY=sk-your-api-key-here
        echo OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
        echo.
        echo # LLM配置
        echo LLM_BINDING=openai
        echo LLM_MODEL=qwen-turbo
        echo LLM_BINDING_HOST=https://dashscope.aliyuncs.com/compatible-mode/v1
        echo LLM_BINDING_API_KEY=sk-your-api-key-here
        echo.
        echo # Embedding配置
        echo EMBEDDING_BINDING=openai
        echo EMBEDDING_MODEL=text-embedding-v1
        echo EMBEDDING_DIM=1536
        echo EMBEDDING_BINDING_API_KEY=sk-your-api-key-here
        echo EMBEDDING_BINDING_HOST=https://dashscope.aliyuncs.com/compatible-mode/v1
        echo.
        echo # Windows GPU配置
        echo MINERU_DEVICE=cuda
        echo PYTORCH_ENABLE_MPS_FALLBACK=0
    ) > .env
    echo ✅ .env模板已创建，请编辑并填入您的API密钥
    echo 📝 请编辑.env文件，将 sk-your-api-key-here 替换为您的真实API密钥
    pause
    exit /b 1
)

REM 加载环境变量
for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

REM 检查API密钥
if "%DASHSCOPE_API_KEY%"=="sk-your-api-key-here" (
    echo ❌ 请在.env文件中配置您的真实API密钥
    pause
    exit /b 1
)

if "%DASHSCOPE_API_KEY%"=="" (
    echo ❌ 未找到API密钥，请检查.env文件配置
    pause
    exit /b 1
)

REM 检查参数
if "%1"=="" (
    echo ❌ 请提供文档路径
    echo 用法: run_windows.bat 文档路径
    echo 示例: run_windows.bat documents\sample.pdf
    echo 示例: run_windows.bat "C:\Users\<USER>\Documents\test.pdf"
    pause
    exit /b 1
)

REM 检查文档文件是否存在
if not exist "%1" (
    echo ❌ 文档文件不存在: %1
    pause
    exit /b 1
)

echo 📄 处理文档: %1
echo 🔑 API密钥: %DASHSCOPE_API_KEY:~0,8%...%DASHSCOPE_API_KEY:~-4%
echo 🌐 API端点: %OPENAI_BASE_URL%

REM 检查CUDA支持
echo 🔍 检查GPU支持...
python -c "import torch; print('CUDA可用:', torch.cuda.is_available()); print('GPU设备:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A')" 2>nul
if errorlevel 1 (
    echo ⚠️ PyTorch未安装，正在安装依赖...
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
    if errorlevel 1 (
        echo ❌ PyTorch安装失败，尝试CPU版本...
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    )
)

REM 安装其他依赖
echo 🔧 检查依赖包...
pip install openai dashscope requests >nul 2>&1

REM 创建输出目录
if not exist "output" mkdir output
if not exist "rag_storage" mkdir rag_storage

echo 🚀 启动RAGAnything...
echo ========================================

REM 运行脚本
python examples/raganything_example_windows.py "%1" --device auto

if errorlevel 1 (
    echo ❌ 处理失败，请检查错误信息
) else (
    echo ✅ 处理完成！
    echo 📁 结果保存在 output 目录中
)

echo ========================================
pause
