# RAGAnything 问答模式运行命令总结

## 🚀 核心启动命令

### 方法1：启动脚本（推荐）
# Windows
run_qa_mode.bat "document.pdf"
run_qa_mode.bat --existing
run_qa_mode.bat --session "session.json"

# Linux/Mac
./run_qa_mode.sh "document.pdf"
./run_qa_mode.sh --existing
./run_qa_mode.sh --session "session.json"

### 方法2：Python脚本
# 基础命令
python examples/qa_mode_example.py "document.pdf" --api-key sk-your-api-key-here

# 完整参数
python examples/qa_mode_example.py "document.pdf" \
    --working-dir "./rag_storage" \
    --output "./output" \
    --api-key "sk-your-api-key-here" \
    --base-url "https://dashscope.aliyuncs.com/compatible-mode/v1" \
    --device "auto" \
    --verbose

# 使用现有知识库
python examples/qa_mode_example.py --working-dir ./rag_storage --api-key sk-your-api-key-here

# 加载会话
python examples/qa_mode_example.py --working-dir ./rag_storage --session "session.json" --api-key sk-your-api-key-here

### 方法3：集成模式
# 在原有脚本中启用问答模式
python examples/raganything_example.py "document.pdf" --qa-mode --api-key sk-your-api-key-here

## 🔧 设备特定命令

### GPU加速
# NVIDIA CUDA
python examples/qa_mode_example.py "document.pdf" --device cuda --api-key sk-your-api-key-here

# Apple MPS
python examples/qa_mode_example.py "document.pdf" --device mps --api-key sk-your-api-key-here

# CPU模式
python examples/qa_mode_example.py "document.pdf" --device cpu --api-key sk-your-api-key-here

## 📄 不同文档类型

### PDF文档
./run_qa_mode.sh "documents/research_paper.pdf"
./run_qa_mode.sh "reports/annual_report.pdf"

### Office文档
./run_qa_mode.sh "documents/proposal.docx"
./run_qa_mode.sh "presentations/demo.pptx"
./run_qa_mode.sh "data/analysis.xlsx"

### 图像文件
./run_qa_mode.sh "images/chart.png"
./run_qa_mode.sh "scans/document.jpg"

## 🎨 多模态查询

### 启动后的交互
# 基础查询
这个文档的主要内容是什么？

# 多模态查询
分析这个销售数据表格
解释这个性能图表
这个公式的含义是什么？

# 系统命令
/help          # 显示帮助
/mode          # 切换查询模式
/history       # 查看对话历史
/stats         # 查看统计信息
/context       # 切换上下文模式
/save          # 保存会话
/load          # 加载会话
/clear         # 清空历史
/multimodal    # 多模态帮助
/exit          # 退出

## 📊 会话管理

### 保存会话
# 在问答模式中
/save my_session.json
/save research_session_20241218.json

### 加载会话
./run_qa_mode.sh --session "my_session.json"
python examples/qa_mode_example.py --session "research_session.json" --api-key sk-your-api-key-here

## 🔍 查询模式

### 模式切换
# 在问答模式中输入
/mode

# 可选模式：
# hybrid  - 混合检索（推荐）
# local   - 局部检索
# global  - 全局检索
# naive   - 朴素检索

## 🧪 测试命令

### 功能测试
python test_qa_mode.py

### 演示脚本
python demo_qa_mode.py

### API连接测试
python -c "
import requests
import os
api_key = os.getenv('DASHSCOPE_API_KEY')
headers = {'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'}
data = {'model': 'qwen-turbo', 'messages': [{'role': 'user', 'content': '测试'}], 'max_tokens': 10}
response = requests.post('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', headers=headers, json=data)
print('API状态:', response.status_code)
"

## 🔧 环境配置

### 安装依赖
pip install openai dashscope requests torch

### 配置API密钥
# 方法1：环境变量
export DASHSCOPE_API_KEY=sk-your-api-key-here

# 方法2：.env文件
echo "DASHSCOPE_API_KEY=sk-your-api-key-here" > .env
echo "OPENAI_API_KEY=sk-your-api-key-here" >> .env
echo "OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1" >> .env

### 权限设置（Linux/Mac）
chmod +x run_qa_mode.sh

## 🎯 实用场景命令

### 学术研究
./run_qa_mode.sh "papers/ml_survey.pdf"
# Q: 这篇论文的主要贡献是什么？
# Q: 实验结果如何？

### 企业分析
./run_qa_mode.sh "reports/quarterly_report.pdf"
# Q: 本季度营收如何？
# Q: 分析这个财务数据表格

### 技术文档
./run_qa_mode.sh "docs/api_guide.pdf"
# Q: 如何使用这个API？
# Q: 有哪些端点可用？

### 法律文档
./run_qa_mode.sh "contracts/agreement.pdf"
# Q: 合同的主要条款是什么？
# Q: 违约责任如何规定？

## 🚨 故障排除

### 检查环境
python -c "
import os
print('API Key:', 'Set' if os.getenv('DASHSCOPE_API_KEY') else 'Not set')
try:
    import torch
    print('PyTorch:', torch.__version__)
    print('CUDA:', torch.cuda.is_available())
    print('MPS:', torch.backends.mps.is_available())
except ImportError:
    print('PyTorch: Not installed')
"

### 清理缓存
rm -rf ./rag_storage/
rm -rf ./output/
rm -f *.json
mkdir -p rag_storage output

## 📱 一键运行模板

### 创建快速启动脚本
# Linux/Mac (save as start_qa.sh)
#!/bin/bash
export DASHSCOPE_API_KEY="sk-your-api-key-here"
source venv/bin/activate
./run_qa_mode.sh "$1"

# Windows (save as start_qa.bat)
@echo off
set DASHSCOPE_API_KEY=sk-your-api-key-here
call venv\Scripts\activate.bat
run_qa_mode.bat %1

### 使用快速启动
chmod +x start_qa.sh
./start_qa.sh "your_document.pdf"

## 🎉 最简启动命令

# 确保已配置API密钥后，最简单的启动方式：
./run_qa_mode.sh "your_document.pdf"

# 或者
run_qa_mode.bat "your_document.pdf"
