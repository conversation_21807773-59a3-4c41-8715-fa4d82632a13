@echo off
chcp 65001 >nul
echo 🤖 RAGAnything 交互式问答模式启动脚本
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Python未安装或未添加到PATH
    echo 请从 https://www.python.org/downloads/ 下载并安装Python
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo ⚠️ 虚拟环境不存在，正在创建...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查是否有.env文件
if not exist ".env" (
    echo ⚠️ .env文件不存在，请先配置API密钥
    echo 创建.env文件模板...
    (
        echo # 阿里云百炼API配置
        echo DASHSCOPE_API_KEY=sk-your-api-key-here
        echo OPENAI_API_KEY=sk-your-api-key-here
        echo OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
    ) > .env
    echo ✅ .env模板已创建，请编辑并填入您的API密钥
    echo 📝 请编辑.env文件，将 sk-your-api-key-here 替换为您的真实API密钥
    pause
    exit /b 1
)

REM 加载环境变量
for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

REM 检查API密钥
if "%DASHSCOPE_API_KEY%"=="sk-your-api-key-here" (
    echo ❌ 请在.env文件中配置您的真实API密钥
    pause
    exit /b 1
)

if "%DASHSCOPE_API_KEY%"=="" (
    echo ❌ 未找到API密钥，请检查.env文件配置
    pause
    exit /b 1
)

echo 🔑 API密钥: %DASHSCOPE_API_KEY:~0,8%...%DASHSCOPE_API_KEY:~-4%
echo 🌐 API端点: %OPENAI_BASE_URL%

REM 安装依赖
echo 🔧 检查依赖包...
pip install openai dashscope requests >nul 2>&1

REM 创建必要目录
if not exist "rag_storage" mkdir rag_storage

echo 🚀 启动RAGAnything问答模式...
echo ========================================

REM 检查参数
if "%1"=="" (
    echo 📋 使用方法:
    echo   1. 处理新文档并启动问答: run_qa_mode.bat document.pdf
    echo   2. 使用现有知识库启动问答: run_qa_mode.bat --existing
    echo   3. 加载会话文件: run_qa_mode.bat --session session.json
    echo.
    set /p choice="请选择模式 (1/2/3): "
    
    if "%choice%"=="1" (
        set /p doc_path="请输入文档路径: "
        if not exist "!doc_path!" (
            echo ❌ 文档文件不存在
            pause
            exit /b 1
        )
        python examples/qa_mode_example.py "!doc_path!" --api-key %DASHSCOPE_API_KEY%
    ) else if "%choice%"=="2" (
        python examples/qa_mode_example.py --working-dir ./rag_storage --api-key %DASHSCOPE_API_KEY%
    ) else if "%choice%"=="3" (
        set /p session_file="请输入会话文件路径: "
        python examples/qa_mode_example.py --working-dir ./rag_storage --session "!session_file!" --api-key %DASHSCOPE_API_KEY%
    ) else (
        echo ❌ 无效选择
        pause
        exit /b 1
    )
) else if "%1"=="--existing" (
    REM 使用现有知识库
    echo 📚 使用现有知识库启动问答模式...
    python examples/qa_mode_example.py --working-dir ./rag_storage --api-key %DASHSCOPE_API_KEY%
) else if "%1"=="--session" (
    REM 加载会话
    if "%2"=="" (
        echo ❌ 请提供会话文件路径
        echo 用法: run_qa_mode.bat --session session.json
        pause
        exit /b 1
    )
    echo 📂 加载会话文件: %2
    python examples/qa_mode_example.py --working-dir ./rag_storage --session "%2" --api-key %DASHSCOPE_API_KEY%
) else (
    REM 处理文档
    if not exist "%1" (
        echo ❌ 文档文件不存在: %1
        pause
        exit /b 1
    )
    echo 📄 处理文档: %1
    python examples/qa_mode_example.py "%1" --api-key %DASHSCOPE_API_KEY%
)

if errorlevel 1 (
    echo ❌ 问答模式启动失败，请检查错误信息
) else (
    echo ✅ 问答模式已结束
)

echo ========================================
pause
