#!/usr/bin/env python
"""
高德地图API接入模块
提供地理编码、路线规划、POI搜索等功能
"""

import os
import json
import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from urllib.parse import urlencode

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Location:
    """位置信息"""
    longitude: float  # 经度
    latitude: float   # 纬度
    name: str = ""    # 地点名称
    address: str = "" # 详细地址

@dataclass
class RouteInfo:
    """路线信息"""
    distance: str      # 距离
    duration: str      # 时间
    tolls: str        # 过路费
    traffic_lights: int # 红绿灯数量
    steps: List[str]   # 路线步骤
    polyline: str     # 路线坐标串
    raw_data: dict = None  # 原始API响应数据

@dataclass
class POIInfo:
    """POI信息"""
    id: str           # POI ID
    name: str         # 名称
    type: str         # 类型
    address: str      # 地址
    location: Location # 位置
    distance: str     # 距离
    tel: str = ""     # 电话
    business_area: str = ""  # 商圈

@dataclass
class DistrictInfo:
    """行政区域信息"""
    adcode: str       # 区域编码
    name: str         # 区域名称
    center: Location  # 中心点
    level: str        # 级别
    polyline: str = "" # 边界坐标

@dataclass
class WeatherInfo:
    """天气信息"""
    province: str     # 省份
    city: str         # 城市
    adcode: str       # 区域编码
    weather: str      # 天气现象
    temperature: str  # 温度
    winddirection: str # 风向
    windpower: str    # 风力
    humidity: str     # 湿度
    reporttime: str   # 报告时间
    temperature_float: str = ""  # 温度浮点数
    humidity_float: str = ""     # 湿度浮点数

class AmapAPI:
    """高德地图API封装类"""

    # API基础URL
    BASE_URL = "https://restapi.amap.com"

    # API端点
    ENDPOINTS = {
        'geocode': '/v3/geocode/geo',           # 地理编码
        'regeo': '/v3/geocode/regeo',          # 逆地理编码
        'poi_search': '/v3/place/text',        # POI搜索
        'poi_around': '/v3/place/around',      # 周边搜索
        'district': '/v3/config/district',     # 行政区域查询
        'direction_driving': '/v3/direction/driving',    # 驾车路径规划
        'direction_walking': '/v3/direction/walking',    # 步行路径规划
        'direction_bicycling': '/v3/direction/bicycling', # 骑行路径规划
        'direction_transit': '/v3/direction/transit/integrated', # 公交路径规划
        'distance': '/v3/distance',            # 距离测量
        'ip_location': '/v3/ip',               # IP定位
        'location': '/v5/locationsearch',      # 在线定位
        'weather_base': '/v3/weather/weatherInfo',  # 实况天气
        'weather_forecast': '/v3/weather/weatherInfo'  # 天气预报
    }

    def __init__(self, api_key: str = None):
        """
        初始化高德地图API

        Args:
            api_key: 高德地图API密钥
        """
        self.api_key = api_key or os.getenv("AMAP_API_KEY", "e4203c466e89130a43daedd0ae9f4368")
        if not self.api_key:
            raise ValueError("请设置AMAP_API_KEY环境变量或传入api_key参数")
        
        self.base_url = "https://restapi.amap.com/v3"
        self.session = None
        
        logger.info(f"🗺️ 高德地图API初始化成功，密钥: {self.api_key[:8]}...")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发起API请求
        
        Args:
            endpoint: API端点
            params: 请求参数
            
        Returns:
            API响应数据
        """
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        # 添加API密钥
        params["key"] = self.api_key
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                if data.get("status") == "1":
                    return data
                else:
                    error_msg = data.get("info", "未知错误")
                    logger.error(f"❌ 高德API请求失败: {error_msg}")
                    raise Exception(f"高德API错误: {error_msg}")
                    
        except Exception as e:
            logger.error(f"❌ API请求异常: {e}")
            raise

    def clean_address_for_geocoding(self, address: str) -> str:
        """清理地址用于地理编码"""
        # 移除查询词汇，只在结尾移除
        cleaned = address
        cleaned = cleaned.rstrip('怎么走')
        cleaned = cleaned.rstrip('的路线')
        cleaned = cleaned.rstrip('路线')
        cleaned = cleaned.rstrip('规划路线')
        cleaned = cleaned.rstrip('规划')
        cleaned = cleaned.strip()

        # 标准化常见地名 - 保持原有逻辑但不强制添加北京
        if '故宫' in cleaned and '北京' not in cleaned:
            cleaned = '北京故宫博物院'
        elif '天安门' in cleaned and '北京' not in cleaned:
            cleaned = '北京天安门广场'
        elif '颐和园' in cleaned and '北京' not in cleaned:
            cleaned = '北京颐和园'

        # 对于镇、区、县等行政区划，保持原样不添加前缀
        # 这样可以让高德API自动识别全国范围内的地名

        logger.info(f"🧹 地址清理: {address} -> {cleaned}")
        return cleaned

    async def geocode(self, address: str, city: str = "") -> Optional[Location]:
        """
        地理编码 - 地址转坐标

        Args:
            address: 地址
            city: 城市（可选，提高精度）

        Returns:
            位置信息
        """
        # 清理地址
        cleaned_address = self.clean_address_for_geocoding(address)
        logger.info(f"🔍 地理编码查询: {cleaned_address}")

        params = {
            "address": cleaned_address,
            "output": "json",
            "key": self.api_key
        }

        if city:
            params["city"] = city
        # 移除自动添加北京的逻辑，让高德API自动识别地址所在城市
        
        try:
            data = await self._make_request("geocode/geo", params)
            
            geocodes = data.get("geocodes", [])
            if not geocodes:
                logger.warning(f"⚠️ 未找到地址: {address}")
                return None
            
            geocode = geocodes[0]
            location_str = geocode.get("location", "")
            
            if not location_str:
                return None
            
            lng, lat = map(float, location_str.split(","))
            
            location = Location(
                longitude=lng,
                latitude=lat,
                name=geocode.get("formatted_address", address),
                address=geocode.get("formatted_address", address)
            )
            
            logger.info(f"✅ 地理编码成功: {location.name} -> ({lng}, {lat})")
            return location
            
        except Exception as e:
            logger.error(f"❌ 地理编码失败: {e}")
            return None

    async def regeocode(self, longitude: float, latitude: float) -> Optional[str]:
        """
        逆地理编码 - 坐标转地址
        
        Args:
            longitude: 经度
            latitude: 纬度
            
        Returns:
            地址信息
        """
        logger.info(f"🔍 逆地理编码查询: ({longitude}, {latitude})")
        
        params = {
            "location": f"{longitude},{latitude}",
            "output": "json",
            "extensions": "all"
        }
        
        try:
            data = await self._make_request("geocode/regeo", params)
            
            regeocode = data.get("regeocode", {})
            formatted_address = regeocode.get("formatted_address", "")
            
            logger.info(f"✅ 逆地理编码成功: ({longitude}, {latitude}) -> {formatted_address}")
            return formatted_address
            
        except Exception as e:
            logger.error(f"❌ 逆地理编码失败: {e}")
            return None

    async def route_planning(
        self, 
        origin: str, 
        destination: str, 
        strategy: int = 0,
        waypoints: List[str] = None
    ) -> Optional[RouteInfo]:
        """
        路线规划
        
        Args:
            origin: 起点（地址或坐标）
            destination: 终点（地址或坐标）
            strategy: 路线策略 (0:速度优先 1:费用优先 2:距离优先 3:不走高速)
            waypoints: 途经点列表
            
        Returns:
            路线信息
        """
        logger.info(f"🛣️ 路线规划: {origin} -> {destination}")
        
        # 如果是地址，先转换为坐标
        origin_location = await self._get_location(origin)
        dest_location = await self._get_location(destination)
        
        if not origin_location or not dest_location:
            logger.error("❌ 起点或终点地址解析失败")
            return None
        
        params = {
            "origin": f"{origin_location.longitude},{origin_location.latitude}",
            "destination": f"{dest_location.longitude},{dest_location.latitude}",
            "strategy": strategy,
            "output": "json",
            "extensions": "all"
        }
        
        if waypoints:
            waypoint_coords = []
            for waypoint in waypoints:
                wp_location = await self._get_location(waypoint)
                if wp_location:
                    waypoint_coords.append(f"{wp_location.longitude},{wp_location.latitude}")
            
            if waypoint_coords:
                params["waypoints"] = ";".join(waypoint_coords)
        
        try:
            data = await self._make_request("direction/driving", params)
            
            route = data.get("route", {})
            paths = route.get("paths", [])
            
            if not paths:
                logger.warning("⚠️ 未找到可用路线")
                return None
            
            # 取第一条路线
            path = paths[0]
            
            # 解析路线信息
            distance = path.get("distance", "0")
            duration = path.get("duration", "0")
            tolls = path.get("tolls", "0")
            traffic_lights = path.get("traffic_lights", 0)
            
            # 格式化距离和时间
            distance_km = f"{float(distance) / 1000:.1f}公里"
            duration_min = f"{int(duration) // 60}分钟"
            tolls_yuan = f"{float(tolls)}元" if float(tolls) > 0 else "无过路费"
            
            # 解析路线步骤
            steps = []
            for step in path.get("steps", []):
                instruction = step.get("instruction", "")
                step_distance = step.get("distance", "0")
                step_duration = step.get("duration", "0")
                
                step_info = f"{instruction} ({float(step_distance)/1000:.1f}公里, {int(step_duration)//60}分钟)"
                steps.append(step_info)
            
            # 获取路线坐标串
            polyline = path.get("polyline", "")
            
            route_info = RouteInfo(
                distance=distance_km,
                duration=duration_min,
                tolls=tolls_yuan,
                traffic_lights=traffic_lights,
                steps=steps,
                polyline=polyline
            )
            
            logger.info(f"✅ 路线规划成功: {distance_km}, {duration_min}")
            return route_info
            
        except Exception as e:
            logger.error(f"❌ 路线规划失败: {e}")
            return None

    async def poi_search(
        self, 
        keywords: str, 
        city: str = "", 
        category: str = "",
        page_size: int = 10
    ) -> List[Dict[str, Any]]:
        """
        POI搜索
        
        Args:
            keywords: 搜索关键词
            city: 城市
            category: 分类代码
            page_size: 返回数量
            
        Returns:
            POI列表
        """
        logger.info(f"🔍 POI搜索: {keywords}")
        
        params = {
            "keywords": keywords,
            "output": "json",
            "offset": page_size,
            "page": 1,
            "extensions": "all"
        }
        
        if city:
            params["city"] = city
        
        if category:
            params["types"] = category
        
        try:
            data = await self._make_request("place/text", params)
            
            pois = data.get("pois", [])
            
            result = []
            for poi in pois:
                poi_info = {
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "location": poi.get("location", ""),
                    "tel": poi.get("tel", ""),
                    "type": poi.get("type", ""),
                    "business_area": poi.get("business_area", ""),
                    "distance": poi.get("distance", "")
                }
                result.append(poi_info)
            
            logger.info(f"✅ POI搜索成功: 找到{len(result)}个结果")
            return result
            
        except Exception as e:
            logger.error(f"❌ POI搜索失败: {e}")
            return []

    async def _get_location(self, address_or_coord: str) -> Optional[Location]:
        """
        获取位置信息（支持地址或坐标）
        
        Args:
            address_or_coord: 地址或坐标字符串
            
        Returns:
            位置信息
        """
        # 检查是否为坐标格式 (经度,纬度)
        if "," in address_or_coord and len(address_or_coord.split(",")) == 2:
            try:
                lng, lat = map(float, address_or_coord.split(","))
                return Location(longitude=lng, latitude=lat, name=address_or_coord)
            except ValueError:
                pass
        
        # 作为地址进行地理编码
        return await self.geocode(address_or_coord)

    async def get_weather(self, city: str) -> Optional[Dict[str, Any]]:
        """
        获取天气信息
        
        Args:
            city: 城市名称或城市编码
            
        Returns:
            天气信息
        """
        logger.info(f"🌤️ 获取天气信息: {city}")
        
        params = {
            "city": city,
            "output": "json",
            "extensions": "all"
        }
        
        try:
            data = await self._make_request("weather/weatherInfo", params)
            
            lives = data.get("lives", [])
            forecasts = data.get("forecasts", [])
            
            if lives:
                live = lives[0]
                weather_info = {
                    "city": live.get("city", ""),
                    "weather": live.get("weather", ""),
                    "temperature": live.get("temperature", ""),
                    "winddirection": live.get("winddirection", ""),
                    "windpower": live.get("windpower", ""),
                    "humidity": live.get("humidity", ""),
                    "reporttime": live.get("reporttime", "")
                }
                
                # 添加预报信息
                if forecasts:
                    forecast = forecasts[0]
                    weather_info["forecast"] = forecast.get("casts", [])
                
                logger.info(f"✅ 天气信息获取成功: {weather_info['city']} {weather_info['weather']} {weather_info['temperature']}°C")
                return weather_info
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 天气信息获取失败: {e}")
            return None

    async def search_around(self, location: str, keywords: str, radius: int = 1000,
                           types: str = "", page: int = 1, offset: int = 20) -> List[POIInfo]:
        """
        周边搜索

        Args:
            location: 中心点坐标 "经度,纬度" 或地址
            keywords: 搜索关键词
            radius: 搜索半径，单位米，最大50000
            types: POI类型，多个用|分隔
            page: 页码
            offset: 每页记录数，最大25

        Returns:
            List[POIInfo]: POI信息列表
        """
        try:
            # 如果location不是坐标格式，先进行地理编码
            if not self._is_coordinate(location):
                geo_result = await self.geocode(location)
                if geo_result:
                    location = f"{geo_result.longitude},{geo_result.latitude}"
                else:
                    logger.error(f"❌ 无法获取位置坐标: {location}")
                    return []

            params = {
                'key': self.api_key,
                'location': location,
                'keywords': keywords,
                'radius': min(radius, 50000),
                'types': types,
                'page': page,
                'offset': min(offset, 25),
                'extensions': 'all'
            }

            url = f"{self.BASE_URL}/v3/place/around"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1':
                        pois = []
                        for poi in data.get('pois', []):
                            location_str = poi.get('location', '0,0')
                            lng, lat = map(float, location_str.split(','))

                            poi_info = POIInfo(
                                id=poi.get('id', ''),
                                name=poi.get('name', ''),
                                type=poi.get('type', ''),
                                address=poi.get('address', ''),
                                location=Location(lng, lat, poi.get('name', '')),
                                distance=poi.get('distance', ''),
                                tel=poi.get('tel', ''),
                                business_area=poi.get('business_area', '')
                            )
                            pois.append(poi_info)

                        logger.info(f"✅ 周边搜索成功，找到 {len(pois)} 个结果")
                        return pois
                    else:
                        logger.error(f"❌ 周边搜索失败: {data.get('info', '未知错误')}")
                        return []
                else:
                    logger.error(f"❌ 周边搜索请求失败: HTTP {response.status}")
                    return []

        except Exception as e:
            logger.error(f"❌ 周边搜索异常: {e}")
            return []

    def _is_coordinate(self, location: str) -> bool:
        """检查是否为坐标格式"""
        try:
            parts = location.split(',')
            if len(parts) == 2:
                float(parts[0])
                float(parts[1])
                return True
            return False
        except:
            return False

    async def get_district(self, keywords: str = "", subdistrict: int = 1,
                          page: int = 1, offset: int = 20) -> List[DistrictInfo]:
        """
        行政区域查询

        Args:
            keywords: 查询关键词，如"北京"、"朝阳区"
            subdistrict: 子级行政区，0-不返回，1-返回下一级，2-返回下两级，3-返回下三级
            page: 页码
            offset: 每页记录数

        Returns:
            List[DistrictInfo]: 行政区域信息列表
        """
        try:
            params = {
                'key': self.api_key,
                'keywords': keywords,
                'subdistrict': subdistrict,
                'page': page,
                'offset': offset,
                'extensions': 'all'
            }

            url = f"{self.BASE_URL}/v3/config/district"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1':
                        districts = []
                        for district in data.get('districts', []):
                            center_str = district.get('center', '0,0')
                            lng, lat = map(float, center_str.split(','))

                            district_info = DistrictInfo(
                                adcode=district.get('adcode', ''),
                                name=district.get('name', ''),
                                center=Location(lng, lat, district.get('name', '')),
                                level=district.get('level', ''),
                                polyline=district.get('polyline', '')
                            )
                            districts.append(district_info)

                        logger.info(f"✅ 行政区域查询成功，找到 {len(districts)} 个结果")
                        return districts
                    else:
                        logger.error(f"❌ 行政区域查询失败: {data.get('info', '未知错误')}")
                        return []
                else:
                    logger.error(f"❌ 行政区域查询请求失败: HTTP {response.status}")
                    return []

        except Exception as e:
            logger.error(f"❌ 行政区域查询异常: {e}")
            return []

    async def driving_route(self, origin: str, destination: str, strategy: int = 0,
                           waypoints: str = "", avoidpolygons: str = "",
                           avoidroad: str = "") -> Optional[RouteInfo]:
        """
        驾车路径规划

        Args:
            origin: 起点坐标或地址
            destination: 终点坐标或地址
            strategy: 路径策略 0-速度优先 1-费用优先 2-距离优先 3-不走高速
            waypoints: 途经点坐标，多个用|分隔
            avoidpolygons: 避让区域
            avoidroad: 避让道路

        Returns:
            RouteInfo: 路线信息
        """
        return await self._route_planning("/v3/direction/driving", origin, destination,
                                        strategy, waypoints, avoidpolygons, avoidroad)

    async def walking_route(self, origin: str, destination: str) -> Optional[RouteInfo]:
        """
        步行路径规划

        Args:
            origin: 起点坐标或地址
            destination: 终点坐标或地址

        Returns:
            RouteInfo: 路线信息
        """
        return await self._route_planning("/v3/direction/walking", origin, destination)

    async def bicycling_route(self, origin: str, destination: str) -> Optional[RouteInfo]:
        """
        骑行路径规划

        Args:
            origin: 起点坐标或地址
            destination: 终点坐标或地址

        Returns:
            RouteInfo: 路线信息
        """
        return await self._route_planning("/v3/direction/bicycling", origin, destination)

    async def transit_route(self, origin: str, destination: str, city: str = "北京",
                           strategy: int = 0, nightflag: int = 0) -> Optional[RouteInfo]:
        """
        公交路径规划

        Args:
            origin: 起点坐标或地址
            destination: 终点坐标或地址
            city: 城市名称或adcode
            strategy: 公交策略 0-最快捷 1-最经济 2-最少换乘 3-最少步行 4-最舒适 5-不乘地铁
            nightflag: 是否计算夜班车 0-不计算 1-计算

        Returns:
            RouteInfo: 路线信息
        """
        try:
            # 转换坐标
            origin_coord = await self._get_coordinate(origin)
            dest_coord = await self._get_coordinate(destination)

            if not origin_coord or not dest_coord:
                return None

            params = {
                'key': self.api_key,
                'origin': origin_coord,
                'destination': dest_coord,
                'city': city,
                'strategy': strategy,
                'nightflag': nightflag,
                'extensions': 'all'
            }

            url = f"{self.BASE_URL}/v3/direction/transit/integrated"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1' and data.get('route'):
                        route_data = data['route']
                        transits = route_data.get('transits', [])

                        if transits:
                            transit = transits[0]  # 取第一个方案

                            route_info = RouteInfo(
                                distance=f"{float(transit.get('distance', 0))/1000:.1f}公里",
                                duration=f"{int(transit.get('duration', 0))//60}分钟",
                                tolls="0元",  # 公交无过路费
                                traffic_lights=0,  # 公交无红绿灯
                                steps=[segment.get('instruction', '') for segment in transit.get('segments', [])],
                                polyline="",  # 公交路线较复杂，暂不处理
                                raw_data=data  # 保存原始API响应数据
                            )

                            logger.info(f"✅ 公交路径规划成功: {route_info.distance}, {route_info.duration}")
                            return route_info
                        else:
                            logger.error("❌ 未找到公交路线")
                            return None
                    else:
                        logger.error(f"❌ 公交路径规划失败: {data.get('info', '未知错误')}")
                        return None
                else:
                    logger.error(f"❌ 公交路径规划请求失败: HTTP {response.status}")
                    return None

        except Exception as e:
            logger.error(f"❌ 公交路径规划异常: {e}")
            return None

    async def calculate_distance(self, origins: List[str], destinations: List[str],
                               type: int = 1) -> Dict[str, Any]:
        """
        距离测量

        Args:
            origins: 起点列表，坐标或地址
            destinations: 终点列表，坐标或地址
            type: 路径计算方式 1-直线距离 3-驾车导航距离

        Returns:
            Dict: 距离信息
        """
        try:
            # 转换所有地址为坐标
            origin_coords = []
            for origin in origins:
                coord = await self._get_coordinate(origin)
                if coord:
                    origin_coords.append(coord)

            dest_coords = []
            for dest in destinations:
                coord = await self._get_coordinate(dest)
                if coord:
                    dest_coords.append(coord)

            if not origin_coords or not dest_coords:
                logger.error("❌ 无法获取有效坐标")
                return {}

            params = {
                'key': self.api_key,
                'origins': '|'.join(origin_coords),
                'destination': '|'.join(dest_coords),
                'type': type
            }

            url = f"{self.BASE_URL}/v3/distance"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1':
                        results = data.get('results', [])
                        distances = []

                        for result in results:
                            distance_info = {
                                'distance': f"{float(result.get('distance', 0))/1000:.1f}公里",
                                'duration': f"{int(result.get('duration', 0))//60}分钟" if result.get('duration') else "N/A"
                            }
                            distances.append(distance_info)

                        logger.info(f"✅ 距离测量成功，计算了 {len(distances)} 个距离")
                        return {'distances': distances}
                    else:
                        logger.error(f"❌ 距离测量失败: {data.get('info', '未知错误')}")
                        return {}
                else:
                    logger.error(f"❌ 距离测量请求失败: HTTP {response.status}")
                    return {}

        except Exception as e:
            logger.error(f"❌ 距离测量异常: {e}")
            return {}

    async def ip_location(self, ip: str = "") -> Optional[Location]:
        """
        IP定位

        Args:
            ip: IP地址，为空则使用请求方IP

        Returns:
            Location: 位置信息
        """
        try:
            params = {
                'key': self.api_key,
                'ip': ip
            }

            url = f"{self.BASE_URL}/v3/ip"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1':
                        location_str = data.get('rectangle', '0,0;0,0')
                        # 取矩形中心点
                        coords = location_str.split(';')
                        if len(coords) >= 2:
                            coord1 = coords[0].split(',')
                            coord2 = coords[1].split(',')
                            lng = (float(coord1[0]) + float(coord2[0])) / 2
                            lat = (float(coord1[1]) + float(coord2[1])) / 2
                        else:
                            lng, lat = 0, 0

                        location = Location(
                            longitude=lng,
                            latitude=lat,
                            name=data.get('city', ''),
                            address=f"{data.get('province', '')}{data.get('city', '')}"
                        )

                        logger.info(f"✅ IP定位成功: {location.address}")
                        return location
                    else:
                        logger.error(f"❌ IP定位失败: {data.get('info', '未知错误')}")
                        return None
                else:
                    logger.error(f"❌ IP定位请求失败: HTTP {response.status}")
                    return None

        except Exception as e:
            logger.error(f"❌ IP定位异常: {e}")
            return None

    async def get_weather_forecast(self, city: str, extensions: str = "all") -> Optional[WeatherInfo]:
        """
        天气预报查询

        Args:
            city: 城市名称或adcode
            extensions: base-实况天气 all-预报天气

        Returns:
            WeatherInfo: 天气信息
        """
        try:
            params = {
                'key': self.api_key,
                'city': city,
                'extensions': extensions
            }

            url = f"{self.BASE_URL}/v3/weather/weatherInfo"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1':
                        lives = data.get('lives', [])
                        if lives:
                            live = lives[0]
                            weather_info = WeatherInfo(
                                province=live.get('province', ''),
                                city=live.get('city', ''),
                                adcode=live.get('adcode', ''),
                                weather=live.get('weather', ''),
                                temperature=live.get('temperature', ''),
                                winddirection=live.get('winddirection', ''),
                                windpower=live.get('windpower', ''),
                                humidity=live.get('humidity', ''),
                                reporttime=live.get('reporttime', ''),
                                temperature_float=live.get('temperature_float', ''),
                                humidity_float=live.get('humidity_float', '')
                            )

                            logger.info(f"✅ 天气查询成功: {weather_info.city} {weather_info.weather} {weather_info.temperature}°C")
                            return weather_info
                        else:
                            logger.error("❌ 未找到天气数据")
                            return None
                    else:
                        logger.error(f"❌ 天气查询失败: {data.get('info', '未知错误')}")
                        return None
                else:
                    logger.error(f"❌ 天气查询请求失败: HTTP {response.status}")
                    return None

        except Exception as e:
            logger.error(f"❌ 天气查询异常: {e}")
            return None

    async def _get_coordinate(self, location: str) -> Optional[str]:
        """获取坐标字符串"""
        if self._is_coordinate(location):
            return location

        geo_result = await self.geocode(location)
        if geo_result:
            return f"{geo_result.longitude},{geo_result.latitude}"
        return None

    async def _route_planning(self, endpoint: str, origin: str, destination: str,
                            strategy: int = 0, waypoints: str = "",
                            avoidpolygons: str = "", avoidroad: str = "") -> Optional[RouteInfo]:
        """通用路径规划方法"""
        try:
            # 转换坐标
            origin_coord = await self._get_coordinate(origin)
            dest_coord = await self._get_coordinate(destination)

            if not origin_coord or not dest_coord:
                return None

            params = {
                'key': self.api_key,
                'origin': origin_coord,
                'destination': dest_coord,
                'extensions': 'all',
                'output': 'json',
                'geometry': 'polyline'  # 明确要求返回polyline数据
            }

            # 添加特定参数
            if strategy is not None:
                params['strategy'] = strategy
            if waypoints:
                params['waypoints'] = waypoints
            if avoidpolygons:
                params['avoidpolygons'] = avoidpolygons
            if avoidroad:
                params['avoidroad'] = avoidroad

            url = f"{self.BASE_URL}{endpoint}"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1' and data.get('route'):
                        route_data = data['route']
                        paths = route_data.get('paths', [])

                        if paths:
                            path = paths[0]  # 取第一个路径

                            # 尝试从多个可能的位置获取polyline
                            polyline = ""
                            if 'polyline' in path:
                                polyline = path['polyline']
                            elif 'geometry' in path:
                                polyline = path['geometry']
                            elif len(path.get('steps', [])) > 0:
                                # 如果没有整体polyline，尝试从steps中获取
                                step_polylines = []
                                for step in path.get('steps', []):
                                    if 'polyline' in step:
                                        step_polylines.append(step['polyline'])
                                if step_polylines:
                                    polyline = ';'.join(step_polylines)

                            logger.info(f"🗺️ Polyline数据长度: {len(polyline)} 字符")

                            route_info = RouteInfo(
                                distance=f"{float(path.get('distance', 0))/1000:.1f}公里",
                                duration=f"{int(path.get('duration', 0))//60}分钟",
                                tolls=f"{float(path.get('tolls', 0))}元",
                                traffic_lights=int(path.get('traffic_lights', 0)),
                                steps=[step.get('instruction', '') for step in path.get('steps', [])],
                                polyline=polyline
                            )

                            logger.info(f"✅ 路径规划成功: {route_info.distance}, {route_info.duration}")
                            return route_info
                        else:
                            logger.error("❌ 未找到路径")
                            return None
                    else:
                        logger.error(f"❌ 路径规划失败: {data.get('info', '未知错误')}")
                        return None
                else:
                    logger.error(f"❌ 路径规划请求失败: HTTP {response.status}")
                    return None

        except Exception as e:
            logger.error(f"❌ 路径规划异常: {e}")
            return None

# 使用示例
async def main():
    """测试高德地图API功能"""
    
    # 请在环境变量中设置您的高德地图API密钥
    # export AMAP_API_KEY="your_amap_api_key"
    
    async with AmapAPI() as amap:
        # 1. 地理编码测试
        print("=== 地理编码测试 ===")
        location = await amap.geocode("北京市朝阳区望京SOHO")
        if location:
            print(f"地址: {location.address}")
            print(f"坐标: ({location.longitude}, {location.latitude})")
        
        # 2. 路线规划测试
        print("\n=== 路线规划测试 ===")
        route = await amap.route_planning(
            origin="北京市朝阳区望京SOHO",
            destination="北京市海淀区中关村",
            strategy=0  # 速度优先
        )
        
        if route:
            print(f"距离: {route.distance}")
            print(f"时间: {route.duration}")
            print(f"过路费: {route.tolls}")
            print(f"红绿灯: {route.traffic_lights}个")
            print("路线步骤:")
            for i, step in enumerate(route.steps[:3], 1):  # 只显示前3步
                print(f"  {i}. {step}")
        
        # 3. POI搜索测试
        print("\n=== POI搜索测试 ===")
        pois = await amap.poi_search("美食", city="北京", page_size=5)
        for poi in pois:
            print(f"- {poi['name']}: {poi['address']}")
        
        # 4. 天气查询测试
        print("\n=== 天气查询测试 ===")
        weather = await amap.get_weather("北京")
        if weather:
            print(f"城市: {weather['city']}")
            print(f"天气: {weather['weather']}")
            print(f"温度: {weather['temperature']}°C")
            print(f"湿度: {weather['humidity']}%")

if __name__ == "__main__":
    asyncio.run(main())
