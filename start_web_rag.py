#!/usr/bin/env python3
"""
RAG数字人Web系统启动脚本
macOS优化版 - 一键启动完整的Web RAG数字人系统
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查必要的Python包
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'pathlib'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少Python包: {', '.join(missing_packages)}")
        print("请运行: pip install fastapi uvicorn websockets")
        return False
    
    # 检查文件
    required_files = [
        "web_interface.html",
        "avatar_engine.js",
        "web_client.js", 
        "web_rag_server.py",
        "simple_voice_rag.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 依赖检查通过")
    return True

def check_rag_system():
    """检查RAG系统"""
    print("🔍 检查RAG系统...")
    
    try:
        # 检查环境变量
        api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("⚠️ 警告: 未设置API密钥 (DASHSCOPE_API_KEY 或 OPENAI_API_KEY)")
            print("RAG功能可能无法正常工作")
            return False
        
        # 检查RAG存储目录
        rag_storage = Path("rag_storage_server")
        if not rag_storage.exists():
            print("⚠️ 警告: 未找到rag_storage_server目录")
            print("请先运行raganything_example.py初始化知识库")
            return False
        
        print("✅ RAG系统检查通过")
        return True
        
    except Exception as e:
        print(f"❌ RAG系统检查失败: {e}")
        return False

def start_server():
    """启动Web服务器"""
    print("🚀 启动Web RAG服务器...")
    
    try:
        # 启动服务器
        process = subprocess.Popen([
            sys.executable, "web_rag_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查服务器是否正在运行
        if process.poll() is None:
            print("✅ 服务器启动成功")
            return process
        else:
            print("❌ 服务器启动失败")
            output, _ = process.communicate()
            print(f"错误输出: {output}")
            return None
            
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("🌐 打开浏览器...")
    
    url = "http://localhost:8000"
    
    try:
        # 在macOS上优先使用Safari
        if sys.platform == "darwin":
            subprocess.run(["open", "-a", "Safari", url], check=True)
        else:
            webbrowser.open(url)
        
        print(f"✅ 浏览器已打开: {url}")
        return True
        
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print(f"请手动访问: {url}")
        return False

def print_usage_info():
    """打印使用说明"""
    print("\n" + "="*60)
    print("🎉 RAG数字人Web系统已启动!")
    print("="*60)
    print("📱 访问地址: http://localhost:8000")
    print("🤖 功能特性:")
    print("  ✅ 智能问答 - 基于RAG知识库")
    print("  ✅ 语音输入 - 点击🎤按钮或直接说话")
    print("  ✅ 语音输出 - 自动播放回答语音")
    print("  ✅ 数字人动画 - 实时嘴形同步")
    print("  ✅ 中英文支持 - 自动识别语言")
    print("\n💡 使用提示:")
    print("  • 首次使用请允许麦克风权限")
    print("  • 支持文字输入和语音输入")
    print("  • 数字人会根据状态显示不同动画")
    print("  • 按Ctrl+C停止服务器")
    print("\n🔧 故障排除:")
    print("  • 如果语音不工作，请检查浏览器权限")
    print("  • 如果连接失败，请检查防火墙设置")
    print("  • 如果回答不准确，请检查API密钥配置")
    print("="*60)

def main():
    """主函数"""
    print("🚀 RAG数字人Web系统启动器")
    print("macOS优化版 v1.0")
    print("-" * 40)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请解决上述问题后重试")
        return 1
    
    # 检查RAG系统
    rag_ok = check_rag_system()
    if not rag_ok:
        print("\n⚠️ RAG系统检查失败，但仍可启动基础功能")
        response = input("是否继续启动? (y/N): ")
        if response.lower() != 'y':
            return 1
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        print("\n❌ 服务器启动失败")
        return 1
    
    # 打开浏览器
    time.sleep(2)  # 等待服务器完全启动
    open_browser()
    
    # 打印使用说明
    print_usage_info()
    
    try:
        # 等待用户中断
        print("\n⌨️  按Ctrl+C停止服务器...")
        server_process.wait()
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        server_process.terminate()
        
        # 等待进程结束
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            print("⚠️ 强制终止服务器进程")
            server_process.kill()
        
        print("✅ 服务器已停止")
        print("👋 感谢使用RAG数字人系统!")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
