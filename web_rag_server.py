#!/usr/bin/env python3
"""
Web RAG服务器 - macOS优化版
集成现有的SimpleVoiceRAG系统，提供Web界面
"""

import os
import asyncio
import json
import logging
import tempfile
import base64
from pathlib import Path
from typing import Set
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse

# 检查Edge-TTS是否可用
try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
    print("✅ Edge-TTS可用，将使用高质量语音")
except ImportError:
    EDGE_TTS_AVAILABLE = False
    print("⚠️ Edge-TTS不可用，将使用浏览器TTS")

# 导入地图功能
try:
    from map_rag_integration import MapRAGIntegration
    MAP_INTEGRATION_AVAILABLE = True
    print("✅ 高德地图集成可用")
except ImportError as e:
    MAP_INTEGRATION_AVAILABLE = False
    print(f"⚠️ 高德地图集成不可用: {e}")

# 导入现有的RAG系统
try:
    from simple_voice_rag import SimpleVoiceRAG
    RAG_AVAILABLE = True
    print("✅ 成功导入SimpleVoiceRAG")
except ImportError as e:
    RAG_AVAILABLE = False
    print(f"❌ 无法导入SimpleVoiceRAG: {e}")
    print("请确保simple_voice_rag.py在同一目录下")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="RAG数字人系统",
    description="基于RAG技术的智能问答数字人",
    version="1.0.0"
)

# 静态文件服务
static_dir = Path(__file__).parent
app.mount("/static", StaticFiles(directory=static_dir), name="static")

class WebRAGServer:
    """Web RAG服务器类"""
    
    def __init__(self):
        self.rag_system: SimpleVoiceRAG = None
        self.map_integration: MapRAGIntegration = None
        self.connected_clients: Set[WebSocket] = set()
        self.is_initialized = False
    
    async def initialize(self):
        """初始化RAG系统"""
        if not RAG_AVAILABLE:
            logger.error("RAG系统不可用")
            return False
        
        try:
            logger.info("🚀 正在初始化RAG系统...")
            self.rag_system = SimpleVoiceRAG()
            await self.rag_system.initialize_rag()

            # 初始化地图集成
            if MAP_INTEGRATION_AVAILABLE:
                try:
                    logger.info("🗺️ 正在初始化地图集成...")
                    self.map_integration = MapRAGIntegration()
                    await self.map_integration.__aenter__()
                    logger.info("✅ 地图集成初始化成功")
                except Exception as e:
                    logger.warning(f"⚠️ 地图集成初始化失败: {e}")
                    self.map_integration = None

            self.is_initialized = True
            logger.info("✅ RAG系统初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ RAG系统初始化失败: {e}")
            return False
    
    async def process_query(self, query_text: str) -> dict:
        """处理查询请求"""
        try:
            logger.info(f"📝 处理查询: {query_text}")

            # 首先检查是否为地图相关查询
            if self.map_integration:
                try:
                    map_result = await self.map_integration.process_map_query_with_data(
                        query_text,
                        user_location="北京市朝阳区",  # 可以从用户设置中获取
                        city="北京"
                    )
                    if map_result:
                        logger.info("🗺️ 使用地图服务响应")
                        return {
                            'type': 'response',
                            'text': map_result.get('text', ''),
                            'language': 'zh-CN',
                            'source': 'map',
                            'map_data': map_result.get('map_data', {}),
                            'map_action': map_result.get('map_action', 'none')
                        }
                except Exception as e:
                    logger.warning(f"⚠️ 地图查询处理失败: {e}")

            # 如果RAG系统已初始化，使用真实RAG
            if self.is_initialized and self.rag_system:
                result = await self.rag_system.query(query_text)

                if result and 'cleaned_text' in result:
                    response = {
                        'type': 'response',
                        'text': result['cleaned_text'],
                        'language': result.get('language', 'zh-CN'),
                        'timestamp': result.get('timestamp'),
                        'source': 'rag'
                    }
                    logger.info(f"✅ RAG查询成功，回答长度: {len(result['cleaned_text'])} 字符")
                    return response

            # 如果RAG系统未初始化，使用演示模式
            logger.info("🎭 使用演示模式回答")
            demo_response = self.get_demo_response(query_text)
            return demo_response

        except Exception as e:
            logger.error(f"❌ 查询处理错误: {e}")
            # 降级到演示模式
            return self.get_demo_response(query_text)

    def get_demo_response(self, query_text: str) -> dict:
        """演示模式回答（无需API密钥）"""
        import time

        # 检测语言
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in query_text)
        language = 'zh-CN' if is_chinese else 'en-US'

        # 根据问题类型生成演示回答
        if any(word in query_text.lower() for word in ['交通', '路线', '怎么去', 'transport', 'route', 'how to get']):
            if is_chinese:
                demo_text = f"""关于"{query_text}"的交通路线信息：

🚌 公交路线：
- 919路：从德胜门出发，途经昌平区，全程约1小时
- 345路：从西直门出发，经过多个站点
- 地铁13号线：快速便捷的地铁选择

🚗 自驾路线：
- 走京藏高速，约45分钟车程
- 市区道路：走北四环-京藏高速路线

💰 费用参考：
- 公交：2-5元
- 地铁：3-6元
- 出租车：约80-120元

⏰ 建议出行时间：避开早晚高峰期

注意：这是演示模式回答，实际使用请配置API密钥获取真实数据。"""
            else:
                demo_text = f"""Transportation information for "{query_text}":

🚌 Bus Routes:
- Route 919: From Deshengmen, via Changping District, about 1 hour
- Route 345: From Xizhimen, multiple stops
- Metro Line 13: Fast and convenient subway option

🚗 Driving Routes:
- Take Jingzang Expressway, about 45 minutes
- City roads: North 4th Ring - Jingzang Expressway route

💰 Cost Reference:
- Bus: 2-5 RMB
- Metro: 3-6 RMB
- Taxi: About 80-120 RMB

⏰ Recommended travel time: Avoid rush hours

Note: This is a demo response. Please configure API key for real data."""

        elif any(word in query_text.lower() for word in ['美食', '吃', '餐厅', 'food', 'restaurant', 'eat']):
            if is_chinese:
                demo_text = f"""关于"{query_text}"的美食推荐：

🍜 特色美食：
- 北京烤鸭：全聚德、便宜坊等老字号
- 炸酱面：地道的北京风味
- 豆汁焦圈：传统北京小吃

🏪 推荐餐厅：
- 全聚德烤鸭店：王府井店、前门店
- 老北京炸酱面馆：多个分店
- 护国寺小吃：传统小吃集合

💰 价格范围：
- 小吃：10-30元
- 正餐：50-200元
- 高档餐厅：200-500元

注意：这是演示模式回答，实际使用请配置API密钥获取真实数据。"""
            else:
                demo_text = f"""Food recommendations for "{query_text}":

🍜 Specialty Foods:
- Peking Duck: Quanjude, Bianyifang and other time-honored brands
- Zhajiangmian: Authentic Beijing flavor
- Douzhir Jiaoquan: Traditional Beijing snacks

🏪 Recommended Restaurants:
- Quanjude Roast Duck: Wangfujing, Qianmen branches
- Old Beijing Zhajiangmian Restaurant: Multiple locations
- Huguo Temple Snacks: Traditional snack collection

💰 Price Range:
- Snacks: 10-30 RMB
- Regular meals: 50-200 RMB
- Fine dining: 200-500 RMB

Note: This is a demo response. Please configure API key for real data."""

        else:
            if is_chinese:
                demo_text = f"""您询问了"{query_text}"，这是一个很好的问题！

🤖 演示模式说明：
当前系统运行在演示模式下，可以体验完整的Web界面功能：
- ✅ 语音输入和输出
- ✅ 数字人动画效果
- ✅ 实时交互界面
- ✅ 中英文支持

🔑 获取完整功能：
要获取基于真实知识库的智能回答，请：
1. 获取阿里云DashScope API密钥
2. 设置环境变量：export DASHSCOPE_API_KEY="your-key"
3. 重新启动系统

💡 您可以尝试问一些关于交通、美食、地点的问题来体验演示效果！"""
            else:
                demo_text = f"""You asked about "{query_text}", which is a great question!

🤖 Demo Mode Explanation:
The system is currently running in demo mode, you can experience the complete web interface features:
- ✅ Voice input and output
- ✅ Digital human animation effects
- ✅ Real-time interactive interface
- ✅ Chinese and English support

🔑 Get Full Features:
To get intelligent answers based on real knowledge base, please:
1. Get Alibaba Cloud DashScope API key
2. Set environment variable: export DASHSCOPE_API_KEY="your-key"
3. Restart the system

💡 You can try asking questions about transportation, food, or locations to experience the demo effects!"""

        return {
            'type': 'response',
            'text': demo_text,
            'language': language,
            'timestamp': int(time.time())
        }
    
    def add_client(self, websocket: WebSocket):
        """添加客户端连接"""
        self.connected_clients.add(websocket)
        logger.info(f"🔗 新客户端连接，当前连接数: {len(self.connected_clients)}")
    
    def remove_client(self, websocket: WebSocket):
        """移除客户端连接"""
        self.connected_clients.discard(websocket)
        logger.info(f"🔌 客户端断开，当前连接数: {len(self.connected_clients)}")

    async def generate_edge_tts_audio(self, text: str, language: str = 'zh-CN') -> str:
        """使用Edge-TTS生成高质量语音，返回base64编码的音频"""
        if not EDGE_TTS_AVAILABLE:
            return None

        try:
            # 清理文本
            cleaned_text = self.clean_text_for_speech(text, language)
            if not cleaned_text.strip():
                return None

            # 根据语言选择语音（与后端simple_voice_rag.py保持一致）
            if language.startswith('zh') or language == 'zh-CN':
                voice = "zh-CN-XiaoxiaoNeural"  # 中文女声
            else:
                voice = "en-US-AriaNeural"      # 英文女声

            logger.info(f"🎤 正在使用Edge-TTS生成高质量女声: {voice}")
            logger.info(f"📝 待合成文本长度: {len(cleaned_text)} 字符")
            logger.info(f"📝 文本内容预览: {cleaned_text[:100]}{'...' if len(cleaned_text) > 100 else ''}")

            # 对于长文本，Edge-TTS可以直接处理，不需要分段
            logger.info(f"📢 准备合成完整文本，无长度限制")

            # 使用Edge-TTS生成语音
            communicate = edge_tts.Communicate(cleaned_text, voice)

            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                audio_file = temp_file.name

            # 生成语音文件
            await communicate.save(audio_file)

            # 读取音频文件并转换为base64
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # 清理临时文件
            os.unlink(audio_file)

            logger.info(f"✅ Edge-TTS语音生成成功，音频大小: {len(audio_data)} 字节")
            return audio_base64

        except Exception as e:
            logger.error(f"❌ Edge-TTS语音生成失败: {e}")
            return None

    def clean_text_for_speech(self, text: str, language: str = 'zh-CN') -> str:
        """清理文本用于语音合成"""
        import re

        logger.info(f"🧹 开始清理Edge-TTS文本，原始长度: {len(text)}")

        # 基本清理
        cleaned = text.encode('utf-8', errors='ignore').decode('utf-8')
        cleaned = re.sub(r'[\ud800-\udfff]', '', cleaned)

        # 移除所有emoji和特殊符号（使用Unicode范围）
        cleaned = re.sub(r'[\U0001F600-\U0001F64F]', '', cleaned)  # 表情符号
        cleaned = re.sub(r'[\U0001F300-\U0001F5FF]', '', cleaned)  # 杂项符号和象形文字
        cleaned = re.sub(r'[\U0001F680-\U0001F6FF]', '', cleaned)  # 交通和地图符号
        cleaned = re.sub(r'[\U0001F1E0-\U0001F1FF]', '', cleaned)  # 国旗
        cleaned = re.sub(r'[\U00002600-\U000026FF]', '', cleaned)  # 杂项符号
        cleaned = re.sub(r'[\U00002700-\U000027BF]', '', cleaned)  # 装饰符号

        # 移除具体的特殊符号
        cleaned = re.sub(r'[🎤🔊💬📱🌟🎯🔧💄🤖🗺️🛣️📍🎉✅❌⚠️🔍🚗📏⏱️💰🚦🏛️🍽️🏨🛍️🏥🏫•·▪▫◦‣⁃]', '', cleaned)

        # 移除markdown格式
        cleaned = re.sub(r'\*\*(.*?)\*\*', r'\1', cleaned)  # 粗体
        cleaned = re.sub(r'\*(.*?)\*', r'\1', cleaned)      # 斜体
        cleaned = re.sub(r'__(.*?)__', r'\1', cleaned)      # 下划线粗体
        cleaned = re.sub(r'_(.*?)_', r'\1', cleaned)        # 下划线斜体
        cleaned = re.sub(r'`(.*?)`', r'\1', cleaned)        # 代码
        cleaned = re.sub(r'```[\s\S]*?```', '', cleaned)    # 代码块

        # 移除特殊标点和符号
        cleaned = re.sub(r'[【】〖〗〔〕（）""'']', '', cleaned)  # 各种括号和引号
        cleaned = re.sub(r'[→←↑↓↔]', '到', cleaned)         # 箭头符号
        cleaned = re.sub(r'[①②③④⑤⑥⑦⑧⑨⑩⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑]', '', cleaned)  # 圆圈和括号数字

        # 移除开发术语和技术词汇
        cleaned = re.sub(r'知识图谱|KG|Knowledge Graph', '', cleaned)
        cleaned = re.sub(r'数据库|Database|DB', '', cleaned)
        cleaned = re.sub(r'向量数据库|Vector Database', '', cleaned)
        cleaned = re.sub(r'RAG|检索增强生成', '', cleaned)
        cleaned = re.sub(r'LLM|大语言模型', '', cleaned)
        cleaned = re.sub(r'API|接口', '', cleaned)
        cleaned = re.sub(r'JSON|XML|HTML', '', cleaned)
        cleaned = re.sub(r'SQL|查询语句', '', cleaned)
        cleaned = re.sub(r'实体|关系|Entity|Relationship', '', cleaned)
        cleaned = re.sub(r'嵌入|Embedding|向量', '', cleaned)
        cleaned = re.sub(r'索引|Index|检索', '', cleaned)
        cleaned = re.sub(r'算法|Algorithm', '', cleaned)
        cleaned = re.sub(r'模型|Model', '', cleaned)
        cleaned = re.sub(r'训练|Training', '', cleaned)
        cleaned = re.sub(r'推理|Inference', '', cleaned)
        cleaned = re.sub(r'参数|Parameter', '', cleaned)
        cleaned = re.sub(r'配置|Config|Configuration', '', cleaned)
        cleaned = re.sub(r'框架|Framework', '', cleaned)
        cleaned = re.sub(r'库|Library', '', cleaned)
        cleaned = re.sub(r'依赖|Dependency', '', cleaned)

        # 移除文档引用和特殊格式
        cleaned = re.sub(r'[#*]+', '', cleaned)
        cleaned = re.sub(r'References?:', '', cleaned)
        cleaned = re.sub(r'\[KG\].*?\.pdf', '', cleaned)
        cleaned = re.sub(r'\[DC\].*?\.pdf', '', cleaned)
        cleaned = re.sub(r'\[.*?\]', '', cleaned)  # 移除所有方括号内容
        cleaned = re.sub(r'基于.*?的', '基于智能分析的', cleaned)  # 简化技术描述

        # 移除列表标记
        cleaned = re.sub(r'^\s*[-*+]\s+', '', cleaned, flags=re.MULTILINE)
        cleaned = re.sub(r'^\s*\d+\.\s*', '', cleaned, flags=re.MULTILINE)

        # 移除网址
        cleaned = re.sub(r'https?://\S+', '', cleaned)
        cleaned = re.sub(r'www\.\S+', '', cleaned)

        # 清理空格和换行
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = re.sub(r'\n+', '。', cleaned)
        cleaned = cleaned.strip()

        # 移除多余的标点
        cleaned = re.sub(r'[。]{2,}', '。', cleaned)
        cleaned = re.sub(r'[，]{2,}', '，', cleaned)

        # 移除长度限制 - 支持完整语音播报
        # 不再限制文本长度，让Edge-TTS播报完整内容
        logger.info(f"📢 准备播报完整文本，长度: {len(cleaned)} 字符")

        # 如果清理后文本太短，提供默认文本
        if len(cleaned) < 5:
            cleaned = "已为您处理完成，请查看详细信息。" if not language.startswith('en') else "Processing completed. Please check the detailed response."

        logger.info(f"✅ Edge-TTS文本清理完成，清理后长度: {len(cleaned)}")
        return cleaned

# 全局服务器实例
server = WebRAGServer()

@app.on_event("startup")
async def startup_event():
    """服务器启动事件"""
    logger.info("🚀 启动Web RAG服务器...")
    success = await server.initialize()
    if success:
        logger.info("✅ 服务器启动成功")
    else:
        logger.warning("⚠️ 服务器启动但RAG系统初始化失败")

@app.get("/", response_class=HTMLResponse)
async def get_homepage():
    """返回主页"""
    html_file = Path(__file__).parent / "web_interface.html"
    if html_file.exists():
        return FileResponse(html_file)
    else:
        return HTMLResponse(content="""
        <!DOCTYPE html>
        <html>
        <head>
            <title>RAG数字人系统</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>🤖 RAG数字人系统</h1>
            <p>❌ 找不到web_interface.html文件</p>
            <p>请确保以下文件在同一目录下：</p>
            <ul>
                <li>web_interface.html</li>
                <li>avatar_engine.js</li>
                <li>web_client.js</li>
                <li>simple_voice_rag.py</li>
            </ul>
        </body>
        </html>
        """)

@app.get("/avatar_engine.js")
async def get_avatar_engine():
    """返回数字人引擎JS文件"""
    js_file = Path(__file__).parent / "avatar_engine.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// avatar_engine.js not found", status_code=404)

@app.get("/web_client.js")
async def get_web_client():
    """返回Web客户端JS文件"""
    js_file = Path(__file__).parent / "web_client.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// web_client.js not found", status_code=404)

@app.get("/test_audio.html")
async def get_test_audio():
    """返回音频测试页面"""
    html_file = Path(__file__).parent / "test_audio.html"
    if html_file.exists():
        return FileResponse(html_file)
    else:
        return HTMLResponse("test_audio.html not found", status_code=404)

@app.get("/rpm")
async def get_rpm_interface():
    """返回Ready Player Me界面"""
    html_file = Path(__file__).parent / "rpm_web_interface.html"
    if html_file.exists():
        return FileResponse(html_file)
    else:
        return HTMLResponse("rpm_web_interface.html not found", status_code=404)

@app.get("/rpm_avatar_engine.js")
async def get_rpm_avatar_engine():
    """返回Ready Player Me引擎JS文件"""
    js_file = Path(__file__).parent / "rpm_avatar_engine.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// rpm_avatar_engine.js not found", status_code=404)

@app.get("/rpm_web_client.js")
async def get_rpm_web_client():
    """返回Ready Player Me客户端JS文件"""
    js_file = Path(__file__).parent / "rpm_web_client.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// rpm_web_client.js not found", status_code=404)

@app.get("/realistic_face_manager.js")
async def get_realistic_face_manager():
    """返回真实人脸管理器JS文件"""
    js_file = Path(__file__).parent / "realistic_face_manager.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// realistic_face_manager.js not found", status_code=404)

@app.get("/simple_beauty_test.html")
async def get_simple_beauty_test():
    """返回简化美女测试页面"""
    html_file = Path(__file__).parent / "simple_beauty_test.html"
    if html_file.exists():
        return FileResponse(html_file)
    else:
        return HTMLResponse("simple_beauty_test.html not found", status_code=404)

@app.get("/beauty")
async def get_real_beauty_avatar():
    """返回真实美女数字人页面"""
    html_file = Path(__file__).parent / "real_beauty_avatar.html"
    if html_file.exists():
        return FileResponse(html_file)
    else:
        return HTMLResponse("real_beauty_avatar.html not found", status_code=404)

@app.get("/real_beauty_client.js")
async def get_real_beauty_client():
    """返回真实美女客户端JS文件"""
    js_file = Path(__file__).parent / "real_beauty_client.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// real_beauty_client.js not found", status_code=404)

@app.get("/my.webp")
async def get_beauty_image():
    """返回美女图片"""
    img_file = Path(__file__).parent / "my.webp"
    if img_file.exists():
        return FileResponse(img_file, media_type="image/webp")
    else:
        return HTMLResponse("my.webp not found", status_code=404)

@app.get("/static")
async def get_static_beauty_chat():
    """返回静态美女智能语音交互页面"""
    static_file = Path(__file__).parent / "static_beauty_chat.html"
    if static_file.exists():
        return FileResponse(static_file)
    else:
        return HTMLResponse("static_beauty_chat.html not found", status_code=404)

@app.get("/enhanced-map")
async def get_enhanced_map_chat():
    """返回增强版地图智能交互页面"""
    enhanced_file = Path(__file__).parent / "enhanced_map_chat.html"
    if enhanced_file.exists():
        return FileResponse(enhanced_file)
    else:
        return HTMLResponse("enhanced_map_chat.html not found", status_code=404)

@app.get("/amap-test")
async def get_amap_test():
    """返回高德地图API测试页面"""
    test_file = Path(__file__).parent / "amap_api_test.html"
    if test_file.exists():
        return FileResponse(test_file)
    else:
        return HTMLResponse("amap_api_test.html not found", status_code=404)

@app.get("/face")
async def get_real_face_animation():
    """返回真实人脸动画页面"""
    html_file = Path(__file__).parent / "real_face_animation.html"
    if html_file.exists():
        return FileResponse(html_file)
    else:
        return HTMLResponse("real_face_animation.html not found", status_code=404)

@app.get("/real_face_animation.js")
async def get_real_face_animation_js():
    """返回真实人脸动画JS文件"""
    js_file = Path(__file__).parent / "real_face_animation.js"
    if js_file.exists():
        return FileResponse(js_file, media_type="application/javascript")
    else:
        return HTMLResponse("// real_face_animation.js not found", status_code=404)

@app.get("/summary")
async def get_project_summary():
    """返回项目技术总结可视化页面"""
    summary_file = Path(__file__).parent / "project_summary.html"
    if summary_file.exists():
        return FileResponse(summary_file)
    else:
        return HTMLResponse("project_summary.html not found", status_code=404)

@app.get("/process")
async def get_process_models_analysis():
    """返回流程与模型详细分析页面"""
    process_file = Path(__file__).parent / "process_models_analysis.html"
    if process_file.exists():
        return FileResponse(process_file)
    else:
        return HTMLResponse("process_models_analysis.html not found", status_code=404)

@app.get("/map")
async def get_map_demo():
    """返回高德地图集成演示页面"""
    map_file = Path(__file__).parent / "map_demo.html"
    if map_file.exists():
        return FileResponse(map_file)
    else:
        return HTMLResponse("map_demo.html not found", status_code=404)

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "rag_initialized": server.is_initialized,
        "connected_clients": len(server.connected_clients),
        "rag_available": RAG_AVAILABLE
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接处理"""
    await websocket.accept()
    server.add_client(websocket)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                logger.info(f"📥 收到消息: {message.get('type', 'unknown')}")
                
                if message.get('type') == 'query':
                    query_text = message.get('text', '').strip()

                    if not query_text:
                        await websocket.send_text(json.dumps({
                            'type': 'error',
                            'message': '查询内容不能为空'
                        }, ensure_ascii=False))
                        continue

                    # 处理查询
                    response = await server.process_query(query_text)

                    # 如果查询成功，生成高质量语音
                    if response.get('type') == 'response' and EDGE_TTS_AVAILABLE:
                        logger.info("🎤 正在生成Edge-TTS语音...")
                        audio_base64 = await server.generate_edge_tts_audio(
                            response['text'],
                            response.get('language', 'zh-CN')
                        )

                        if audio_base64:
                            response['audio'] = audio_base64
                            response['audio_type'] = 'edge-tts'
                            logger.info("✅ Edge-TTS语音已添加到响应")
                        else:
                            logger.warning("⚠️ Edge-TTS语音生成失败，前端将使用浏览器TTS")

                    # 发送回答
                    await websocket.send_text(json.dumps(response, ensure_ascii=False))
                    
                else:
                    await websocket.send_text(json.dumps({
                        'type': 'error',
                        'message': f'未知消息类型: {message.get("type")}'
                    }, ensure_ascii=False))
                    
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    'type': 'error',
                    'message': '消息格式错误'
                }, ensure_ascii=False))
            except Exception as e:
                logger.error(f"❌ 消息处理错误: {e}")
                await websocket.send_text(json.dumps({
                    'type': 'error',
                    'message': f'服务器内部错误: {str(e)}'
                }, ensure_ascii=False))
                
    except WebSocketDisconnect:
        logger.info("🔌 客户端主动断开连接")
    except Exception as e:
        logger.error(f"❌ WebSocket错误: {e}")
    finally:
        server.remove_client(websocket)

# 高德地图API接口
@app.get("/api/amap/search-around")
async def search_around(location: str, keywords: str, radius: int = 1000, types: str = "", page: int = 1, offset: int = 20):
    """周边搜索API"""
    try:
        from amap_api import AmapAPI
        async with AmapAPI() as amap:
            results = await amap.search_around(location, keywords, radius, types, page, offset)
            return {"status": "success", "data": [
                {
                    "id": poi.id,
                    "name": poi.name,
                    "type": poi.type,
                    "address": poi.address,
                    "location": {"lng": poi.location.longitude, "lat": poi.location.latitude},
                    "distance": poi.distance,
                    "tel": poi.tel,
                    "business_area": poi.business_area
                } for poi in results
            ]}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/amap/weather")
async def get_weather(city: str, extensions: str = "base"):
    """天气查询API"""
    try:
        from amap_api import AmapAPI
        async with AmapAPI() as amap:
            result = await amap.get_weather_forecast(city, extensions)
            if result:
                return {"status": "success", "data": {
                    "province": result.province,
                    "city": result.city,
                    "adcode": result.adcode,
                    "weather": result.weather,
                    "temperature": result.temperature,
                    "winddirection": result.winddirection,
                    "windpower": result.windpower,
                    "humidity": result.humidity,
                    "reporttime": result.reporttime
                }}
            else:
                return {"status": "error", "message": "天气查询失败"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def main():
    """主函数"""
    print("🚀 启动RAG数字人Web服务器")
    print("=" * 50)
    print("📱 访问地址: http://localhost:8000")
    print("🤖 支持功能:")
    print("  - 智能问答 (基于RAG)")
    print("  - 语音输入 (浏览器原生)")
    print("  - 语音输出 (浏览器TTS)")
    print("  - 数字人动画 (Canvas 2D)")
    print("  - 实时交互 (WebSocket)")
    print("=" * 50)
    
    if not RAG_AVAILABLE:
        print("⚠️ 警告: SimpleVoiceRAG不可用，部分功能可能无法正常工作")
        print("请确保simple_voice_rag.py在同一目录下")
    
    # 检查必要文件
    required_files = [
        "web_interface.html",
        "avatar_engine.js", 
        "web_client.js"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        print("请确保所有文件都在同一目录下")
        return
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
