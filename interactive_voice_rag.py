#!/usr/bin/env python
"""
交互式语音RAG系统
支持语音输入、文字输入、文字回复、语音回复的完整交互系统
"""

import asyncio
import os
import sys
import tempfile
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from raganything import RAGAnything, RAGAnythingConfig
from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc

# 检查语音依赖
try:
    import whisper
    import sounddevice as sd
    import numpy as np
    VOICE_INPUT_AVAILABLE = True
    logger.info("✅ 语音输入可用")
except ImportError:
    VOICE_INPUT_AVAILABLE = False
    logger.warning("❌ 语音输入不可用，请安装: pip install openai-whisper sounddevice")

try:
    import edge_tts
    import pygame
    VOICE_OUTPUT_AVAILABLE = True
    logger.info("✅ 语音输出可用")
except ImportError:
    VOICE_OUTPUT_AVAILABLE = False
    logger.warning("❌ 语音输出不可用，请安装: pip install edge-tts pygame")

# 系统TTS备用
SYSTEM_TTS_AVAILABLE = False
try:
    import platform
    if platform.system() == "Darwin":  # macOS
        SYSTEM_TTS_AVAILABLE = True
        logger.info("✅ 系统TTS可用")
except:
    pass


class InteractiveVoiceRAG:
    """交互式语音RAG系统"""
    
    def __init__(self):
        self.api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.rag_system = None
        self.whisper_model = None
        
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY或OPENAI_API_KEY环境变量")
        
        logger.info(f"🔑 API密钥: {self.api_key[:8]}...{self.api_key[-4:]}")
        
        # 初始化语音识别
        if VOICE_INPUT_AVAILABLE:
            try:
                self.whisper_model = whisper.load_model("base")
                logger.info("✅ Whisper模型加载成功")
            except Exception as e:
                logger.error(f"❌ Whisper模型加载失败: {e}")
                self.whisper_model = None
        
        # 初始化语音输出
        if VOICE_OUTPUT_AVAILABLE:
            try:
                pygame.mixer.init()
                logger.info("✅ 语音输出初始化成功")
            except Exception as e:
                logger.error(f"❌ 语音输出初始化失败: {e}")
    
    async def initialize_rag(self):
        """初始化RAG系统"""
        logger.info("🔧 正在初始化RAG系统...")
        
        working_dir = "./rag_storage_server"
        if not os.path.exists(working_dir):
            logger.error(f"❌ 知识库目录不存在: {working_dir}")
            return False
        
        try:
            from lightrag import LightRAG
            from lightrag.kg.shared_storage import initialize_pipeline_status
            
            def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
                return openai_complete_if_cache(
                    "qwen-turbo",
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    api_key=self.api_key,
                    base_url=self.base_url,
                    **kwargs,
                )
            
            embedding_func = EmbeddingFunc(
                embedding_dim=1536,
                max_token_size=8192,
                func=lambda texts: openai_embed(
                    texts,
                    model="text-embedding-v1",
                    api_key=self.api_key,
                    base_url=self.base_url,
                ),
            )
            
            lightrag_instance = LightRAG(
                working_dir=working_dir,
                llm_model_func=llm_model_func,
                embedding_func=embedding_func,
            )
            
            await lightrag_instance.initialize_storages()
            await initialize_pipeline_status()
            
            self.rag_system = RAGAnything(lightrag=lightrag_instance)
            
            logger.info("✅ RAG系统初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ RAG系统初始化失败: {e}")
            return False
    
    def record_audio(self, duration=5, sample_rate=16000):
        """录制音频"""
        try:
            print(f"🎤 开始录音 {duration} 秒，请说话...")
            audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype=np.float32)
            sd.wait()
            print("🎤 录音完成")
            return audio.flatten()
        except Exception as e:
            logger.error(f"❌ 录音失败: {e}")
            return None
    
    def speech_to_text(self, audio, sample_rate=16000):
        """语音转文字"""
        if not self.whisper_model:
            return None
        
        try:
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                import scipy.io.wavfile as wav
                wav.write(temp_file.name, sample_rate, (audio * 32767).astype(np.int16))
                
                # 使用Whisper识别
                result = self.whisper_model.transcribe(temp_file.name, language="zh")
                
                # 清理临时文件
                os.unlink(temp_file.name)
                
                return result["text"].strip()
                
        except Exception as e:
            logger.error(f"❌ 语音识别失败: {e}")
            return None
    
    def clean_text_for_speech(self, text):
        """清理文本用于语音播放"""
        import re
        
        # 处理编码问题
        try:
            text = text.encode('utf-8', errors='ignore').decode('utf-8')
        except:
            pass
        
        # 移除代理对字符
        text = re.sub(r'[\ud800-\udfff]', '', text)
        
        # 移除星号和特殊标记
        text = re.sub(r'\*+', '', text)
        text = re.sub(r'[#\[\]]+', '', text)
        text = re.sub(r'References?:', '', text)
        text = re.sub(r'\[KG\].*?\.pdf', '', text)
        text = re.sub(r'\[DC\].*?\.pdf', '', text)
        
        # 清理空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 限制长度为2000字符
        if len(text) > 2000:
            sentences = text.split('。')
            result = ""
            for sentence in sentences:
                if len(result + sentence + '。') <= 2000:
                    result += sentence + '。'
                else:
                    break
            text = result.rstrip('。') + '。'
            logger.info(f"📝 文本过长，已截取至{len(text)}字符")
        
        return text
    
    async def text_to_speech(self, text):
        """文本转语音"""
        cleaned_text = self.clean_text_for_speech(text)
        
        if not cleaned_text.strip():
            return False
        
        # 尝试Edge-TTS
        if VOICE_OUTPUT_AVAILABLE:
            try:
                communicate = edge_tts.Communicate(cleaned_text, "zh-CN-XiaoxiaoNeural")
                
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    audio_file = temp_file.name
                
                await asyncio.wait_for(communicate.save(audio_file), timeout=60.0)
                
                if os.path.exists(audio_file) and os.path.getsize(audio_file) > 0:
                    pygame.mixer.music.load(audio_file)
                    pygame.mixer.music.play()
                    
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)
                    
                    os.unlink(audio_file)
                    return True
                    
            except Exception as e:
                logger.warning(f"⚠️ Edge-TTS失败: {e}")
        
        # 尝试系统TTS
        if SYSTEM_TTS_AVAILABLE:
            try:
                import subprocess
                process = subprocess.Popen(["say", cleaned_text])
                await asyncio.get_event_loop().run_in_executor(None, process.wait)
                return True
            except Exception as e:
                logger.warning(f"⚠️ 系统TTS失败: {e}")
        
        return False
    
    async def process_query(self, question):
        """处理查询"""
        try:
            logger.info(f"❓ 查询问题: {question}")
            
            # 查询知识库
            response = await self.rag_system.aquery(question, mode="hybrid")
            
            # 显示文字回答
            print(f"\n📝 回答:\n{response}")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 查询失败: {e}")
            return None
    
    async def voice_input_mode(self):
        """语音输入模式"""
        if not VOICE_INPUT_AVAILABLE:
            print("❌ 语音输入不可用，请安装相关依赖")
            return None
        
        # 录音
        audio = self.record_audio(duration=5)
        if audio is None:
            return None
        
        # 语音识别
        question = self.speech_to_text(audio)
        if not question:
            print("❌ 语音识别失败")
            return None
        
        print(f"🎤 识别结果: {question}")
        return question

    async def continuous_chat_mode(self):
        """连续对话模式"""
        print("\n💬 进入连续对话模式")
        print("=" * 30)
        print("输入 'quit' 或 'exit' 退出连续对话")
        print("输入 'voice' 切换到语音输入")
        print("=" * 30)

        while True:
            try:
                user_input = input("\n💬 您: ").strip()

                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    print("📱 退出连续对话模式")
                    break

                if user_input.lower() in ['voice', '语音']:
                    # 语音输入
                    question = await self.voice_input_mode()
                    if not question:
                        continue
                else:
                    question = user_input

                if not question:
                    continue

                # 处理查询
                response = await self.process_query(question)

                if response:
                    # 自动语音播放（如果可用）
                    if VOICE_OUTPUT_AVAILABLE or SYSTEM_TTS_AVAILABLE:
                        print("🔊 正在播放语音...")
                        await self.text_to_speech(response)

            except KeyboardInterrupt:
                print("\n📱 退出连续对话模式")
                break
            except Exception as e:
                logger.error(f"❌ 对话错误: {e}")
    
    async def interactive_loop(self):
        """交互循环"""
        print("\n🎤 交互式语音RAG系统")
        print("=" * 50)
        print("📊 系统状态:")
        print(f"  🎤 语音输入: {'✅ 可用' if VOICE_INPUT_AVAILABLE else '❌ 不可用'}")
        print(f"  🔊 语音输出: {'✅ 可用' if VOICE_OUTPUT_AVAILABLE or SYSTEM_TTS_AVAILABLE else '❌ 不可用'}")
        print(f"  📚 知识库: ✅ 已加载")
        print("=" * 50)
        print("选择输入方式:")
        print("1. 语音输入 (说话)")
        print("2. 文字输入 (打字)")
        print("3. 连续对话模式")
        print("4. 退出")
        print("=" * 50)
        
        while True:
            try:
                choice = input("\n请选择 (1/2/3/4): ").strip()

                if choice == "4":
                    print("👋 再见！")
                    break
                elif choice == "3":
                    # 连续对话模式
                    await self.continuous_chat_mode()
                    continue
                
                question = None
                
                if choice == "1":
                    # 语音输入
                    question = await self.voice_input_mode()
                elif choice == "2":
                    # 文字输入
                    question = input("💬 请输入您的问题: ").strip()
                else:
                    print("❌ 无效选择，请重新选择")
                    continue
                
                if not question:
                    print("❌ 未获取到问题，请重试")
                    continue
                
                # 处理查询
                response = await self.process_query(question)
                
                if response:
                    # 询问是否需要语音播放
                    tts_choice = input("\n🔊 是否播放语音回答? (y/n): ").strip().lower()
                    if tts_choice in ['y', 'yes', '是', '1']:
                        print("🔊 正在播放语音...")
                        success = await self.text_to_speech(response)
                        if success:
                            print("✅ 语音播放完成")
                        else:
                            print("⚠️ 语音播放失败")
                
                print("\n" + "=" * 50)
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，再见！")
                break
            except Exception as e:
                logger.error(f"❌ 交互错误: {e}")


async def main():
    """主函数"""
    try:
        # 创建系统实例
        voice_rag = InteractiveVoiceRAG()
        
        # 初始化RAG系统
        if not await voice_rag.initialize_rag():
            print("❌ 系统初始化失败")
            return
        
        # 开始交互
        await voice_rag.interactive_loop()
        
    except Exception as e:
        logger.error(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
