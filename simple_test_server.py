#!/usr/bin/env python3
"""
简单测试服务器 - 验证Web功能是否正常
"""

import os
import json
import asyncio
from pathlib import Path

try:
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect
    from fastapi.responses import HTMLResponse, FileResponse
    import uvicorn
    print("✅ FastAPI和Uvicorn导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请安装: pip install fastapi uvicorn")
    exit(1)

# 创建FastAPI应用
app = FastAPI(title="RAG数字人测试服务器")

@app.get("/")
async def get_homepage():
    """返回测试页面"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <title>RAG数字人测试</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                max-width: 800px;
                margin: 50px auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 20px;
                backdrop-filter: blur(10px);
            }
            .status {
                padding: 10px;
                margin: 10px 0;
                border-radius: 10px;
                background: rgba(255,255,255,0.2);
            }
            .success { background: rgba(52, 199, 89, 0.8); }
            .error { background: rgba(255, 59, 48, 0.8); }
            input, button {
                padding: 12px;
                margin: 5px;
                border: none;
                border-radius: 10px;
                font-size: 16px;
            }
            button {
                background: #007AFF;
                color: white;
                cursor: pointer;
            }
            button:hover {
                background: #0056CC;
            }
            #messages {
                height: 300px;
                overflow-y: auto;
                background: rgba(0,0,0,0.2);
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
            }
            .message {
                margin: 10px 0;
                padding: 10px;
                border-radius: 8px;
                background: rgba(255,255,255,0.1);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 RAG数字人系统测试</h1>
            
            <div class="status success">
                ✅ Web服务器运行正常
            </div>
            
            <div class="status" id="wsStatus">
                🔄 正在连接WebSocket...
            </div>
            
            <div>
                <h3>💬 测试聊天功能</h3>
                <div id="messages"></div>
                <input type="text" id="messageInput" placeholder="输入测试消息..." style="width: 70%;">
                <button onclick="sendMessage()">发送</button>
                <button onclick="testVoice()">🎤 测试语音</button>
            </div>
            
            <div>
                <h3>🔧 系统信息</h3>
                <p>服务器地址: <strong>http://localhost:8000</strong></p>
                <p>WebSocket地址: <strong>ws://localhost:8000/ws</strong></p>
                <p>API密钥状态: <span id="apiStatus">检查中...</span></p>
            </div>
        </div>

        <script>
            let ws = null;
            let isConnected = false;

            // 初始化WebSocket连接
            function initWebSocket() {
                try {
                    ws = new WebSocket('ws://localhost:8000/ws');
                    
                    ws.onopen = function() {
                        isConnected = true;
                        document.getElementById('wsStatus').innerHTML = '✅ WebSocket连接成功';
                        document.getElementById('wsStatus').className = 'status success';
                        addMessage('系统', 'WebSocket连接成功！');
                    };
                    
                    ws.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        addMessage('服务器', data.message || JSON.stringify(data));
                    };
                    
                    ws.onclose = function() {
                        isConnected = false;
                        document.getElementById('wsStatus').innerHTML = '❌ WebSocket连接断开';
                        document.getElementById('wsStatus').className = 'status error';
                        addMessage('系统', 'WebSocket连接断开');
                    };
                    
                    ws.onerror = function(error) {
                        console.error('WebSocket错误:', error);
                        document.getElementById('wsStatus').innerHTML = '❌ WebSocket连接失败';
                        document.getElementById('wsStatus').className = 'status error';
                    };
                } catch (error) {
                    console.error('WebSocket初始化失败:', error);
                }
            }

            // 发送消息
            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (!message) return;
                
                if (!isConnected) {
                    alert('WebSocket未连接');
                    return;
                }
                
                addMessage('您', message);
                
                ws.send(JSON.stringify({
                    type: 'test',
                    message: message
                }));
                
                input.value = '';
            }

            // 测试语音功能
            function testVoice() {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance('语音测试成功！Web RAG数字人系统运行正常。');
                    utterance.lang = 'zh-CN';
                    speechSynthesis.speak(utterance);
                    addMessage('系统', '语音测试：播放测试语音');
                } else {
                    addMessage('系统', '浏览器不支持语音合成');
                }
            }

            // 添加消息到聊天区域
            function addMessage(sender, content) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }

            // 检查API状态
            function checkAPIStatus() {
                fetch('/api-status')
                    .then(response => response.json())
                    .then(data => {
                        const status = data.api_configured ? '✅ 已配置' : '❌ 未配置';
                        document.getElementById('apiStatus').innerHTML = status;
                    })
                    .catch(error => {
                        document.getElementById('apiStatus').innerHTML = '❌ 检查失败';
                    });
            }

            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                initWebSocket();
                checkAPIStatus();
                
                // 回车发送消息
                document.getElementById('messageInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            });
        </script>
    </body>
    </html>
    """)

@app.get("/api-status")
async def api_status():
    """检查API状态"""
    api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
    return {
        "api_configured": bool(api_key),
        "api_key_preview": f"{api_key[:8]}...{api_key[-4:]}" if api_key else None
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket测试端点"""
    await websocket.accept()
    print("🔗 WebSocket客户端连接")
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            print(f"📥 收到消息: {message}")
            
            # 回显消息
            response = {
                "type": "response",
                "message": f"收到您的消息: {message.get('message', '')}",
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await websocket.send_text(json.dumps(response, ensure_ascii=False))
            
    except WebSocketDisconnect:
        print("🔌 WebSocket客户端断开")
    except Exception as e:
        print(f"❌ WebSocket错误: {e}")

def main():
    """启动测试服务器"""
    print("🚀 启动RAG数字人测试服务器")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
    if api_key:
        print(f"✅ API密钥已设置: {api_key[:8]}...{api_key[-4:]}")
    else:
        print("⚠️ API密钥未设置")
    
    print("📱 访问地址: http://localhost:8000")
    print("🧪 这是一个测试服务器，用于验证基础功能")
    print("=" * 50)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
