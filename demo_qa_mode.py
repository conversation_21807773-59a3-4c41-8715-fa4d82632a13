#!/usr/bin/env python
"""
RAGAnything Q&A Mode Demo

This script demonstrates the Q&A mode functionality with a sample document.
Run this to see how the interactive Q&A mode works.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc, logger
from raganything import RAGAnything, RAGAnythingConfig
from raganything.qa_mode import QASession, InteractiveQA


def create_sample_document():
    """Create a sample document for demonstration"""
    sample_content = """
# RAGAnything 技术文档

## 概述
RAGAnything是一个先进的多模态检索增强生成系统，支持文档解析、知识图谱构建和智能问答。

## 主要功能
1. **多模态文档解析**: 支持PDF、图像、Office文档的解析
2. **知识图谱构建**: 自动提取实体和关系，构建知识图谱
3. **智能检索**: 支持向量检索和图谱推理的混合检索
4. **交互式问答**: 提供友好的问答界面

## 技术架构
- 解析层: MinerU多模态解析
- 框架层: LightRAG知识图谱
- 模型层: 阿里云百炼API
- 硬件层: GPU加速支持

## 性能指标
- 解析准确率: >95%
- 查询响应时间: 1-3秒
- 多模态理解准确率: >85%

## 应用场景
- 学术研究文献分析
- 企业知识管理
- 法律文档处理
- 医疗报告分析
"""
    
    sample_file = "sample_document.txt"
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    return sample_file


async def demo_qa_mode():
    """Demonstrate Q&A mode functionality"""
    
    print("🎭 RAGAnything Q&A Mode 演示")
    print("=" * 40)
    
    # Check if we have a real API key
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key or api_key == "sk-your-api-key-here":
        print("⚠️ 未检测到有效的API密钥，将使用模拟模式演示")
        use_mock = True
        api_key = "mock-api-key"
    else:
        print(f"✅ 检测到API密钥: {api_key[:8]}...{api_key[-4:]}")
        use_mock = False
    
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    working_dir = "./demo_rag_storage"
    
    # Create sample document
    sample_file = create_sample_document()
    print(f"📄 创建示例文档: {sample_file}")
    
    # Create configuration
    config = RAGAnythingConfig(
        working_dir=working_dir,
        mineru_parse_method="auto",
        enable_image_processing=True,
        enable_table_processing=True,
        enable_equation_processing=True,
    )

    # Define model functions
    if use_mock:
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            # Mock responses based on query content
            if "主要功能" in prompt or "功能" in prompt:
                return "RAGAnything的主要功能包括：1) 多模态文档解析，支持PDF、图像、Office文档；2) 知识图谱构建，自动提取实体和关系；3) 智能检索，结合向量检索和图谱推理；4) 交互式问答，提供友好的对话界面。"
            elif "技术架构" in prompt or "架构" in prompt:
                return "RAGAnything采用四层技术架构：解析层使用MinerU进行多模态解析，框架层使用LightRAG构建知识图谱，模型层集成阿里云百炼API，硬件层支持GPU加速。"
            elif "性能" in prompt or "指标" in prompt:
                return "RAGAnything的性能指标包括：解析准确率超过95%，查询响应时间1-3秒，多模态理解准确率超过85%。"
            elif "应用" in prompt or "场景" in prompt:
                return "RAGAnything适用于多种场景：学术研究文献分析、企业知识管理、法律文档处理、医疗报告分析等。"
            else:
                return f"基于文档内容，针对您的查询'{prompt[:50]}...'，这是一个模拟回答。在实际使用中会调用真实的LLM API提供更准确的答案。"

        def vision_model_func(prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs):
            if image_data:
                return f"这是对包含图像的查询'{prompt[:50]}...'的模拟多模态回答。"
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

        def mock_embedding_func(texts):
            import random
            # Return mock embeddings with some randomness
            return [[random.random() for _ in range(1536)] for _ in texts]

        embedding_func = EmbeddingFunc(
            embedding_dim=1536,
            max_token_size=8192,
            func=mock_embedding_func,
        )
    else:
        # Real API functions
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return openai_complete_if_cache(
                "qwen-turbo",
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )

        def vision_model_func(prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs):
            if image_data:
                return openai_complete_if_cache(
                    "qwen-vl-plus",
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt} if system_prompt else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}},
                            ],
                        } if image_data else {"role": "user", "content": prompt},
                    ],
                    api_key=api_key,
                    base_url=base_url,
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

        embedding_func = EmbeddingFunc(
            embedding_dim=1536,
            max_token_size=8192,
            func=lambda texts: openai_embed(
                texts,
                model="text-embedding-v1",
                api_key=api_key,
                base_url=base_url,
            ),
        )

    # Initialize RAGAnything
    rag = RAGAnything(
        config=config,
        llm_model_func=llm_model_func,
        vision_model_func=vision_model_func,
        embedding_func=embedding_func,
    )

    print("✅ RAGAnything系统初始化完成")
    
    # Process the sample document (simplified for demo)
    print(f"\n📖 处理示例文档...")
    try:
        if not use_mock:
            # Only process document with real API
            await rag.process_document_complete(
                file_path=sample_file,
                output_dir="./demo_output",
                parse_method="auto",
                device="cpu",
                backend="pipeline"
            )
        print("✅ 文档处理完成")
    except Exception as e:
        print(f"⚠️ 文档处理跳过 (演示模式): {e}")

    # Create demo session with some sample interactions
    session = QASession("demo_session")
    
    # Add some sample interactions to show history
    session.add_interaction(
        query="RAGAnything有哪些主要功能？",
        response="RAGAnything的主要功能包括多模态文档解析、知识图谱构建、智能检索和交互式问答。",
        mode="hybrid",
        response_time=1.2
    )
    
    session.add_interaction(
        query="系统的技术架构是怎样的？",
        response="RAGAnything采用四层架构：解析层、框架层、模型层和硬件层。",
        mode="hybrid",
        response_time=1.8
    )

    # Initialize Q&A interface
    qa = InteractiveQA(rag, session)
    
    print("✅ Q&A接口初始化完成")
    
    # Demo automatic queries
    print(f"\n🤖 演示自动问答...")
    demo_queries = [
        "RAGAnything的性能指标如何？",
        "这个系统适用于哪些应用场景？",
        "技术架构的各层分别负责什么？"
    ]
    
    for i, query in enumerate(demo_queries, 1):
        print(f"\n❓ 演示查询 {i}: {query}")
        try:
            response = await qa.process_query(query)
            print(f"🤖 回答: {response}")
            print("-" * 40)
        except Exception as e:
            print(f"❌ 查询失败: {e}")
    
    # Show session statistics
    print(f"\n📊 会话统计:")
    qa.display_stats()
    
    # Show recent history
    print(f"\n📝 对话历史:")
    qa.display_history(3)
    
    # Demo mode switching
    print(f"\n🔄 演示模式切换:")
    print(f"当前模式: {qa.current_mode}")
    qa.current_mode = "local"
    print(f"切换后模式: {qa.current_mode}")
    
    # Demo context toggling
    print(f"\n💬 演示上下文切换:")
    print(f"当前上下文模式: {'开启' if qa.enable_context else '关闭'}")
    qa.toggle_context_mode()
    print(f"切换后上下文模式: {'开启' if qa.enable_context else '关闭'}")
    
    # Save demo session
    demo_session_file = "demo_session.json"
    session.save_session(demo_session_file)
    print(f"\n💾 演示会话已保存到: {demo_session_file}")
    
    print(f"\n🎉 演示完成！")
    print(f"\n💡 要启动真实的交互式问答模式，请运行:")
    if use_mock:
        print(f"   1. 首先配置您的API密钥到.env文件")
        print(f"   2. 然后运行: python examples/qa_mode_example.py {sample_file}")
    else:
        print(f"   python examples/qa_mode_example.py {sample_file} --api-key {api_key[:8]}...")
    print(f"   或使用启动脚本: ./run_qa_mode.sh {sample_file}")
    
    # Clean up
    try:
        os.remove(sample_file)
        print(f"\n🧹 清理演示文件完成")
    except:
        pass


async def main():
    """Main demo function"""
    try:
        await demo_qa_mode()
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")


if __name__ == "__main__":
    asyncio.run(main())
