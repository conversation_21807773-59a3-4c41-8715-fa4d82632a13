#!/usr/bin/env python3
"""
快速测试MinerU
"""

import subprocess
import os

def quick_test():
    print("🚀 快速MinerU测试")
    
    # 1. 测试magic-pdf命令
    pdf_path = '/data/RAG-Anything/sample_questions.pdf'
    output_dir = './quick_test_output'
    
    cmd = ['magic-pdf', '-p', pdf_path, '-o', output_dir, '-m', 'txt']
    
    try:
        print(f"执行: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ magic-pdf执行成功")
            
            # 检查输出
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if file.endswith('.md'):
                        file_path = os.path.join(root, file)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            print(f"📄 文件: {file}")
                            print(f"📏 长度: {len(content)} 字符")
                            if content.strip():
                                print(f"📝 预览: {content[:200]}...")
                                return True
        else:
            print("❌ magic-pdf执行失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return False

if __name__ == "__main__":
    if quick_test():
        print("\n🎉 测试成功！可以运行RAGAnything")
    else:
        print("\n❌ 测试失败")
