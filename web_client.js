/**
 * Web RAG客户端 - macOS优化版
 * 集成数字人、语音识别、WebSocket通信
 */
class WebRAGClient {
    constructor() {
        // 核心组件
        this.avatar = null;
        this.websocket = null;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        
        // 状态管理
        this.isConnected = false;
        this.isListening = false;
        this.isSpeaking = false;
        
        // DOM元素
        this.elements = {
            canvas: document.getElementById('avatarCanvas'),
            status: document.getElementById('avatarStatus'),
            connectionStatus: document.getElementById('connectionStatus'),
            messagesContainer: document.getElementById('messagesContainer'),
            textInput: document.getElementById('textInput'),
            voiceBtn: document.getElementById('voiceBtn'),
            sendBtn: document.getElementById('sendBtn'),
            loadingIndicator: document.getElementById('loadingIndicator')
        };
        
        // 初始化
        this.initialize();
    }

    async initialize() {
        try {
            // 初始化数字人
            this.avatar = new MacOSAvatarEngine(this.elements.canvas);
            console.log('✅ 数字人引擎初始化成功');

            // 初始化语音系统
            this.initializeSpeechSynthesis();

            // 初始化WebSocket连接
            this.initializeWebSocket();

            // 初始化语音识别
            this.initializeSpeechRecognition();

            // 设置事件监听器
            this.setupEventListeners();

            // 初始化音频上下文
            this.initializeAudioContext();

            // 更新状态
            this.updateAvatarStatus('已就绪');

        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.updateConnectionStatus('初始化失败', false);
        }
    }

    initializeSpeechSynthesis() {
        /**
         * 初始化语音合成系统，预加载语音列表
         */
        if (this.synthesis) {
            // 触发语音列表加载
            const voices = this.synthesis.getVoices();

            if (voices.length === 0) {
                // 如果语音列表为空，监听加载完成事件
                this.synthesis.addEventListener('voiceschanged', () => {
                    const loadedVoices = this.synthesis.getVoices();
                    console.log(`🎤 语音列表已加载，共 ${loadedVoices.length} 个语音`);

                    // 打印可用的中文和英文语音
                    const chineseVoices = loadedVoices.filter(v => v.lang.startsWith('zh'));
                    const englishVoices = loadedVoices.filter(v => v.lang.startsWith('en'));

                    console.log('🇨🇳 可用中文语音:', chineseVoices.map(v => v.name));
                    console.log('🇺🇸 可用英文语音:', englishVoices.map(v => v.name));
                });
            } else {
                console.log(`🎤 语音列表已就绪，共 ${voices.length} 个语音`);
            }
        }
    }

    initializeAudioContext() {
        /**
         * 初始化音频上下文，确保用户交互后可以播放音频
         */
        this.audioContextInitialized = false;

        // 添加点击事件监听器来初始化音频
        const initAudio = () => {
            if (!this.audioContextInitialized) {
                try {
                    // 创建一个静音音频来初始化音频上下文
                    const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    silentAudio.volume = 0;
                    silentAudio.play().then(() => {
                        this.audioContextInitialized = true;
                        console.log('✅ 音频上下文已初始化');
                        document.removeEventListener('click', initAudio);
                        document.removeEventListener('touchstart', initAudio);
                    }).catch(() => {
                        console.log('⚠️ 音频上下文初始化失败');
                    });
                } catch (error) {
                    console.log('⚠️ 音频上下文初始化异常:', error);
                }
            }
        };

        // 监听用户交互
        document.addEventListener('click', initAudio);
        document.addEventListener('touchstart', initAudio);
    }

    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            this.isConnected = true;
            this.updateConnectionStatus('已连接', true);
            console.log('✅ WebSocket连接成功');
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleServerMessage(data);
            } catch (error) {
                console.error('❌ 消息解析失败:', error);
            }
        };

        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            this.updateConnectionStatus('连接错误', false);
        };

        this.websocket.onclose = () => {
            this.isConnected = false;
            this.updateConnectionStatus('连接断开', false);
            console.log('🔌 WebSocket连接关闭');
            
            // 尝试重连
            setTimeout(() => {
                if (!this.isConnected) {
                    console.log('🔄 尝试重新连接...');
                    this.initializeWebSocket();
                }
            }, 3000);
        };
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            // 配置语音识别
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'zh-CN';
            
            // 事件处理
            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateVoiceButton(true);
                this.avatar.setStatus('listening');
                this.updateAvatarStatus('正在听取...');
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.textInput.value = transcript;
                this.sendQuery(transcript);
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceButton(false);
                this.avatar.setStatus('idle');
                this.updateAvatarStatus('已就绪');
            };

            this.recognition.onerror = (event) => {
                console.error('❌ 语音识别错误:', event.error);
                this.isListening = false;
                this.updateVoiceButton(false);
                this.avatar.setStatus('idle');
                this.updateAvatarStatus('语音识别失败');
                
                if (event.error === 'not-allowed') {
                    alert('请允许麦克风权限以使用语音输入功能');
                }
            };

            console.log('✅ 语音识别初始化成功');
        } else {
            console.warn('⚠️ 浏览器不支持语音识别');
        }
    }

    setupEventListeners() {
        // 发送按钮
        this.elements.sendBtn.addEventListener('click', () => {
            const text = this.elements.textInput.value.trim();
            if (text) {
                this.sendQuery(text);
                this.elements.textInput.value = '';
            }
        });

        // 回车发送
        this.elements.textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.elements.sendBtn.click();
            }
        });

        // 语音按钮
        this.elements.voiceBtn.addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // 输入框焦点处理
        this.elements.textInput.addEventListener('focus', () => {
            this.avatar.setStatus('listening');
        });

        this.elements.textInput.addEventListener('blur', () => {
            if (!this.isListening && !this.isSpeaking) {
                this.avatar.setStatus('idle');
            }
        });
    }

    toggleVoiceInput() {
        if (!this.recognition) {
            alert('您的浏览器不支持语音识别功能');
            return;
        }

        if (this.isListening) {
            this.recognition.stop();
        } else {
            try {
                this.recognition.start();
            } catch (error) {
                console.error('❌ 启动语音识别失败:', error);
                alert('启动语音识别失败，请检查麦克风权限');
            }
        }
    }

    sendQuery(text) {
        if (!this.isConnected) {
            alert('未连接到服务器，请稍后重试');
            return;
        }

        if (!text.trim()) {
            return;
        }

        // 显示用户消息
        this.addMessage('user', text);
        
        // 显示加载状态
        this.showLoading(true);
        this.avatar.setStatus('thinking');
        this.updateAvatarStatus('正在思考...');

        // 设置超时处理
        const queryTimeout = setTimeout(() => {
            console.warn('⚠️ 查询超时，可能是回答太长');
            this.showLoading(false);
            this.avatar.setStatus('idle');
            this.updateAvatarStatus('查询超时，请重试');
            this.addMessage('system', '查询超时，请尝试更简短的问题或重新提问');
        }, 30000); // 30秒超时

        // 发送到服务器
        const message = {
            type: 'query',
            text: text.trim(),
            timestamp: Date.now()
        };

        try {
            this.websocket.send(JSON.stringify(message));
            console.log('📤 发送查询:', text);

            // 保存超时ID以便在收到响应时清除
            this.currentQueryTimeout = queryTimeout;

        } catch (error) {
            console.error('❌ 发送消息失败:', error);
            clearTimeout(queryTimeout);
            this.showLoading(false);
            this.avatar.setStatus('idle');
            this.updateAvatarStatus('发送失败');
        }
    }

    handleServerMessage(data) {
        console.log('📥 收到服务器消息:', data);

        switch (data.type) {
            case 'response':
                this.handleResponse(data);
                break;
            case 'error':
                this.handleError(data);
                break;
            default:
                console.warn('⚠️ 未知消息类型:', data.type);
        }
    }

    handleResponse(data) {
        // 清除查询超时
        if (this.currentQueryTimeout) {
            clearTimeout(this.currentQueryTimeout);
            this.currentQueryTimeout = null;
        }

        // 隐藏加载状态
        this.showLoading(false);

        // 显示回答
        this.addMessage('assistant', data.text);

        // 调试信息
        console.log('📥 收到响应数据:', {
            hasAudio: !!data.audio,
            audioType: data.audio_type,
            audioLength: data.audio ? data.audio.length : 0,
            textLength: data.text.length
        });

        // 播放语音并同步数字人动画
        if (data.audio && data.audio_type === 'edge-tts') {
            console.log('🎤 检测到Edge-TTS音频，开始播放');
            // 使用后端生成的高质量Edge-TTS语音
            this.playEdgeTTSAudio(data.audio, data.text);
        } else {
            console.log('🔄 使用浏览器TTS播放');
            // 降级到浏览器TTS
            this.speakResponse(data.text, data.language || 'zh-CN');
        }
    }

    handleError(data) {
        this.showLoading(false);
        this.avatar.setStatus('idle');
        this.updateAvatarStatus('处理失败');
        
        this.addMessage('system', `错误: ${data.message || '未知错误'}`);
        console.error('❌ 服务器错误:', data);
    }

    speakResponse(text, language = 'zh-CN') {
        if (!this.synthesis) {
            console.warn('⚠️ 浏览器不支持语音合成');
            return;
        }

        // 停止当前语音
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);

        // 配置语音参数，与后端Edge-TTS保持一致
        if (language === 'zh-CN' || language === 'zh') {
            utterance.lang = 'zh-CN';
            // 尝试选择与XiaoxiaoNeural相似的女声
            this.selectBestVoice(utterance, 'zh-CN', 'female');
        } else {
            utterance.lang = 'en-US';
            // 尝试选择与AriaNeural相似的女声
            this.selectBestVoice(utterance, 'en-US', 'female');
        }

        // 优化语音参数，使其更自然
        utterance.rate = 0.85;  // 稍慢一点，更清晰
        utterance.pitch = 1.1;  // 稍高一点，更女性化
        utterance.volume = 0.9;

        // 语音事件处理
        utterance.onstart = () => {
            this.isSpeaking = true;
            this.avatar.setStatus('speaking');
            this.updateAvatarStatus('正在回答...');
        };

        utterance.onend = () => {
            this.isSpeaking = false;
            this.avatar.setStatus('idle');
            this.updateAvatarStatus('已就绪');
        };

        utterance.onerror = (event) => {
            console.error('❌ 语音合成错误:', event);
            this.isSpeaking = false;
            this.avatar.setStatus('idle');
            this.updateAvatarStatus('语音播放失败');
        };

        // 在语音播放过程中更新数字人动画
        const updateAnimation = () => {
            if (this.isSpeaking) {
                this.avatar.updateSpeechAnimation();
                setTimeout(updateAnimation, 100);
            }
        };
        updateAnimation();

        // 开始语音播放
        this.synthesis.speak(utterance);
    }

    playEdgeTTSAudio(audioBase64, text) {
        /**
         * 播放后端生成的Edge-TTS高质量语音 - 改进版
         */
        try {
            console.log('🎤 开始处理Edge-TTS音频');
            console.log('📊 音频数据长度:', audioBase64.length);

            // 验证base64数据
            if (!audioBase64 || audioBase64.length < 100) {
                console.error('❌ 音频数据无效');
                this.speakResponse(text, 'zh-CN');
                return;
            }

            // 创建data URL方式播放（更兼容）
            const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;
            console.log('✅ 音频Data URL创建成功');

            // 创建音频元素
            const audio = new Audio();

            // 设置音频属性
            audio.preload = 'auto';
            audio.volume = 0.9;

            // 设置音频事件
            audio.addEventListener('loadstart', () => {
                console.log('🎤 音频开始加载');
                this.isSpeaking = true;
                this.avatar.setStatus('speaking');
                this.updateAvatarStatus('正在播放高质量语音...');
            });

            audio.addEventListener('canplay', () => {
                console.log('🎤 音频可以播放');
            });

            audio.addEventListener('play', () => {
                console.log('🎤 音频开始播放');
                this.startSpeechAnimation();
            });

            audio.addEventListener('ended', () => {
                console.log('✅ Edge-TTS语音播放完成');
                this.isSpeaking = false;
                this.avatar.setStatus('idle');
                this.updateAvatarStatus('已就绪');
            });

            audio.addEventListener('error', (event) => {
                console.error('❌ Edge-TTS音频播放失败:', event);
                console.error('错误详情:', audio.error);

                this.isSpeaking = false;
                this.avatar.setStatus('idle');
                this.updateAvatarStatus('语音播放失败，使用备用方案');

                // 降级到浏览器TTS
                console.log('🔄 降级到浏览器TTS');
                setTimeout(() => {
                    this.speakResponse(text, 'zh-CN');
                }, 500);
            });

            // 设置音频源并播放
            audio.src = audioDataUrl;

            // 尝试播放
            const playPromise = audio.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('✅ 音频播放启动成功');
                }).catch(error => {
                    console.error('❌ 音频播放启动失败:', error);

                    // 可能是用户交互问题，提示用户
                    this.updateAvatarStatus('请点击页面后重试');

                    // 延迟降级到浏览器TTS
                    setTimeout(() => {
                        console.log('🔄 降级到浏览器TTS');
                        this.speakResponse(text, 'zh-CN');
                    }, 1000);
                });
            }

        } catch (error) {
            console.error('❌ Edge-TTS音频处理失败:', error);
            // 降级到浏览器TTS
            this.speakResponse(text, 'zh-CN');
        }
    }

    startSpeechAnimation() {
        /**
         * 开始数字人说话动画，与音频同步
         */
        const animateFrame = () => {
            if (this.isSpeaking) {
                this.avatar.updateSpeechAnimation();
                setTimeout(animateFrame, 100); // 每100ms更新一次
            }
        };
        animateFrame();
    }

    selectBestVoice(utterance, language, gender = 'female') {
        /**
         * 选择最佳语音，尽量匹配后端Edge-TTS的效果
         */
        const voices = this.synthesis.getVoices();

        if (voices.length === 0) {
            // 如果语音列表还没加载，等待后重试
            setTimeout(() => {
                this.selectBestVoice(utterance, language, gender);
            }, 100);
            return;
        }

        let bestVoice = null;

        if (language === 'zh-CN') {
            // 中文语音优先级（尽量匹配XiaoxiaoNeural的效果）
            const chineseVoicePreferences = [
                'Microsoft Xiaoxiao - Chinese (Simplified, PRC)',
                'Microsoft Yaoyao - Chinese (Simplified, PRC)',
                'Microsoft Huihui - Chinese (Simplified, PRC)',
                'Ting-Ting (Enhanced) - Chinese (China)',
                'Ting-Ting - Chinese (China)',
                'Sin-ji - Chinese (Hong Kong SAR China)',
                'Mei-Jia - Chinese (Taiwan)',
                'Google 普通话（中国大陆）',
                'zh-CN'
            ];

            // 按优先级查找
            for (const preference of chineseVoicePreferences) {
                bestVoice = voices.find(voice =>
                    voice.name.includes(preference) ||
                    voice.name.toLowerCase().includes('xiaoxiao') ||
                    voice.name.toLowerCase().includes('yaoyao') ||
                    (voice.lang === 'zh-CN' && voice.name.toLowerCase().includes('female'))
                );
                if (bestVoice) break;
            }

            // 如果没找到特定的，找任何中文女声
            if (!bestVoice) {
                bestVoice = voices.find(voice =>
                    voice.lang.startsWith('zh') &&
                    (voice.name.toLowerCase().includes('female') ||
                     voice.name.toLowerCase().includes('woman') ||
                     !voice.name.toLowerCase().includes('male'))
                );
            }

        } else if (language === 'en-US') {
            // 英文语音优先级（尽量匹配AriaNeural的效果）
            const englishVoicePreferences = [
                'Microsoft Aria - English (United States)',
                'Microsoft Zira - English (United States)',
                'Microsoft Eva - English (United States)',
                'Samantha - English (United States)',
                'Alex - English (United States)',
                'Google US English',
                'en-US'
            ];

            // 按优先级查找
            for (const preference of englishVoicePreferences) {
                bestVoice = voices.find(voice =>
                    voice.name.includes(preference) ||
                    voice.name.toLowerCase().includes('aria') ||
                    voice.name.toLowerCase().includes('zira') ||
                    (voice.lang === 'en-US' && voice.name.toLowerCase().includes('female'))
                );
                if (bestVoice) break;
            }

            // 如果没找到特定的，找任何英文女声
            if (!bestVoice) {
                bestVoice = voices.find(voice =>
                    voice.lang.startsWith('en') &&
                    (voice.name.toLowerCase().includes('female') ||
                     voice.name.toLowerCase().includes('woman') ||
                     voice.name.toLowerCase().includes('samantha') ||
                     !voice.name.toLowerCase().includes('male'))
                );
            }
        }

        // 如果找到了最佳语音，设置它
        if (bestVoice) {
            utterance.voice = bestVoice;
            console.log(`🎤 选择语音: ${bestVoice.name} (${bestVoice.lang})`);
        } else {
            console.log(`⚠️ 未找到合适的${language}语音，使用默认语音`);
        }
    }

    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        let roleText = '';
        switch (role) {
            case 'user':
                roleText = '您';
                break;
            case 'assistant':
                roleText = 'AI助手';
                break;
            case 'system':
                roleText = '系统';
                break;
        }

        messageDiv.innerHTML = `
            <div class="message-content">
                <strong>${roleText}:</strong><br>
                ${content.replace(/\n/g, '<br>')}
            </div>
            <div class="message-time">${timeString}</div>
        `;

        this.elements.messagesContainer.appendChild(messageDiv);
        this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
    }

    showLoading(show) {
        if (show) {
            this.elements.loadingIndicator.classList.add('show');
        } else {
            this.elements.loadingIndicator.classList.remove('show');
        }
    }

    updateVoiceButton(isRecording) {
        if (isRecording) {
            this.elements.voiceBtn.classList.add('recording');
            this.elements.voiceBtn.textContent = '🔴';
            this.elements.voiceBtn.title = '点击停止录音';
        } else {
            this.elements.voiceBtn.classList.remove('recording');
            this.elements.voiceBtn.textContent = '🎤';
            this.elements.voiceBtn.title = '语音输入';
        }
    }

    updateConnectionStatus(message, isConnected) {
        this.elements.connectionStatus.textContent = message;
        this.elements.connectionStatus.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
    }

    updateAvatarStatus(status) {
        this.elements.status.textContent = status;
    }

    destroy() {
        // 清理资源
        if (this.websocket) {
            this.websocket.close();
        }
        if (this.recognition) {
            this.recognition.stop();
        }
        if (this.synthesis) {
            this.synthesis.cancel();
        }
        if (this.avatar) {
            this.avatar.destroy();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 初始化Web RAG客户端...');
    window.webRAGClient = new WebRAGClient();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.webRAGClient) {
        window.webRAGClient.destroy();
    }
});
