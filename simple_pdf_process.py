#!/usr/bin/env python3
"""
最简化的PDF处理
"""

import asyncio
import sys
import os

# 确保在正确目录
os.chdir('/data/RAG-Anything')
sys.path.insert(0, '/data/RAG-Anything')

async def simple_process():
    print("🚀 简化PDF处理")
    
    # 1. 提取PDF文本
    try:
        import PyPDF2
        
        pdf_path = '/data/RAG-Anything/sample_questions.pdf'
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = ""
            
            for i, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text.strip():
                    text_content += f"\n=== 第{i+1}页 ===\n{page_text}\n"
            
            print(f"✅ 提取了 {len(text_content)} 字符")
            print(f"内容预览:\n{text_content[:500]}...")
            
    except ImportError:
        print("❌ 需要安装PyPDF2: pip install PyPDF2")
        return
    except Exception as e:
        print(f"❌ PDF提取失败: {e}")
        return
    
    # 2. 创建LightRAG
    try:
        from lightrag import LightRAG
        from lightrag.llm.openai import openai_complete_if_cache, openai_embed
        from lightrag.utils import EmbeddingFunc
        
        # API配置
        api_key = 'sk-e182b143987f48a385e70370515db60a'
        base_url = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
        
        async def llm_func(prompt, **kwargs):
            return await openai_complete_if_cache(
                'qwen-turbo', prompt, api_key=api_key, base_url=base_url, **kwargs
            )
        
        async def embed_func(texts):
            if isinstance(texts, str):
                texts = [texts]
            return await openai_embed(
                texts, model='text-embedding-v1', api_key=api_key, base_url=base_url
            )
        
        embedding_func = EmbeddingFunc(
            embedding_dim=1536, max_token_size=8192, func=embed_func
        )
        
        rag = LightRAG(
            working_dir='./simple_rag',
            llm_model_func=llm_func,
            embedding_func=embedding_func
        )
        
        print("✅ LightRAG创建成功")
        
    except Exception as e:
        print(f"❌ LightRAG创建失败: {e}")
        return
    
    # 3. 插入内容
    try:
        await rag.ainsert(text_content)
        print("✅ 内容插入成功")
    except Exception as e:
        print(f"❌ 内容插入失败: {e}")
        return
    
    # 4. 测试查询
    test_queries = [
        "xx银行的盈利增长因素是什么？",
        "文档的主要内容是什么？",
        "资产质量控制有什么措施？"
    ]
    
    print("\n🔍 测试查询:")
    for query in test_queries:
        try:
            result = await rag.aquery(query)
            print(f"\n问: {query}")
            print(f"答: {result}")
        except Exception as e:
            print(f"\n问: {query}")
            print(f"答: 查询失败 - {e}")
    
    # 5. 交互式问答
    print("\n🎯 交互式问答 (输入 'quit' 退出)")
    while True:
        try:
            user_query = input("\n请输入问题: ")
            if user_query.lower() in ['quit', 'exit', '退出']:
                break
            if user_query.strip():
                result = await rag.aquery(user_query)
                print(f"回答: {result}")
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"查询失败: {e}")

if __name__ == "__main__":
    asyncio.run(simple_process())
