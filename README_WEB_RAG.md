# 🤖 RAG数字人Web系统 - macOS优化版

基于您现有的RAG-Anything系统，打造的完整Web数字人交互界面。

## ✨ 功能特性

### 🎯 核心功能
- **智能问答** - 基于RAG知识库的精准回答
- **语音交互** - 支持语音输入和语音输出
- **数字人动画** - 实时嘴形同步和表情动画
- **Web界面** - 现代化的响应式Web界面
- **中英文支持** - 自动语言识别和适配

### 🎨 数字人特性
- **Canvas 2D渲染** - 针对macOS Retina屏幕优化
- **实时动画** - 待机、听取、思考、说话状态
- **嘴形同步** - 基于语音的实时嘴部动画
- **表情系统** - 眨眼、头部摆动等自然动作

### 🌐 Web技术
- **WebSocket通信** - 实时双向通信
- **语音识别** - 浏览器原生Speech Recognition
- **语音合成** - 浏览器原生Text-to-Speech
- **响应式设计** - 支持桌面和移动设备

## 📁 文件结构

```
RAG-Anything/
├── web_interface.html      # 主Web界面
├── avatar_engine.js        # 数字人动画引擎
├── web_client.js          # Web客户端逻辑
├── web_rag_server.py      # Web服务器
├── start_web_rag.py       # 一键启动脚本
├── simple_voice_rag.py    # 现有的RAG系统
├── raganything_example.py # 原始RAG示例
└── rag_storage_server/    # RAG知识库存储
```

## 🚀 快速开始

### 1. 环境准备

确保您已经设置好基础RAG系统：

```bash
# 1. 设置API密钥
export DASHSCOPE_API_KEY="your_api_key_here"

# 2. 安装Web依赖
pip install fastapi uvicorn websockets

# 3. 确保RAG系统可用
python simple_voice_rag.py  # 测试基础功能
```

### 2. 一键启动

```bash
# 使用启动脚本（推荐）
python start_web_rag.py

# 或手动启动服务器
python web_rag_server.py
```

### 3. 访问系统

- 🌐 **Web界面**: http://localhost:8000
- 🔍 **健康检查**: http://localhost:8000/health
- 📊 **系统状态**: 查看连接状态和RAG系统状态

## 🎮 使用指南

### 💬 文字交互
1. 在输入框中输入问题
2. 点击"发送"按钮或按回车键
3. 观看数字人动画并听取语音回答

### 🎤 语音交互
1. 点击🎤按钮开始语音输入
2. 说出您的问题（支持中英文）
3. 系统自动识别并处理
4. 数字人会语音回答并同步动画

### 🤖 数字人状态
- **待机中** - 绿色状态，等待输入
- **正在听取** - 蓝色状态，语音识别中
- **正在思考** - 黄色状态，处理查询中
- **正在回答** - 红色状态，语音播放中

## 🔧 技术架构

### 前端技术栈
```javascript
// 核心技术
- HTML5 Canvas 2D
- Web Speech API
- WebSocket API
- CSS3 动画

// 数字人引擎
- MacOSAvatarEngine (Canvas渲染)
- 实时动画系统
- 语音同步算法
```

### 后端技术栈
```python
# 核心框架
- FastAPI (Web框架)
- WebSocket (实时通信)
- Uvicorn (ASGI服务器)

# RAG系统
- SimpleVoiceRAG (您的现有系统)
- LightRAG (检索增强)
- 阿里云百炼API (LLM)
```

## ⚙️ 配置选项

### 环境变量
```bash
# API配置
DASHSCOPE_API_KEY=your_key_here
OPENAI_API_KEY=alternative_key

# 服务器配置
WEB_HOST=0.0.0.0
WEB_PORT=8000
```

### 数字人配置
```javascript
// 在avatar_engine.js中可调整
- 动画速度
- 嘴形敏感度  
- 表情频率
- 渲染质量
```

## 🐛 故障排除

### 常见问题

**1. 语音功能不工作**
```
解决方案：
- 检查浏览器麦克风权限
- 使用HTTPS或localhost
- 确认浏览器支持Web Speech API
```

**2. 数字人动画卡顿**
```
解决方案：
- 检查浏览器硬件加速
- 降低Canvas分辨率
- 关闭其他占用GPU的应用
```

**3. RAG回答不准确**
```
解决方案：
- 检查API密钥配置
- 确认知识库已初始化
- 查看服务器日志错误信息
```

**4. WebSocket连接失败**
```
解决方案：
- 检查防火墙设置
- 确认端口8000未被占用
- 重启服务器
```

### 日志查看
```bash
# 查看服务器日志
python web_rag_server.py

# 查看浏览器控制台
F12 -> Console标签
```

## 🔄 系统更新

### 更新RAG系统
```bash
# 更新知识库
python raganything_example.py

# 重启Web服务
python start_web_rag.py
```

### 自定义数字人
```javascript
// 修改avatar_engine.js中的绘制函数
drawHead()    // 头部样式
drawEyes()    // 眼部样式  
drawMouth()   // 嘴部样式
drawDecorations() // 装饰元素
```

## 📊 性能优化

### macOS优化
- ✅ Retina屏幕适配
- ✅ 硬件加速渲染
- ✅ 低CPU占用动画
- ✅ 原生语音API集成

### 浏览器优化
- ✅ Canvas 2D高质量渲染
- ✅ WebSocket长连接复用
- ✅ 音频流优化
- ✅ 内存泄漏防护

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统！

### 开发环境
```bash
# 克隆项目
git clone your_repo_url

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python web_rag_server.py --reload
```

## 📄 许可证

本项目基于您现有的RAG-Anything系统构建，遵循相同的许可证条款。

## 🙏 致谢

- 基于您优秀的SimpleVoiceRAG系统
- 使用LightRAG和RAGAnything技术
- 感谢阿里云百炼API支持

---

🎉 **享受您的Web RAG数字人系统！** 🤖✨
