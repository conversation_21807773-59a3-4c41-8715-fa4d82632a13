<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-Anything 高德地图集成演示</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=e4203c466e89130a43daedd0ae9f4368&plugin=AMap.PlaceSearch,AMap.Driving,AMap.Geocoder"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            min-height: 700px;
        }

        .map-section {
            display: flex;
            flex-direction: column;
        }

        .map-container {
            width: 100%;
            height: 500px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            border: 3px solid #007AFF;
            overflow: hidden;
            background: #f0f0f0;
            position: relative;
        }

        #mapContainer {
            width: 100%;
            height: 100%;
        }

        .map-controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 15px;
            background: #007AFF;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: #34C759;
        }

        .status {
            margin-top: 15px;
            padding: 12px 20px;
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(52, 199, 89, 0.3);
        }

        .chat-section {
            display: flex;
            flex-direction: column;
            height: 640px;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .chat-header h1 {
            color: #1d1d1f;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 20px;
            margin-bottom: 20px;
            border: 2px solid #e5e5ea;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 20px;
            max-width: 85%;
            word-wrap: break-word;
            animation: messageSlideIn 0.4s ease-out;
        }

        .message.user {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 8px;
        }

        .message.assistant {
            background: linear-gradient(45deg, #E5E5EA, #F2F2F7);
            color: #1d1d1f;
            border-bottom-left-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .message.map {
            background: linear-gradient(45deg, #FFE082, #FFCC02);
            color: #E65100;
            border-bottom-left-radius: 8px;
            border-left: 4px solid #FF9500;
        }

        .input-section {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: 2px solid #e5e5ea;
        }

        .text-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        .btn-voice {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            min-width: 60px;
        }

        .btn-voice:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 199, 89, 0.3);
        }

        .btn-voice.recording {
            background: linear-gradient(45deg, #FF3B30, #FF6B35);
            animation: pulse 1.5s infinite;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 15px;
            color: #007AFF;
            font-weight: 600;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 15px;
            margin: 10px 0;
        }

        .loading.show {
            display: block;
            animation: loadingPulse 1.5s infinite;
        }

        .quick-queries {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .quick-query {
            padding: 8px 15px;
            background: linear-gradient(45deg, #E5E5EA, #F2F2F7);
            border: none;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-query:hover {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            transform: translateY(-2px);
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes loadingPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .map-container {
                height: 300px;
            }
            
            .chat-section {
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 地图展示区域 -->
        <div class="map-section">
            <div class="map-container">
                <div id="mapContainer"></div>
            </div>
            
            <div class="map-controls">
                <button class="control-btn" id="initMapBtn">
                    🗺️ 初始化地图
                </button>
                <button class="control-btn" id="locateBtn">
                    📍 定位
                </button>
                <button class="control-btn" id="routeBtn">
                    🛣️ 路线规划
                </button>
                <button class="control-btn" id="searchBtn">
                    🔍 POI搜索
                </button>
            </div>
            
            <div class="status" id="mapStatus">🗺️ 准备初始化高德地图</div>
        </div>

        <!-- 聊天交互区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>🗺️ 高德地图智能助手</h1>
                <p>支持路线规划、POI搜索、天气查询等功能</p>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>地图助手:</strong><br>
                        您好！我是集成了高德地图API的智能助手。<br><br>
                        🗺️ <strong>支持功能：</strong><br>
                        • 🛣️ 路线规划：从A到B怎么走<br>
                        • 📍 位置查询：某地在哪里<br>
                        • 🔍 POI搜索：附近的美食、酒店<br>
                        • 🌤️ 天气查询：北京天气怎么样<br>
                        • 📏 距离计算：两地距离多远<br><br>
                        请尝试问我地图相关问题！
                    </div>
                </div>
            </div>

            <div class="quick-queries">
                <button class="quick-query" onclick="sendQuickQuery('从北京到上海怎么走')">北京到上海路线</button>
                <button class="quick-query" onclick="sendQuickQuery('附近有什么好吃的')">附近美食</button>
                <button class="quick-query" onclick="sendQuickQuery('北京天气怎么样')">北京天气</button>
                <button class="quick-query" onclick="sendQuickQuery('天安门在哪里')">天安门位置</button>
                <button class="quick-query" onclick="sendQuickQuery('找个酒店')">找酒店</button>
            </div>

            <div class="loading" id="loadingIndicator">
                <span>🧠 AI正在处理中...</span>
            </div>

            <div class="input-section">
                <input 
                    type="text" 
                    id="textInput" 
                    class="text-input" 
                    placeholder="问我地图相关问题..."
                >
                <button id="voiceBtn" class="btn btn-voice" title="语音输入">
                    🎤
                </button>
                <button id="sendBtn" class="btn">发送</button>
            </div>
        </div>
    </div>

    <script>
        /**
         * 高德地图集成演示
         */
        class MapDemo {
            constructor() {
                this.map = null;
                this.websocket = null;
                this.recognition = null;
                this.isListening = false;

                // DOM元素
                this.elements = {
                    mapContainer: document.getElementById('mapContainer'),
                    mapStatus: document.getElementById('mapStatus'),
                    initMapBtn: document.getElementById('initMapBtn'),
                    locateBtn: document.getElementById('locateBtn'),
                    routeBtn: document.getElementById('routeBtn'),
                    searchBtn: document.getElementById('searchBtn'),
                    messagesContainer: document.getElementById('messagesContainer'),
                    textInput: document.getElementById('textInput'),
                    voiceBtn: document.getElementById('voiceBtn'),
                    sendBtn: document.getElementById('sendBtn'),
                    loadingIndicator: document.getElementById('loadingIndicator')
                };

                this.initialize();
            }

            initialize() {
                console.log('🗺️ 初始化地图演示系统...');

                // 设置事件监听器
                this.setupEventListeners();

                // 初始化WebSocket
                this.initializeWebSocket();

                // 初始化语音识别
                this.initializeSpeechRecognition();

                this.updateMapStatus('✅ 系统已就绪，点击"初始化地图"开始');
            }

            setupEventListeners() {
                // 地图控制按钮
                this.elements.initMapBtn.addEventListener('click', () => {
                    this.initializeMap();
                });

                this.elements.locateBtn.addEventListener('click', () => {
                    this.getCurrentLocation();
                });

                this.elements.routeBtn.addEventListener('click', () => {
                    this.planRoute();
                });

                this.elements.searchBtn.addEventListener('click', () => {
                    this.searchPOI();
                });

                // 聊天功能
                this.elements.voiceBtn.addEventListener('click', () => {
                    this.toggleVoiceInput();
                });

                this.elements.sendBtn.addEventListener('click', () => {
                    const text = this.elements.textInput.value.trim();
                    if (text) {
                        this.sendQuery(text);
                        this.elements.textInput.value = '';
                    }
                });

                this.elements.textInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.elements.sendBtn.click();
                    }
                });
            }

            initializeMap() {
                try {
                    this.updateMapStatus('🗺️ 正在初始化高德地图...');

                    // 检查高德地图API是否加载
                    if (typeof AMap === 'undefined') {
                        this.updateMapStatus('❌ 高德地图API未加载，请检查网络连接');
                        return;
                    }

                    // 创建地图实例
                    this.map = new AMap.Map('mapContainer', {
                        zoom: 11,
                        center: [116.397428, 39.90923], // 北京天安门
                        mapStyle: 'amap://styles/normal'
                    });

                    // 添加控件
                    this.map.addControl(new AMap.Scale());
                    this.map.addControl(new AMap.ToolBar());

                    this.updateMapStatus('✅ 高德地图初始化成功');
                    this.elements.initMapBtn.classList.add('active');

                    console.log('✅ 高德地图初始化成功');

                } catch (error) {
                    console.error('❌ 地图初始化失败:', error);
                    this.updateMapStatus('❌ 地图初始化失败: ' + error.message);
                }
            }

            getCurrentLocation() {
                if (!this.map) {
                    alert('请先初始化地图');
                    return;
                }

                this.updateMapStatus('📍 正在获取当前位置...');

                this.map.plugin('AMap.Geolocation', () => {
                    const geolocation = new AMap.Geolocation({
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0,
                        convert: true,
                        showButton: true,
                        buttonPosition: 'LB',
                        showMarker: true,
                        showCircle: true,
                        panToLocation: true,
                        zoomToAccuracy: true
                    });

                    geolocation.getCurrentPosition((status, result) => {
                        if (status === 'complete') {
                            this.updateMapStatus(`📍 定位成功: ${result.formattedAddress}`);
                            console.log('定位成功:', result);
                        } else {
                            this.updateMapStatus('❌ 定位失败: ' + result.message);
                            console.error('定位失败:', result);
                        }
                    });

                    this.map.addControl(geolocation);
                });
            }

            planRoute() {
                if (!this.map) {
                    alert('请先初始化地图');
                    return;
                }

                // 示例路线规划
                const origin = [116.379028, 39.865042]; // 起点
                const destination = [116.427281, 39.903719]; // 终点

                this.updateMapStatus('🛣️ 正在规划路线...');

                this.map.plugin('AMap.Driving', () => {
                    const driving = new AMap.Driving({
                        map: this.map,
                        showTraffic: false,
                        hideMarkers: false
                    });

                    driving.search(origin, destination, (status, result) => {
                        if (status === 'complete') {
                            this.updateMapStatus('✅ 路线规划成功');
                            console.log('路线规划结果:', result);
                        } else {
                            this.updateMapStatus('❌ 路线规划失败');
                            console.error('路线规划失败:', result);
                        }
                    });
                });
            }

            searchPOI() {
                if (!this.map) {
                    alert('请先初始化地图');
                    return;
                }

                this.updateMapStatus('🔍 正在搜索POI...');

                this.map.plugin('AMap.PlaceSearch', () => {
                    const placeSearch = new AMap.PlaceSearch({
                        pageSize: 10,
                        pageIndex: 1,
                        city: '010',
                        map: this.map,
                        panel: false
                    });

                    placeSearch.search('美食', (status, result) => {
                        if (status === 'complete') {
                            this.updateMapStatus(`✅ 找到${result.poiList.pois.length}个POI`);
                            console.log('POI搜索结果:', result);
                        } else {
                            this.updateMapStatus('❌ POI搜索失败');
                            console.error('POI搜索失败:', result);
                        }
                    });
                });
            }

            initializeWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;

                this.websocket = new WebSocket(wsUrl);

                this.websocket.onopen = () => {
                    console.log('✅ WebSocket连接成功');
                };

                this.websocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleServerMessage(data);
                    } catch (error) {
                        console.error('❌ 消息解析失败:', error);
                    }
                };

                this.websocket.onerror = (error) => {
                    console.error('❌ WebSocket错误:', error);
                };

                this.websocket.onclose = () => {
                    console.log('🔌 WebSocket连接关闭');
                };
            }

            initializeSpeechRecognition() {
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();

                    this.recognition.continuous = false;
                    this.recognition.interimResults = false;
                    this.recognition.lang = 'zh-CN';

                    this.recognition.onstart = () => {
                        this.isListening = true;
                        this.updateVoiceButton(true);
                        this.updateMapStatus('👂 正在听取您的问题...');
                    };

                    this.recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        this.elements.textInput.value = transcript;
                        setTimeout(() => {
                            this.sendQuery(transcript);
                        }, 500);
                    };

                    this.recognition.onend = () => {
                        this.isListening = false;
                        this.updateVoiceButton(false);
                        this.updateMapStatus('✅ 高德地图初始化成功');
                    };

                    this.recognition.onerror = (event) => {
                        console.error('❌ 语音识别错误:', event.error);
                        this.isListening = false;
                        this.updateVoiceButton(false);
                        this.updateMapStatus('❌ 语音识别失败: ' + event.error);
                    };
                }
            }

            toggleVoiceInput() {
                if (!this.recognition) {
                    alert('您的浏览器不支持语音识别功能');
                    return;
                }

                if (this.isListening) {
                    this.recognition.stop();
                } else {
                    try {
                        this.recognition.start();
                    } catch (error) {
                        console.error('❌ 启动语音识别失败:', error);
                        alert('启动语音识别失败，请检查麦克风权限');
                    }
                }
            }

            updateVoiceButton(isRecording) {
                if (isRecording) {
                    this.elements.voiceBtn.classList.add('recording');
                    this.elements.voiceBtn.innerHTML = '🔴 停止';
                } else {
                    this.elements.voiceBtn.classList.remove('recording');
                    this.elements.voiceBtn.innerHTML = '🎤';
                }
            }

            sendQuery(text) {
                if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                    alert('未连接到服务器');
                    return;
                }

                this.addMessage('user', text);
                this.showLoading(true);

                const message = {
                    type: 'query',
                    text: text.trim(),
                    timestamp: Date.now()
                };

                this.websocket.send(JSON.stringify(message));
            }

            handleServerMessage(data) {
                if (data.type === 'response') {
                    this.showLoading(false);

                    // 根据响应来源选择消息类型
                    const messageType = data.source === 'map' ? 'map' : 'assistant';
                    this.addMessage(messageType, data.text);

                    // 如果是地图响应，可以在地图上显示相关信息
                    if (data.source === 'map' && this.map) {
                        this.handleMapResponse(data.text);
                    }
                }
            }

            handleMapResponse(responseText) {
                // 根据响应内容在地图上显示相关信息
                // 这里可以添加更多地图交互逻辑
                console.log('地图响应:', responseText);
            }

            addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;

                let roleText = '';
                switch(role) {
                    case 'user':
                        roleText = '您';
                        break;
                    case 'map':
                        roleText = '地图助手';
                        break;
                    default:
                        roleText = 'AI助手';
                }

                messageDiv.innerHTML = `
                    <div class="message-content">
                        <strong>${roleText}:</strong><br>
                        ${content.replace(/\n/g, '<br>')}
                    </div>
                `;

                this.elements.messagesContainer.appendChild(messageDiv);
                this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
            }

            showLoading(show) {
                if (show) {
                    this.elements.loadingIndicator.classList.add('show');
                } else {
                    this.elements.loadingIndicator.classList.remove('show');
                }
            }

            updateMapStatus(message) {
                this.elements.mapStatus.textContent = message;
            }
        }

        // 快速查询函数
        function sendQuickQuery(query) {
            const textInput = document.getElementById('textInput');
            textInput.value = query;
            document.getElementById('sendBtn').click();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🗺️ 初始化高德地图演示系统...');
            window.mapDemo = new MapDemo();
        });
    </script>
</body>
</html>
