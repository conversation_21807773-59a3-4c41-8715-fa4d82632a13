/**
 * 真实美女人脸模型管理器
 * 集成高质量的真实人脸3D模型
 */

class RealisticFaceManager {
    constructor() {
        this.currentFaceIndex = 0;
        this.faces = [
            {
                name: "艾莉亚",
                description: "欧美混血美女，深邃眼眸",
                url: null, // 直接使用程序化生成
                fallback: "procedural"
            },
            {
                name: "小雅",
                description: "东方古典美女，温婉气质",
                url: null, // 直接使用程序化生成
                fallback: "procedural"
            },
            {
                name: "莉娜",
                description: "现代时尚美女，活力四射",
                url: null, // 直接使用程序化生成
                fallback: "procedural"
            },
            {
                name: "索菲亚",
                description: "知性优雅美女，成熟魅力",
                url: null, // 直接使用程序化生成
                fallback: "procedural"
            }
        ];
        
        this.textureLoader = new THREE.TextureLoader();
        this.gltfLoader = new THREE.GLTFLoader();
    }

    async loadRealisticFace(index = 0) {
        const face = this.faces[index];
        console.log(`🌟 创建程序化真实美女: ${face.name} - ${face.description}`);

        // 直接创建程序化美女，确保稳定显示
        return this.createRealisticFallback(face);
    }

    loadGLTF(url) {
        return new Promise((resolve, reject) => {
            this.gltfLoader.load(
                url,
                (gltf) => resolve(gltf),
                (progress) => {
                    const percent = (progress.loaded / progress.total * 100).toFixed(1);
                    console.log(`📊 加载进度: ${percent}%`);
                },
                (error) => reject(error)
            );
        });
    }

    setupRealisticAvatar(gltf, faceInfo) {
        const avatar = gltf.scene;
        
        // 优化材质，增强真实感
        avatar.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                
                if (child.material) {
                    // 增强皮肤材质
                    if (child.material.name && child.material.name.toLowerCase().includes('skin')) {
                        this.enhanceSkinMaterial(child.material);
                    }
                    
                    // 增强眼睛材质
                    if (child.material.name && child.material.name.toLowerCase().includes('eye')) {
                        this.enhanceEyeMaterial(child.material);
                    }
                    
                    // 增强头发材质
                    if (child.material.name && child.material.name.toLowerCase().includes('hair')) {
                        this.enhanceHairMaterial(child.material);
                    }
                }
            }
        });

        // 设置最佳位置和缩放
        avatar.position.set(0, -1.8, 0);
        avatar.scale.set(1.8, 1.8, 1.8);
        
        console.log(`✨ ${faceInfo.name}已准备就绪`);
        return {
            avatar: avatar,
            animations: gltf.animations,
            faceInfo: faceInfo
        };
    }

    enhanceSkinMaterial(material) {
        // 增强皮肤材质的真实感
        if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
            material.roughness = 0.6;
            material.metalness = 0.0;
            
            // 添加次表面散射效果
            if (material.isMeshPhysicalMaterial) {
                material.transmission = 0.1;
                material.thickness = 0.5;
            }
            
            // 增强法线贴图
            if (material.normalMap) {
                material.normalScale.set(0.3, 0.3);
            }
        }
    }

    enhanceEyeMaterial(material) {
        // 增强眼睛材质
        if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
            material.roughness = 0.1;
            material.metalness = 0.0;
            material.envMapIntensity = 1.5;
        }
    }

    enhanceHairMaterial(material) {
        // 增强头发材质
        if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
            material.roughness = 0.8;
            material.metalness = 0.1;
            
            // 增强各向异性效果
            if (material.isMeshPhysicalMaterial) {
                material.anisotropy = 0.8;
            }
        }
    }

    createRealisticFallback(faceInfo) {
        console.log(`🎨 创建程序化真实美女: ${faceInfo.name}`);

        const group = new THREE.Group();

        // 确保THREE.js已加载
        if (typeof THREE === 'undefined') {
            console.error('❌ THREE.js未加载');
            return null;
        }

        // 创建更真实的头部
        const headGeometry = new THREE.SphereGeometry(0.4, 64, 64);
        
        // 创建皮肤材质
        const skinMaterial = new THREE.MeshPhysicalMaterial({
            color: 0xffdbac,
            roughness: 0.6,
            metalness: 0.0,
            transmission: 0.05,
            thickness: 0.5,
            clearcoat: 0.1,
            clearcoatRoughness: 0.3
        });
        
        const head = new THREE.Mesh(headGeometry, skinMaterial);
        head.position.y = 0.2;
        head.castShadow = true;
        head.receiveShadow = true;
        group.add(head);

        // 创建更真实的眼睛
        this.createRealisticEyes(group);
        
        // 创建更真实的嘴唇
        this.createRealisticLips(group);
        
        // 创建眉毛
        this.createEyebrows(group);
        
        // 创建鼻子
        this.createNose(group);
        
        // 创建头发
        this.createHair(group, faceInfo);

        // 应用统一的位置和缩放
        group.position.set(0, -1.8, 0);
        group.scale.set(1.8, 1.8, 1.8);

        return {
            avatar: group,
            animations: [],
            faceInfo: faceInfo,
            isProceduralFace: true
        };
    }

    createRealisticEyes(group) {
        // 眼球
        const eyeballGeometry = new THREE.SphereGeometry(0.05, 32, 32);
        const eyeballMaterial = new THREE.MeshPhysicalMaterial({
            color: 0xffffff,
            roughness: 0.1,
            metalness: 0.0,
            transmission: 0.9,
            thickness: 0.1
        });

        // 虹膜
        const irisGeometry = new THREE.CircleGeometry(0.025, 32);
        const irisMaterial = new THREE.MeshStandardMaterial({
            color: 0x4a90e2, // 蓝色眼睛
            roughness: 0.3,
            metalness: 0.0
        });

        // 瞳孔
        const pupilGeometry = new THREE.CircleGeometry(0.015, 32);
        const pupilMaterial = new THREE.MeshBasicMaterial({
            color: 0x000000
        });

        // 左眼
        const leftEyeball = new THREE.Mesh(eyeballGeometry, eyeballMaterial);
        leftEyeball.position.set(-0.1, 0.25, 0.32);
        group.add(leftEyeball);

        const leftIris = new THREE.Mesh(irisGeometry, irisMaterial);
        leftIris.position.set(-0.1, 0.25, 0.37);
        group.add(leftIris);

        const leftPupil = new THREE.Mesh(pupilGeometry, pupilMaterial);
        leftPupil.position.set(-0.1, 0.25, 0.371);
        group.add(leftPupil);

        // 右眼
        const rightEyeball = new THREE.Mesh(eyeballGeometry, eyeballMaterial);
        rightEyeball.position.set(0.1, 0.25, 0.32);
        group.add(rightEyeball);

        const rightIris = new THREE.Mesh(irisGeometry, irisMaterial);
        rightIris.position.set(0.1, 0.25, 0.37);
        group.add(rightIris);

        const rightPupil = new THREE.Mesh(pupilGeometry, pupilMaterial);
        rightPupil.position.set(0.1, 0.25, 0.371);
        group.add(rightPupil);

        // 保存眼部引用用于动画
        group.userData.eyes = {
            leftEyeball, rightEyeball,
            leftIris, rightIris,
            leftPupil, rightPupil
        };
    }

    createRealisticLips(group) {
        // 上唇
        const upperLipGeometry = new THREE.SphereGeometry(0.03, 16, 8);
        const lipMaterial = new THREE.MeshPhysicalMaterial({
            color: 0xd4756b, // 自然唇色
            roughness: 0.4,
            metalness: 0.0,
            clearcoat: 0.3,
            clearcoatRoughness: 0.1
        });

        const upperLip = new THREE.Mesh(upperLipGeometry, lipMaterial);
        upperLip.position.set(0, 0.12, 0.35);
        upperLip.scale.set(2.5, 0.8, 1);
        group.add(upperLip);

        // 下唇
        const lowerLip = new THREE.Mesh(upperLipGeometry, lipMaterial);
        lowerLip.position.set(0, 0.08, 0.35);
        lowerLip.scale.set(2.5, 1.2, 1);
        group.add(lowerLip);

        // 保存嘴部引用用于动画
        group.userData.mouth = { upperLip, lowerLip };
    }

    createEyebrows(group) {
        const browGeometry = new THREE.BoxGeometry(0.08, 0.01, 0.02);
        const browMaterial = new THREE.MeshStandardMaterial({
            color: 0x4a3728, // 棕色眉毛
            roughness: 0.9
        });

        // 左眉
        const leftBrow = new THREE.Mesh(browGeometry, browMaterial);
        leftBrow.position.set(-0.08, 0.32, 0.35);
        leftBrow.rotation.z = 0.1;
        group.add(leftBrow);

        // 右眉
        const rightBrow = new THREE.Mesh(browGeometry, browMaterial);
        rightBrow.position.set(0.08, 0.32, 0.35);
        rightBrow.rotation.z = -0.1;
        group.add(rightBrow);
    }

    createNose(group) {
        const noseGeometry = new THREE.ConeGeometry(0.02, 0.06, 8);
        const noseMaterial = new THREE.MeshPhysicalMaterial({
            color: 0xffdbac,
            roughness: 0.6,
            metalness: 0.0
        });

        const nose = new THREE.Mesh(noseGeometry, noseMaterial);
        nose.position.set(0, 0.18, 0.36);
        nose.rotation.x = Math.PI;
        group.add(nose);
    }

    createHair(group, faceInfo) {
        // 根据不同角色创建不同发型
        const hairGeometry = new THREE.SphereGeometry(0.45, 32, 32);
        
        let hairColor = 0x4a3728; // 默认棕色
        switch(faceInfo.name) {
            case "艾莉亚":
                hairColor = 0x8b4513; // 棕色
                break;
            case "小雅":
                hairColor = 0x000000; // 黑色
                break;
            case "莉娜":
                hairColor = 0xdaa520; // 金色
                break;
            case "索菲亚":
                hairColor = 0x654321; // 深棕色
                break;
        }

        const hairMaterial = new THREE.MeshPhysicalMaterial({
            color: hairColor,
            roughness: 0.8,
            metalness: 0.1,
            anisotropy: 0.8
        });

        const hair = new THREE.Mesh(hairGeometry, hairMaterial);
        hair.position.set(0, 0.25, -0.05);
        hair.scale.set(1, 1.2, 0.8);
        group.add(hair);
    }

    getNextFace() {
        this.currentFaceIndex = (this.currentFaceIndex + 1) % this.faces.length;
        return this.currentFaceIndex;
    }

    getCurrentFaceInfo() {
        return this.faces[this.currentFaceIndex];
    }

    getAllFaces() {
        return this.faces;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealisticFaceManager;
}
