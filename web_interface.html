<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG数字人 - macOS优化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            min-height: 600px;
        }

        .avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .avatar-canvas {
            width: 400px;
            height: 400px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 3px solid #007AFF;
        }

        .avatar-status {
            margin-top: 15px;
            padding: 8px 16px;
            background: #34C759;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .chat-section {
            display: flex;
            flex-direction: column;
            height: 560px;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .chat-header h1 {
            color: #1d1d1f;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .chat-header p {
            color: #86868b;
            font-size: 16px;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 15px;
            margin-bottom: 15px;
            border: 1px solid #e5e5ea;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 85%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-in;
        }

        .message.user {
            background: #007AFF;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }

        .message.assistant {
            background: #E5E5EA;
            color: #1d1d1f;
            border-bottom-left-radius: 4px;
        }

        .message-content {
            line-height: 1.4;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 4px;
        }

        .input-section {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .text-input {
            flex: 1;
            padding: 14px 18px;
            border: 2px solid #E5E5EA;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
            background: white;
        }

        .text-input:focus {
            border-color: #007AFF;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .btn {
            padding: 14px 20px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: #007AFF;
            color: white;
        }

        .btn-primary:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        .btn-voice {
            background: #34C759;
            color: white;
        }

        .btn-voice:hover {
            background: #28A745;
        }

        .btn-voice.recording {
            background: #FF3B30;
            animation: pulse 1s infinite;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
        }

        .connection-status.connected {
            background: #34C759;
            color: white;
        }

        .connection-status.disconnected {
            background: #FF3B30;
            color: white;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #86868b;
        }

        .loading.show {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 15px;
            }
            
            .avatar-canvas {
                width: 300px;
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">连接中...</div>
    
    <div class="container">
        <!-- 数字人区域 -->
        <div class="avatar-section">
            <canvas id="avatarCanvas" class="avatar-canvas" width="400" height="400"></canvas>
            <div class="avatar-status" id="avatarStatus">待机中</div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>🤖 RAG智能助手</h1>
                <p>基于知识库的智能问答系统</p>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>AI助手:</strong><br>
                        您好！我是基于RAG技术的智能助手，可以回答您关于知识库的各种问题。您可以通过文字或语音与我交流。
                    </div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>

            <div class="loading" id="loadingIndicator">
                <span>🤔 正在思考中...</span>
            </div>

            <div class="input-section">
                <input 
                    type="text" 
                    id="textInput" 
                    class="text-input" 
                    placeholder="输入您的问题..."
                    maxlength="500"
                >
                <button id="voiceBtn" class="btn btn-voice" title="语音输入">
                    🎤
                </button>
                <button id="sendBtn" class="btn btn-primary" title="发送">
                    发送
                </button>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript模块 -->
    <script src="avatar_engine.js"></script>
    <script src="web_client.js"></script>

    <script>
        // 设置欢迎消息时间
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();
    </script>
</body>
</html>
