# RAGAnything 服务器部署指南

## 🚀 快速部署脚本

### 1. 系统环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y python3 python3-pip python3-venv git curl wget

# 安装系统依赖
sudo apt install -y build-essential libssl-dev libffi-dev python3-dev
sudo apt install -y libjpeg-dev zlib1g-dev libpng-dev
sudo apt install -y poppler-utils tesseract-ocr

# 检查Python版本
python3 --version
```

### 2. 创建项目环境

```bash
# 创建项目目录
mkdir -p /opt/raganything
cd /opt/raganything

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip setuptools wheel
```

### 3. 克隆项目代码

```bash
# 克隆项目 (假设您有Git仓库)
git clone https://github.com/your-repo/RAG-Anything.git
cd RAG-Anything

# 或者上传本地代码
# scp -P 54953 -r ./RAG-Anything <EMAIL>:/opt/raganything/
```

### 4. 安装Python依赖

```bash
# 激活虚拟环境
source /opt/raganything/venv/bin/activate

# 安装核心依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 安装项目依赖
pip install -r requirements.txt

# 手动安装关键包
pip install openai dashscope requests python-dotenv
pip install magic-pdf pymupdf pillow numpy pandas networkx
pip install fastapi uvicorn streamlit
pip install faiss-cpu  # 或 faiss-gpu 如果有GPU
```

### 5. 配置环境变量

```bash
# 创建环境配置文件
cat > /opt/raganything/RAG-Anything/.env << EOF
# API配置
DASHSCOPE_API_KEY=sk-75e6425669944a9385ec758a64f009d2
OPENAI_API_KEY=sk-75e6425669944a9385ec758a64f009d2
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 路径配置
WORKING_DIR=/opt/raganything/data
OUTPUT_DIR=/opt/raganything/output

# 服务配置
HOST=0.0.0.0
PORT=8000
EOF

# 设置权限
chmod 600 /opt/raganything/RAG-Anything/.env
```

### 6. 创建数据目录

```bash
# 创建必要目录
mkdir -p /opt/raganything/data
mkdir -p /opt/raganything/output
mkdir -p /opt/raganything/logs
mkdir -p /opt/raganything/uploads

# 设置权限
chown -R $USER:$USER /opt/raganything/
chmod -R 755 /opt/raganything/
```

### 7. 测试安装

```bash
cd /opt/raganything/RAG-Anything
source /opt/raganything/venv/bin/activate

# 测试基础功能
python -c "
import torch
import openai
import dashscope
print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
"

# 测试API连接
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
api_key = os.getenv('DASHSCOPE_API_KEY')
print(f'✅ API密钥配置: {api_key[:8]}...{api_key[-4:]}')
"
```

## 🌐 Web服务部署

### 创建Web服务脚本

```bash
cat > /opt/raganything/RAG-Anything/web_server.py << 'EOF'
#!/usr/bin/env python3
"""
RAGAnything Web服务
"""

import os
import asyncio
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = FastAPI(title="RAGAnything API", version="1.0.0")

# 添加CORS支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "RAGAnything API Server", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "raganything"}

@app.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """上传文档并处理"""
    try:
        # 保存上传的文件
        upload_dir = "/opt/raganything/uploads"
        file_path = os.path.join(upload_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        return {"message": "文件上传成功", "file_path": file_path}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query")
async def query_document(query: str, working_dir: str = None):
    """查询文档"""
    try:
        # 这里集成您的RAGAnything查询逻辑
        response = f"模拟回答: {query}"
        return {"query": query, "response": response}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    
    uvicorn.run(
        "web_server:app",
        host=host,
        port=port,
        reload=False,
        workers=1
    )
EOF

chmod +x /opt/raganything/RAG-Anything/web_server.py
```

### 创建系统服务

```bash
cat > /etc/systemd/system/raganything.service << EOF
[Unit]
Description=RAGAnything Web Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/raganything/RAG-Anything
Environment=PATH=/opt/raganything/venv/bin
ExecStart=/opt/raganything/venv/bin/python web_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl daemon-reload
systemctl enable raganything
systemctl start raganything
systemctl status raganything
```

## 🔧 配置Nginx反向代理

```bash
# 安装Nginx
sudo apt install -y nginx

# 创建配置文件
cat > /etc/nginx/sites-available/raganything << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 启用站点
ln -s /etc/nginx/sites-available/raganything /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

## 📊 监控和日志

```bash
# 查看服务状态
systemctl status raganything

# 查看日志
journalctl -u raganything -f

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## 🔒 安全配置

```bash
# 配置防火墙
ufw allow ssh
ufw allow 80
ufw allow 443
ufw enable

# 设置文件权限
chmod 600 /opt/raganything/RAG-Anything/.env
chown -R root:root /opt/raganything/
```

## 🚀 启动服务

```bash
# 启动所有服务
systemctl start raganything
systemctl start nginx

# 检查服务状态
curl http://localhost:8000/health
```

## 📝 使用说明

部署完成后，您可以通过以下方式使用：

1. **Web API**: `http://your-server-ip/`
2. **健康检查**: `http://your-server-ip/health`
3. **文档上传**: `POST http://your-server-ip/upload`
4. **文档查询**: `POST http://your-server-ip/query`

## 🔧 故障排除

```bash
# 检查Python环境
source /opt/raganything/venv/bin/activate
python -c "import sys; print(sys.path)"

# 检查依赖
pip list | grep -E "(torch|openai|dashscope)"

# 检查服务日志
journalctl -u raganything --no-pager -l

# 重启服务
systemctl restart raganything
```
