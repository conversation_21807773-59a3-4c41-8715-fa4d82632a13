#!/usr/bin/env python
"""
地图功能与RAG系统集成模块
将高德地图API集成到RAG-Anything系统中
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any
from amap_api import AmapAPI, Location, RouteInfo

logger = logging.getLogger(__name__)

class MapRAGIntegration:
    """地图功能与RAG系统集成类"""
    
    def __init__(self, amap_api_key: str = None):
        """
        初始化地图RAG集成
        
        Args:
            amap_api_key: 高德地图API密钥
        """
        self.amap_api_key = amap_api_key or os.getenv("AMAP_API_KEY")
        self.amap = None
        
        # 地图相关关键词
        self.map_keywords = {
            "路线": [
                "路线", "导航", "怎么走", "怎么去", "路径",
                "开车", "驾车", "自驾", "步行", "走路", "徒步", "散步",
                "骑车", "骑行", "自行车", "单车",
                "坐公交", "公交", "地铁", "坐地铁", "乘地铁", "坐车"
            ],
            "位置": ["在哪里", "位置", "地址", "坐标", "经纬度"],
            "搜索": ["附近", "周边", "找", "搜索", "查找"],
            "天气": ["天气", "气温", "温度", "下雨", "晴天", "阴天"],
            "距离": ["多远", "距离", "多长时间", "几公里", "几分钟"]
        }
        
        # 常用POI分类
        self.poi_categories = {
            "餐饮": "050000",
            "美食": "050000",
            "餐厅": "050000",
            "酒店": "100000",
            "住宿": "100000",
            "景点": "110000",
            "旅游": "110000",
            "购物": "060000",
            "商场": "060000",
            "医院": "090000",
            "银行": "160000",
            "加油站": "010000",
            "超市": "060000",
            "药店": "090000",
            "学校": "141200",
            "公园": "110000",
            "停车场": "150000"
        }
        
        logger.info("🗺️ 地图RAG集成模块初始化完成")

    async def search_nearby_pois(self, location: str, keywords: str, radius: int = 1000) -> Dict[str, Any]:
        """
        搜索周边POI

        Args:
            location: 中心位置
            keywords: 搜索关键词
            radius: 搜索半径(米)

        Returns:
            Dict: 搜索结果
        """
        try:
            if not self.amap:
                self.amap = AmapAPI(self.amap_api_key)

            async with self.amap:
                # 获取POI类型
                poi_type = self.poi_categories.get(keywords, "")

                # 执行搜索
                results = await self.amap.search_around(location, keywords, radius, poi_type)

                if results:
                    # 格式化结果
                    formatted_results = []
                    for poi in results[:5]:  # 限制返回前5个结果
                        formatted_results.append({
                            "name": poi.name,
                            "address": poi.address,
                            "type": poi.type,
                            "distance": poi.distance,
                            "tel": poi.tel,
                            "location": {
                                "lng": poi.location.longitude,
                                "lat": poi.location.latitude
                            }
                        })

                    return {
                        "success": True,
                        "location": location,
                        "keywords": keywords,
                        "count": len(results),
                        "results": formatted_results,
                        "message": f"在{location}周边找到{len(results)}个{keywords}相关的地点"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"在{location}周边没有找到{keywords}相关的地点"
                    }

        except Exception as e:
            logger.error(f"❌ 周边搜索失败: {e}")
            return {
                "success": False,
                "message": f"周边搜索失败: {str(e)}"
            }

    async def get_weather_info(self, city: str) -> Dict[str, Any]:
        """
        获取天气信息

        Args:
            city: 城市名称

        Returns:
            Dict: 天气信息
        """
        try:
            if not self.amap:
                self.amap = AmapAPI(self.amap_api_key)

            async with self.amap:
                weather = await self.amap.get_weather_forecast(city, "base")

                if weather:
                    return {
                        "success": True,
                        "city": weather.city,
                        "weather": weather.weather,
                        "temperature": weather.temperature,
                        "winddirection": weather.winddirection,
                        "windpower": weather.windpower,
                        "humidity": weather.humidity,
                        "reporttime": weather.reporttime,
                        "message": f"{weather.city}当前天气{weather.weather}，温度{weather.temperature}°C，湿度{weather.humidity}%"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"无法获取{city}的天气信息"
                    }

        except Exception as e:
            logger.error(f"❌ 天气查询失败: {e}")
            return {
                "success": False,
                "message": f"天气查询失败: {str(e)}"
            }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self.amap_api_key:
            self.amap = AmapAPI(self.amap_api_key)
            await self.amap.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.amap:
            await self.amap.__aexit__(exc_type, exc_val, exc_tb)

    def is_map_related_query(self, query: str) -> bool:
        """
        判断查询是否与地图相关

        Args:
            query: 用户查询

        Returns:
            是否为地图相关查询
        """
        query_lower = query.lower()

        # 优先使用知识图谱的查询类型（不使用地图API）
        knowledge_priority_keywords = [
            "美食", "小吃", "特色菜", "名菜", "菜系", "料理", "美食种类", "美食品种",
            "景点", "旅游", "游玩", "参观", "观光", "名胜", "古迹", "博物馆", "公园", "游玩景点",
            "文化", "历史", "介绍", "特色", "推荐", "有名", "著名",
            "什么好吃", "哪里好玩", "有什么", "有哪些", "种类有哪些", "品种"
        ]

        # 如果包含知识优先关键词，不使用地图API
        for keyword in knowledge_priority_keywords:
            if keyword in query_lower:
                logger.info(f"🧠 检测到知识优先查询关键词: '{keyword}'，使用RAG系统")
                return False

        # 1. 检查传统地图关键词
        for category, keywords in self.map_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    return True

        # 2. 检查POI类型关键词（但排除美食和景点相关）
        map_only_poi_types = ["酒店", "住宿", "医院", "银行", "加油站", "超市", "药店", "学校"]
        for poi_type in map_only_poi_types:
            if poi_type in query_lower:
                # 如果包含POI类型，进一步检查是否包含地点信息
                if self._contains_location_info(query_lower):
                    return True

        # 3. 检查路线查询模式
        route_patterns = [
            r'从.+?到.+?(?:怎么走|路线|导航|怎么去)',
            r'从.+?去.+?(?:怎么走|路线|导航|怎么去)',
            r'.+?到.+?(?:怎么走|路线|导航|怎么去)',
            r'.+?去.+?(?:怎么走|路线|导航|怎么去)',
        ]

        import re
        for pattern in route_patterns:
            if re.search(pattern, query_lower):
                logger.info(f"🛣️ 检测到路线查询模式，使用地图API")
                return True

        # 4. 检查地理位置模式（但排除美食和景点）
        location_patterns = [
            r'.+?(市|区|县|镇|街道|路|街).+?(酒店|住宿|医院|银行|超市|药店|学校)',
            r'(北京|上海|广州|深圳|天津|重庆|杭州|南京|武汉|成都|西安|苏州).+?(酒店|住宿|医院|银行|超市|药店|学校)',
            r'.+?(的|有).+?(酒店|住宿|医院|银行|超市|药店|学校)',
        ]

        for pattern in location_patterns:
            if re.search(pattern, query_lower):
                return True

        return False

    def _contains_location_info(self, query: str) -> bool:
        """检查查询是否包含地点信息"""
        location_indicators = [
            "市", "区", "县", "镇", "街道", "路", "街", "村", "乡",
            "北京", "上海", "广州", "深圳", "天津", "重庆", "杭州", "南京", "武汉", "成都", "西安", "苏州",
            "朝阳", "海淀", "西城", "东城", "丰台", "石景山", "昌平", "大兴", "通州", "顺义",
            "浦东", "黄浦", "徐汇", "长宁", "静安", "普陀", "虹口", "杨浦", "闵行", "宝山",
            "天河", "越秀", "荔湾", "海珠", "白云", "番禺", "花都", "南沙", "从化", "增城"
        ]

        return any(indicator in query for indicator in location_indicators)

    def _extract_location_from_query(self, query: str) -> Optional[str]:
        """从查询中直接提取地点信息"""
        import re

        # 匹配"XX市XX区"、"XX的XX"等模式
        location_patterns = [
            r'([\u4e00-\u9fa5]+市[\u4e00-\u9fa5]+区)',  # 上海市浦东新区
            r'([\u4e00-\u9fa5]+市)',  # 上海市
            r'([\u4e00-\u9fa5]+区)',  # 浦东新区
            r'([\u4e00-\u9fa5]+县)',  # XX县
            r'([\u4e00-\u9fa5]+镇)',  # XX镇
        ]

        for pattern in location_patterns:
            match = re.search(pattern, query)
            if match:
                location = match.group(1)
                logger.info(f"🎯 从查询中提取地点: {location}")
                return location

        return None

    def _extract_route_points(self, query: str) -> tuple:
        """从查询中提取起点和终点"""
        import re

        # 匹配"从XX到XX"模式
        patterns = [
            r'从(.+?)到(.+?)(?:怎么走|路线|导航|怎么去)',
            r'从(.+?)去(.+?)(?:怎么走|路线|导航|怎么去)',
            r'(.+?)到(.+?)(?:怎么走|路线|导航|怎么去)',
            r'(.+?)去(.+?)(?:怎么走|路线|导航|怎么去)',
        ]

        for pattern in patterns:
            match = re.search(pattern, query)
            if match:
                origin = match.group(1).strip()
                destination = match.group(2).strip()

                # 清理起点和终点
                origin = origin.replace("从", "").strip()
                destination = destination.replace("到", "").replace("去", "").strip()

                logger.info(f"🎯 提取路线点: {origin} -> {destination}")
                return origin, destination

        return None, None

    def _detect_transport_mode(self, query: str) -> str:
        """
        从查询中检测交通方式

        Args:
            query: 用户查询

        Returns:
            交通方式: 'driving', 'walking', 'bicycling', 'transit'
        """
        query_lower = query.lower()

        # 步行关键词（明确的步行词汇）
        walking_keywords = [
            "步行", "走路", "徒步", "散步", "走着去", "走过去",
            "步行路线", "走路去", "徒步去", "散步去", "步行怎么去"
        ]

        # 骑行关键词
        bicycling_keywords = [
            "骑车", "骑行", "自行车", "单车", "骑自行车", "骑单车",
            "骑车路线", "骑行路线", "自行车路线"
        ]

        # 公交/地铁关键词
        transit_keywords = [
            "坐公交", "公交", "地铁", "坐地铁", "乘地铁", "坐车",
            "公共交通", "公交路线", "地铁路线", "乘车路线",
            "坐公交车", "乘公交", "地铁怎么去", "公交怎么去"
        ]

        # 驾车关键词
        driving_keywords = [
            "开车", "驾车", "自驾", "开车去", "驾车路线", "自驾路线",
            "开车怎么去", "驾车怎么去", "自驾怎么去", "开车路线"
        ]

        # 按优先级检测（具体的交通方式优先于默认驾车）

        # 检测驾车关键词（优先检测，因为最常用）
        for keyword in driving_keywords:
            if keyword in query_lower:
                logger.info(f"🚗 检测到驾车关键词: '{keyword}'")
                return 'driving'

        # 检测公交/地铁关键词
        for keyword in transit_keywords:
            if keyword in query_lower:
                logger.info(f"🚌 检测到公交关键词: '{keyword}'")
                return 'transit'

        # 检测骑行关键词
        for keyword in bicycling_keywords:
            if keyword in query_lower:
                logger.info(f"🚴 检测到骑行关键词: '{keyword}'")
                return 'bicycling'

        # 检测步行关键词（明确的步行词汇才识别为步行）
        for keyword in walking_keywords:
            if keyword in query_lower:
                logger.info(f"🚶 检测到步行关键词: '{keyword}'")
                return 'walking'

        # 默认使用驾车
        logger.info(f"🚗 未检测到特定交通方式，默认使用驾车")
        return 'driving'

    def _extract_city_from_address(self, address: str) -> str:
        """从地址中提取城市名称"""
        if "北京" in address:
            return "北京"
        elif "上海" in address:
            return "上海"
        elif "广州" in address:
            return "广州"
        elif "深圳" in address:
            return "深圳"
        elif "天津" in address:
            return "天津"
        elif "重庆" in address:
            return "重庆"
        else:
            # 尝试提取市级地名
            import re
            match = re.search(r'(\w+市)', address)
            if match:
                return match.group(1)
            return "北京"  # 默认

    def _get_transport_icon(self, transport_mode: str) -> str:
        """获取交通方式图标"""
        icons = {
            'driving': '🚗',
            'walking': '🚶',
            'bicycling': '🚴',
            'transit': '🚌'
        }
        return icons.get(transport_mode, '🚗')

    def _get_transport_name(self, transport_mode: str) -> str:
        """获取交通方式中文名称"""
        names = {
            'driving': '驾车',
            'walking': '步行',
            'bicycling': '骑行',
            'transit': '公交'
        }
        return names.get(transport_mode, '驾车')

    def _get_transit_strategy(self, query: str) -> int:
        """
        根据查询内容选择公交策略

        Args:
            query: 用户查询

        Returns:
            公交策略代码
        """
        query_lower = query.lower()

        # 明确的公交车关键词 - 不乘地铁
        bus_only_keywords = [
            "坐公交", "乘公交", "公交车", "坐公交车", "乘公交车",
            "公交路线", "公交怎么去", "公交怎么走"
        ]

        # 明确的地铁关键词 - 优先地铁
        subway_keywords = [
            "地铁", "坐地铁", "乘地铁", "地铁路线",
            "地铁怎么去", "地铁怎么走", "轨道交通"
        ]

        # 检查是否明确要求只坐公交车
        for keyword in bus_only_keywords:
            if keyword in query_lower:
                logger.info(f"🚌 检测到公交车关键词: '{keyword}' - 使用不乘地铁策略")
                return 5  # 不乘地铁

        # 检查是否明确要求坐地铁
        for keyword in subway_keywords:
            if keyword in query_lower:
                logger.info(f"🚇 检测到地铁关键词: '{keyword}' - 使用最快捷策略")
                return 0  # 最快捷（优先地铁）

        # 默认使用最快捷策略
        logger.info(f"🚌 使用默认最快捷策略")
        return 0

    def _parse_transit_steps(self, route_result) -> list:
        """
        解析公交路线的详细步骤

        Args:
            route_result: 路线规划结果

        Returns:
            详细步骤列表
        """
        detailed_steps = []

        # 如果有原始的steps数据，尝试解析
        if hasattr(route_result, 'raw_data') and route_result.raw_data:
            try:
                route_data = route_result.raw_data.get('route', {})
                transits = route_data.get('transits', [])

                if transits:
                    transit = transits[0]  # 取第一个方案
                    segments = transit.get('segments', [])

                    step_num = 1
                    for segment in segments:
                        # 处理步行段
                        if 'walking' in segment:
                            walking = segment['walking']
                            walking_steps = walking.get('steps', [])

                            if walking_steps:
                                for walk_step in walking_steps:
                                    instruction = walk_step.get('instruction', '')
                                    if instruction:
                                        detailed_steps.append(f"🚶 {instruction}")

                        # 处理公交段
                        if 'bus' in segment:
                            bus = segment['bus']
                            buslines = bus.get('buslines', [])

                            for busline in buslines:
                                line_name = busline.get('name', '公交线路')
                                departure_stop = busline.get('departure_stop', {}).get('name', '起点站')
                                arrival_stop = busline.get('arrival_stop', {}).get('name', '终点站')

                                # 根据线路名称选择合适的图标
                                if "地铁" in line_name or "号线" in line_name:
                                    icon = "🚇"  # 地铁图标
                                else:
                                    icon = "🚌"  # 公交车图标

                                detailed_steps.append(f"{icon} 乘坐{line_name}")
                                detailed_steps.append(f"   📍 从{departure_stop}上车")
                                detailed_steps.append(f"   📍 到{arrival_stop}下车")

                # 如果解析成功，返回详细步骤
                if detailed_steps:
                    return detailed_steps

            except Exception as e:
                logger.warning(f"⚠️ 公交路线详细解析失败: {e}")

        # 如果详细解析失败，使用原有的简单步骤
        if hasattr(route_result, 'steps') and route_result.steps:
            return [step for step in route_result.steps if step.strip()]

        # 如果都没有，返回基本信息
        return [
            "🚇 请根据导航软件查看详细的换乘方案",
            "📱 建议使用地铁APP查看实时班次信息",
            "🕐 注意地铁运营时间，合理安排出行"
        ]

    async def _handle_route_query_with_data(self, query: str, city: str = "北京") -> Dict[str, Any]:
        """
        处理路线查询并返回地图数据

        Args:
            query: 用户查询
            city: 城市名称

        Returns:
            包含文本和地图数据的字典
        """
        try:
            # 提取起点和终点
            locations = self.extract_locations_from_query(query)

            origin = None
            destination = None

            # 从查询中提取起点和终点
            if locations.get("origins"):
                origin = locations["origins"][0]
            if locations.get("destinations"):
                destination = locations["destinations"][0]

            # 如果没有明确的起点，尝试从查询中提取
            if not origin or not destination:
                origin, destination = self._extract_route_points(query)

            if not origin or not destination:
                return {
                    'text': "请明确指出起点和终点，例如：从朝阳区到海淀区",
                    'map_data': {},
                    'map_action': 'none'
                }

            # 检测交通方式
            transport_mode = self._detect_transport_mode(query)

            # 增强地址信息
            origin = self._enhance_location_name(origin)
            destination = self._enhance_location_name(destination)

            # 对于公交查询，进一步确保地址包含城市信息
            if transport_mode == 'transit':
                if not any(city in origin for city in ['北京', '上海', '广州', '深圳']):
                    origin = f"北京{origin}"
                if not any(city in destination for city in ['北京', '上海', '广州', '深圳']):
                    destination = f"北京{destination}"

            logger.info(f"🛣️ 路线规划: {origin} -> {destination} (交通方式: {transport_mode})")

            # 调用高德API进行路线规划
            if not self.amap:
                self.amap = AmapAPI(self.amap_api_key)

            async with self.amap:
                # 获取起点和终点坐标
                origin_location = await self.amap.geocode(origin)
                dest_location = await self.amap.geocode(destination)

                if not origin_location or not dest_location:
                    return {
                        'text': f"无法找到起点 '{origin}' 或终点 '{destination}' 的位置信息",
                        'map_data': {},
                        'map_action': 'none'
                    }

                # 根据检测到的交通方式进行路线规划
                origin_coord = f"{origin_location.longitude},{origin_location.latitude}"
                dest_coord = f"{dest_location.longitude},{dest_location.latitude}"

                if transport_mode == 'walking':
                    route_result = await self.amap.walking_route(origin_coord, dest_coord)
                elif transport_mode == 'bicycling':
                    route_result = await self.amap.bicycling_route(origin_coord, dest_coord)
                    # 如果骑行服务不可用，降级为步行
                    if not route_result:
                        logger.warning("⚠️ 骑行路线规划失败，降级为步行路线")
                        route_result = await self.amap.walking_route(origin_coord, dest_coord)
                        if route_result:
                            transport_mode = 'walking'  # 更新交通方式
                elif transport_mode == 'transit':
                    # 公交路线需要城市参数，从起点地址中提取
                    city = self._extract_city_from_address(origin_location.address)

                    # 根据具体关键词选择公交策略
                    strategy = self._get_transit_strategy(query)
                    route_result = await self.amap.transit_route(origin_coord, dest_coord, city, strategy=strategy)

                    # 公交路线特殊处理：只返回文字信息，不显示地图路线
                    if route_result:
                        transport_icon = self._get_transport_icon(transport_mode)
                        transport_name = self._get_transport_name(transport_mode)

                        # 确定是公交车还是地铁路线
                        is_subway = False
                        is_bus = False

                        if hasattr(route_result, 'raw_data') and route_result.raw_data:
                            try:
                                route_data = route_result.raw_data.get('route', {})
                                transits = route_data.get('transits', [])

                                if transits:
                                    transit = transits[0]  # 取第一个方案
                                    segments = transit.get('segments', [])

                                    for segment in segments:
                                        if 'bus' in segment:
                                            bus = segment['bus']
                                            buslines = bus.get('buslines', [])

                                            for busline in buslines:
                                                line_name = busline.get('name', '')
                                                if "地铁" in line_name or "号线" in line_name:
                                                    is_subway = True
                                                else:
                                                    is_bus = True
                            except Exception:
                                pass

                        # 根据路线类型选择合适的图标和描述
                        if is_subway and not is_bus:
                            route_type_icon = "🚇"
                            route_type_name = "地铁"
                            route_type_desc = "地铁路线，快速便捷"
                        elif is_bus and not is_subway:
                            route_type_icon = "🚌"
                            route_type_name = "公交车"
                            route_type_desc = "公交车路线，覆盖广泛"
                        else:
                            route_type_icon = "🚇🚌"
                            route_type_name = "公交地铁"
                            route_type_desc = "公交地铁联运，绿色出行"

                        # 构建详细的公交路线文字说明
                        response_text = f"{route_type_icon} 为您规划从{origin}到{destination}的{route_type_name}路线：\n\n"
                        response_text += f"📏 总距离：{route_result.distance}\n"
                        response_text += f"⏱️ 预计时间：{route_result.duration}\n"
                        response_text += f"🌿 {route_type_desc}\n\n"

                        response_text += "🚇 详细乘车方案：\n"

                        # 解析详细的公交路线信息
                        detailed_steps = self._parse_transit_steps(route_result)
                        for i, step in enumerate(detailed_steps, 1):
                            response_text += f"{i}. {step}\n"

                        response_text += "\n💡 温馨提示：公交地铁路线为固定线路，请按照乘车方案准时出行。建议提前查看实时班次信息。"

                        # 公交查询只返回文字，不返回地图数据
                        return {
                            'text': response_text,
                            'map_data': {},
                            'map_action': 'none'
                        }
                    else:
                        return {
                            'text': f"抱歉，无法规划从{origin}到{destination}的公交路线",
                            'map_data': {},
                            'map_action': 'none'
                        }
                else:  # driving
                    route_result = await self.amap.driving_route(origin_coord, dest_coord)

                if route_result:
                    # 获取交通方式图标和名称
                    transport_icon = self._get_transport_icon(transport_mode)
                    transport_name = self._get_transport_name(transport_mode)

                    # 构建文本回答
                    response_text = f"{transport_icon} 为您规划从{origin}到{destination}的{transport_name}路线：\n\n"
                    response_text += f"📏 总距离：{route_result.distance}\n"
                    response_text += f"⏱️ 预计时间：{route_result.duration}\n"

                    # 根据交通方式显示不同信息
                    if transport_mode == 'driving':
                        response_text += f"💰 过路费：{route_result.tolls}\n"
                        response_text += f"🚦 红绿灯：{route_result.traffic_lights}个\n\n"
                    elif transport_mode == 'walking':
                        response_text += f"👟 步行路线，无需费用\n\n"
                    elif transport_mode == 'bicycling':
                        response_text += f"🚴 骑行路线，环保出行\n\n"
                    elif transport_mode == 'transit':
                        response_text += f"🚌 公交路线，绿色出行\n\n"

                    response_text += "📋 详细路线：\n"
                    for i, step in enumerate(route_result.steps[:8], 1):  # 显示前8个步骤
                        response_text += f"{i}. {step}\n"

                    if len(route_result.steps) > 8:
                        response_text += f"... 还有{len(route_result.steps) - 8}个步骤\n"

                    response_text += "\n🗺️ 详细路线已在地图上显示，请查看可视化导航路线！"

                    # 解析polyline数据用于地图显示
                    route_path = self._decode_polyline(route_result.polyline) if route_result.polyline else []
                    logger.info(f"🗺️ 解码后路径点数: {len(route_path)}")

                    return {
                        'text': response_text,
                        'map_data': {
                            'origin': {
                                'lng': origin_location.longitude,
                                'lat': origin_location.latitude,
                                'name': origin,
                                'address': origin_location.address
                            },
                            'destination': {
                                'lng': dest_location.longitude,
                                'lat': dest_location.latitude,
                                'name': destination,
                                'address': dest_location.address
                            },
                            'route_path': route_path,
                            'route_info': {
                                'distance': route_result.distance,
                                'duration': route_result.duration,
                                'tolls': route_result.tolls,
                                'traffic_lights': route_result.traffic_lights,
                                'transport_mode': transport_mode,
                                'transport_icon': transport_icon,
                                'transport_name': transport_name
                            },
                            'steps': route_result.steps
                        },
                        'map_action': 'show_route'
                    }
                else:
                    return {
                        'text': f"抱歉，无法规划从{origin}到{destination}的路线",
                        'map_data': {},
                        'map_action': 'none'
                    }

        except Exception as e:
            logger.error(f"❌ 路线查询处理失败: {e}")
            return {
                'text': "抱歉，路线规划遇到问题，请稍后再试。",
                'map_data': {},
                'map_action': 'none'
            }

    def _decode_polyline(self, polyline: str) -> list:
        """
        解码高德地图的polyline数据

        Args:
            polyline: 高德地图返回的polyline字符串

        Returns:
            坐标点列表 [[lng, lat], [lng, lat], ...]
        """
        if not polyline:
            return []

        try:
            # 高德地图的polyline是分号分隔的坐标对
            points = []
            coords = polyline.split(';')

            for coord in coords:
                if ',' in coord:
                    lng, lat = coord.split(',')
                    points.append([float(lng), float(lat)])

            logger.info(f"🗺️ 解码polyline成功，共{len(points)}个坐标点")
            return points

        except Exception as e:
            logger.error(f"❌ polyline解码失败: {e}")
            return []

    def _decode_polyline(self, polyline: str) -> list:
        """
        解码高德地图的polyline数据

        Args:
            polyline: 高德地图返回的polyline字符串

        Returns:
            坐标点列表 [[lng, lat], [lng, lat], ...]
        """
        if not polyline:
            return []

        try:
            # 高德地图的polyline是分号分隔的坐标对
            points = []
            coords = polyline.split(';')

            for coord in coords:
                if ',' in coord:
                    lng, lat = coord.split(',')
                    points.append([float(lng), float(lat)])

            logger.info(f"🗺️ 解码polyline成功，共{len(points)}个坐标点")
            return points

        except Exception as e:
            logger.error(f"❌ polyline解码失败: {e}")
            return []

    def extract_locations_from_query(self, query: str) -> Dict[str, Any]:
        """
        从查询中提取位置信息

        Args:
            query: 用户查询

        Returns:
            提取的位置信息
        """
        locations = {
            "origins": [],
            "destinations": [],
            "keywords": [],
            "search_location": None  # 新增：用于周边搜索的中心位置
        }

        # 查找"从...到..."模式
        if "从" in query and "到" in query:
            parts = query.split("从")[1].split("到")
            if len(parts) >= 2:
                origin = parts[0].strip()
                destination = parts[1].split()[0].strip()
                locations["origins"].append(origin)
                locations["destinations"].append(destination)

        # 查找"去..."模式
        elif "去" in query:
            parts = query.split("去")
            if len(parts) >= 2:
                destination = parts[1].split()[0].strip()
                locations["destinations"].append(destination)

        # 查找"XX附近/周边的XX"模式
        import re

        # 匹配"地点+附近/周边+的+POI"模式
        nearby_patterns = [
            r'(.+?)(?:附近|周边)(?:的|有什么|有哪些)?(.+?)(?:\?|？|$)',
            r'(.+?)(?:附近|周边)(.+?)(?:\?|？|$)',
            r'在(.+?)找(.+?)(?:\?|？|$)',
            r'(.+?)(?:的|有)(.+?)(?:在哪|在哪里)(?:\?|？|$)'
        ]

        for pattern in nearby_patterns:
            match = re.search(pattern, query)
            if match:
                location_part = match.group(1).strip()
                poi_part = match.group(2).strip()

                logger.info(f"🔍 正则匹配成功 - 位置: '{location_part}', POI: '{poi_part}'")

                # 清理位置名称
                location_part = location_part.replace("在", "").replace("找", "").strip()
                poi_part = poi_part.replace("的", "").replace("有什么", "").replace("有哪些", "").strip()

                logger.info(f"🧹 清理后 - 位置: '{location_part}', POI: '{poi_part}'")

                # 检查是否是有效的地点名称（不是POI类型）
                if location_part and location_part not in self.poi_categories and len(location_part) > 1:
                    # 智能地址补全 - 为常见地名添加完整地址
                    enhanced_location = self._enhance_location_name(location_part)
                    locations["search_location"] = enhanced_location
                    logger.info(f"✅ 设置搜索位置: {enhanced_location}")

                # 检查POI类型
                for poi_name in self.poi_categories.keys():
                    if poi_name in poi_part:
                        locations["keywords"].append(poi_name)
                        logger.info(f"✅ 匹配POI类型: {poi_name}")
                        break
                else:
                    # 如果没有匹配到预定义的POI类型，使用原始关键词
                    if poi_part:
                        locations["keywords"].append(poi_part)
                        logger.info(f"✅ 使用原始关键词: {poi_part}")

                break

        # 查找其他POI关键词
        if not locations["keywords"]:
            for poi_name, category_code in self.poi_categories.items():
                if poi_name in query:
                    locations["keywords"].append(poi_name)

        return locations

    def _enhance_location_name(self, location: str) -> str:
        """
        智能增强地点名称，添加完整地址信息

        Args:
            location: 原始地点名称

        Returns:
            增强后的地点名称
        """
        # 北京地区常见地名映射
        beijing_locations = {
            "南口镇": "北京市昌平区南口镇",
            "三里屯": "北京市朝阳区三里屯",
            "西单": "北京市西城区西单",
            "中关村": "北京市海淀区中关村",
            "王府井": "北京市东城区王府井",
            "国贸": "北京市朝阳区国贸",
            "五道口": "北京市海淀区五道口",
            "望京": "北京市朝阳区望京",
            "亦庄": "北京市大兴区亦庄",
            "通州": "北京市通州区",
            "昌平": "北京市昌平区",
            "海淀": "北京市海淀区",
            "朝阳": "北京市朝阳区",
            "东城": "北京市东城区",
            "西城": "北京市西城区",
            "丰台": "北京市丰台区",
            "石景山": "北京市石景山区",
            "门头沟": "北京市门头沟区",
            "房山": "北京市房山区",
            "大兴": "北京市大兴区",
            "顺义": "北京市顺义区",
            "密云": "北京市密云区",
            "延庆": "北京市延庆区",
            "怀柔": "北京市怀柔区",
            "平谷": "北京市平谷区"
        }

        # 如果是北京地区的地名，返回完整地址
        if location in beijing_locations:
            enhanced = beijing_locations[location]
            logger.info(f"🎯 地址增强: {location} -> {enhanced}")
            return enhanced

        # 如果已经包含省市信息，直接返回
        if any(keyword in location for keyword in ["省", "市", "区", "县", "镇", "街道"]):
            return location

        # 其他情况返回原地址
        return location

    async def handle_route_query(self, query: str, user_location: str = None) -> str:
        """
        处理路线查询
        
        Args:
            query: 用户查询
            user_location: 用户当前位置
            
        Returns:
            路线信息回答
        """
        if not self.amap:
            return "抱歉，地图服务暂时不可用。请稍后再试。"
        
        try:
            locations = self.extract_locations_from_query(query)
            
            origin = None
            destination = None
            
            # 确定起点
            if locations["origins"]:
                origin = locations["origins"][0]
            elif user_location:
                origin = user_location
            else:
                return "请提供起点位置，或者告诉我您的当前位置。"
            
            # 确定终点
            if locations["destinations"]:
                destination = locations["destinations"][0]
            else:
                return "请提供目的地。"
            
            # 规划路线
            route = await self.amap.route_planning(origin, destination)
            
            if route:
                response = f"🛣️ 为您规划的路线如下：\n\n"
                response += f"📍 起点：{origin}\n"
                response += f"📍 终点：{destination}\n"
                response += f"📏 距离：{route.distance}\n"
                response += f"⏱️ 预计时间：{route.duration}\n"
                response += f"💰 过路费：{route.tolls}\n"
                response += f"🚦 红绿灯：{route.traffic_lights}个\n\n"
                
                response += "🗺️ 详细路线：\n"
                for i, step in enumerate(route.steps[:5], 1):  # 显示前5步
                    response += f"{i}. {step}\n"
                
                if len(route.steps) > 5:
                    response += f"... 还有{len(route.steps) - 5}个步骤\n"
                
                response += "\n💡 建议：出发前请关注实时路况，注意安全驾驶！"
                
                return response
            else:
                return f"抱歉，无法规划从{origin}到{destination}的路线。请检查地址是否正确。"
                
        except Exception as e:
            logger.error(f"❌ 路线查询处理失败: {e}")
            return "抱歉，路线查询遇到问题，请稍后再试。"

    async def handle_poi_query(self, query: str, city: str = "北京") -> str:
        """
        处理POI搜索查询
        
        Args:
            query: 用户查询
            city: 搜索城市
            
        Returns:
            POI搜索结果
        """
        if not self.amap:
            return "抱歉，地图服务暂时不可用。请稍后再试。"
        
        try:
            locations = self.extract_locations_from_query(query)

            # 确定搜索位置 - 优先从查询中提取地点信息
            search_location = locations.get("search_location")

            # 如果没有提取到具体位置，尝试从查询中直接提取地点信息
            if not search_location:
                search_location = self._extract_location_from_query(query) or city

            # 确定搜索关键词
            keywords = []
            if locations["keywords"]:
                keywords.extend(locations["keywords"])

            # 从查询中提取其他关键词
            for poi_name in self.poi_categories.keys():
                if poi_name in query:
                    keywords.append(poi_name)

            if not keywords:
                # 尝试提取查询中的名词作为关键词
                cleaned_query = query.replace("附近", "").replace("周边", "").replace("找", "").replace("的", "").strip()
                if locations.get("search_location"):
                    cleaned_query = cleaned_query.replace(locations["search_location"], "").strip()
                keywords = [cleaned_query] if cleaned_query else ["餐厅"]

            search_keyword = keywords[0] if keywords else "餐厅"

            logger.info(f"🔍 搜索参数 - 位置: {search_location}, 关键词: {search_keyword}")

            # 使用新的周边搜索API
            search_result = await self.search_nearby_pois(search_location, search_keyword, 2000)

            if search_result["success"]:
                # 显示实际搜索的位置和关键词
                actual_location = search_result.get('location', search_location)
                response = f"🔍 为您找到{actual_location}周边的{search_keyword}：\n\n"

                for i, poi in enumerate(search_result["results"][:5], 1):
                    response += f"{i}. 📍 {poi['name']}\n"
                    response += f"   📮 地址：{poi['address']}\n"
                    if poi.get('tel'):
                        response += f"   📞 电话：{poi['tel']}\n"
                    if poi.get('distance'):
                        response += f"   📏 距离：{poi['distance']}米\n"
                    response += "\n"

                response += "💡 提示：您可以询问具体路线规划，我来为您导航！"

                return response
            else:
                return search_result["message"]
                
        except Exception as e:
            logger.error(f"❌ POI查询处理失败: {e}")
            return "抱歉，搜索遇到问题，请稍后再试。"

    async def handle_weather_query(self, query: str, city: str = "北京") -> str:
        """
        处理天气查询
        
        Args:
            query: 用户查询
            city: 查询城市
            
        Returns:
            天气信息回答
        """
        if not self.amap:
            return "抱歉，天气服务暂时不可用。请稍后再试。"
        
        try:
            # 从查询中提取城市名
            if "天气" in query:
                parts = query.split("天气")
                if len(parts) > 0:
                    potential_city = parts[0].strip()
                    if potential_city and len(potential_city) <= 10:  # 简单验证
                        city = potential_city
            
            # 使用新的天气查询API
            weather_result = await self.get_weather_info(city)

            if weather_result["success"]:
                response = f"🌤️ {weather_result['city']}天气信息：\n\n"
                response += f"☀️ 天气：{weather_result['weather']}\n"
                response += f"🌡️ 温度：{weather_result['temperature']}°C\n"
                response += f"💨 风向：{weather_result['winddirection']}\n"
                response += f"💪 风力：{weather_result['windpower']}级\n"
                response += f"💧 湿度：{weather_result['humidity']}%\n"
                response += f"⏰ 更新时间：{weather_result['reporttime']}\n"

                # 根据天气给出建议
                weather_desc = weather_result['weather']
                if "雨" in weather_desc:
                    response += "\n☔ 建议：记得带伞出行哦！"
                elif "晴" in weather_desc:
                    response += "\n☀️ 建议：天气不错，适合出行！"
                elif "雪" in weather_desc:
                    response += "\n❄️ 建议：注意保暖，路面可能湿滑！"
                elif "阴" in weather_desc:
                    response += "\n☁️ 建议：天气阴沉，适合室内活动！"

                response += "\n💡 出行建议：根据天气情况合理安排行程，注意保暖/防晒！"

                return response
            else:
                return weather_result["message"]
                
        except Exception as e:
            logger.error(f"❌ 天气查询处理失败: {e}")
            return "抱歉，天气查询遇到问题，请稍后再试。"

    async def handle_location_query(self, query: str) -> str:
        """
        处理位置查询
        
        Args:
            query: 用户查询
            
        Returns:
            位置信息回答
        """
        if not self.amap:
            return "抱歉，位置服务暂时不可用。请稍后再试。"
        
        try:
            # 提取地址
            address = query.replace("在哪里", "").replace("位置", "").replace("地址", "").strip()
            
            if not address:
                return "请提供您要查询的地点名称。"
            
            location = await self.amap.geocode(address)
            
            if location:
                response = f"📍 {address} 的位置信息：\n\n"
                response += f"🏠 详细地址：{location.address}\n"
                response += f"🌐 经纬度：({location.longitude:.6f}, {location.latitude:.6f})\n"
                
                # 获取周边信息
                pois = await self.amap.poi_search("", city="", page_size=3)
                if pois:
                    response += "\n🔍 周边信息：\n"
                    for poi in pois[:3]:
                        response += f"• {poi['name']}\n"
                
                response += "\n💡 您可以询问如何到达这里，我来为您规划路线！"
                
                return response
            else:
                return f"抱歉，无法找到{address}的位置信息。请检查地址是否正确。"
                
        except Exception as e:
            logger.error(f"❌ 位置查询处理失败: {e}")
            return "抱歉，位置查询遇到问题，请稍后再试。"

    async def process_map_query(self, query: str, user_location: str = None, city: str = "北京") -> str:
        """
        处理地图相关查询的主入口

        Args:
            query: 用户查询
            user_location: 用户当前位置
            city: 默认城市

        Returns:
            处理结果
        """
        is_map_related = self.is_map_related_query(query)
        logger.info(f"🔍 查询识别结果: {query} -> {'地图查询' if is_map_related else '非地图查询'}")

        if not is_map_related:
            return None  # 不是地图相关查询

        logger.info(f"🗺️ 处理地图查询: {query}")
        
        try:
            # 路线查询
            if any(keyword in query for keyword in self.map_keywords["路线"]):
                return await self.handle_route_query(query, user_location)
            
            # 位置查询
            elif any(keyword in query for keyword in self.map_keywords["位置"]):
                return await self.handle_location_query(query)
            
            # 天气查询
            elif any(keyword in query for keyword in self.map_keywords["天气"]):
                return await self.handle_weather_query(query, city)
            
            # POI搜索
            elif any(keyword in query for keyword in self.map_keywords["搜索"]):
                return await self.handle_poi_query(query, city)
            
            # 默认处理
            else:
                return await self.handle_poi_query(query, city)
                
        except Exception as e:
            logger.error(f"❌ 地图查询处理失败: {e}")
            return "抱歉，地图服务遇到问题，请稍后再试。"

    async def process_map_query_with_data(self, query: str, user_location: str = None, city: str = "北京") -> Optional[Dict[str, Any]]:
        """
        处理地图相关查询并返回结构化数据（包含地图标记信息）

        Args:
            query: 用户查询
            user_location: 用户当前位置
            city: 默认城市

        Returns:
            包含文本回答和地图数据的字典
        """
        is_map_related = self.is_map_related_query(query)
        logger.info(f"🔍 查询识别结果: {query} -> {'地图查询' if is_map_related else '非地图查询'}")

        if not is_map_related:
            return None  # 不是地图相关查询

        logger.info(f"🗺️ 处理地图查询: {query}")

        try:
            # 检查查询类型并处理
            if any(keyword in query for keyword in self.map_keywords["天气"]):
                return await self._handle_weather_query_with_data(query, city)
            elif any(keyword in query for keyword in self.map_keywords["搜索"]):
                return await self._handle_poi_query_with_data(query, city)
            elif any(keyword in query for keyword in self.map_keywords["路线"]):
                return await self._handle_route_query_with_data(query, city)
            else:
                # 默认处理为POI搜索
                return await self._handle_poi_query_with_data(query, city)

        except Exception as e:
            logger.error(f"❌ 地图查询处理异常: {e}")
            return {
                'text': "抱歉，地图查询遇到问题，请稍后再试。",
                'map_data': {},
                'map_action': 'none'
            }

    async def _handle_poi_query_with_data(self, query: str, city: str = "北京") -> Dict[str, Any]:
        """
        处理POI查询并返回地图数据

        Args:
            query: 用户查询
            city: 城市名称

        Returns:
            包含文本和地图数据的字典
        """
        try:
            locations = self.extract_locations_from_query(query)

            # 确定搜索位置 - 优先从查询中提取地点信息
            search_location = locations.get("search_location")

            # 如果没有提取到具体位置，尝试从查询中直接提取地点信息
            if not search_location:
                search_location = self._extract_location_from_query(query) or city

            # 确定搜索关键词
            keywords = []
            if locations["keywords"]:
                keywords.extend(locations["keywords"])

            # 从查询中提取其他关键词
            for poi_name in self.poi_categories.keys():
                if poi_name in query:
                    keywords.append(poi_name)

            if not keywords:
                # 尝试提取查询中的名词作为关键词
                cleaned_query = query.replace("附近", "").replace("周边", "").replace("找", "").replace("的", "").strip()
                if locations.get("search_location"):
                    cleaned_query = cleaned_query.replace(locations["search_location"], "").strip()
                keywords = [cleaned_query] if cleaned_query else ["餐厅"]

            search_keyword = keywords[0] if keywords else "餐厅"

            logger.info(f"🔍 搜索参数 - 位置: {search_location}, 关键词: {search_keyword}")

            # 使用新的周边搜索API
            search_result = await self.search_nearby_pois(search_location, search_keyword, 2000)

            if search_result["success"]:
                # 显示实际搜索的位置和关键词
                actual_location = search_result.get('location', search_location)
                response_text = f"🔍 为您找到{actual_location}周边的{search_keyword}：\n\n"

                # 准备地图标记数据
                markers = []
                center_location = None

                for i, poi in enumerate(search_result["results"][:5], 1):
                    response_text += f"{i}. 📍 {poi['name']}\n"
                    response_text += f"   📮 地址：{poi['address']}\n"
                    if poi.get('tel'):
                        response_text += f"   📞 电话：{poi['tel']}\n"
                    if poi.get('distance'):
                        response_text += f"   📏 距离：{poi['distance']}米\n"
                    response_text += "\n"

                    # 添加地图标记
                    if poi.get('location'):
                        marker = {
                            'id': f"poi_{i}",
                            'lng': poi['location']['lng'],
                            'lat': poi['location']['lat'],
                            'title': poi['name'],
                            'content': f"{poi['name']}<br>{poi['address']}",
                            'type': 'poi',
                            'category': search_keyword
                        }
                        markers.append(marker)

                        # 设置地图中心点（使用第一个POI的位置）
                        if center_location is None:
                            center_location = {
                                'lng': poi['location']['lng'],
                                'lat': poi['location']['lat']
                            }

                response_text += "💡 提示：您可以询问具体路线规划，我来为您导航！"

                return {
                    'text': response_text,
                    'map_data': {
                        'markers': markers,
                        'center': center_location,
                        'zoom': 13,
                        'search_location': actual_location,
                        'search_keyword': search_keyword
                    },
                    'map_action': 'show_pois'
                }
            else:
                return {
                    'text': search_result["message"],
                    'map_data': {},
                    'map_action': 'none'
                }

        except Exception as e:
            logger.error(f"❌ POI查询处理失败: {e}")
            return {
                'text': "抱歉，搜索遇到问题，请稍后再试。",
                'map_data': {},
                'map_action': 'none'
            }

    async def _handle_weather_query_with_data(self, query: str, city: str = "北京") -> Dict[str, Any]:
        """
        处理天气查询并返回地图数据

        Args:
            query: 用户查询
            city: 城市名称

        Returns:
            包含文本和地图数据的字典
        """
        try:
            # 从查询中提取城市名称
            locations = self.extract_locations_from_query(query)
            target_city = locations.get("destination") or locations.get("origin") or city

            # 尝试从查询中直接提取地点信息
            if not target_city or target_city == city:
                extracted_location = self._extract_location_from_query(query)
                if extracted_location:
                    target_city = extracted_location

            # 使用新的天气查询API
            weather_result = await self.get_weather_info(target_city)

            if weather_result["success"]:
                response_text = f"🌤️ {weather_result['city']}天气信息：\n\n"
                response_text += f"☀️ 天气：{weather_result['weather']}\n"
                response_text += f"🌡️ 温度：{weather_result['temperature']}°C\n"
                response_text += f"💨 风向：{weather_result['winddirection']}\n"
                response_text += f"💪 风力：{weather_result['windpower']}级\n"
                response_text += f"💧 湿度：{weather_result['humidity']}%\n"
                response_text += f"⏰ 更新时间：{weather_result['reporttime']}\n"

                # 根据天气给出建议
                weather_desc = weather_result['weather']
                if "雨" in weather_desc:
                    response_text += "\n☔ 建议：记得带伞出行哦！"
                elif "晴" in weather_desc:
                    response_text += "\n☀️ 建议：天气不错，适合出行！"
                elif "雪" in weather_desc:
                    response_text += "\n❄️ 建议：注意保暖，路面可能湿滑！"
                elif "阴" in weather_desc:
                    response_text += "\n☁️ 建议：天气阴沉，适合室内活动！"

                response_text += "\n💡 出行建议：根据天气情况合理安排行程，注意保暖/防晒！"

                # 获取城市坐标用于地图标记
                if not self.amap:
                    self.amap = AmapAPI(self.amap_api_key)

                async with self.amap:
                    city_location = await self.amap.geocode(target_city)

                    if city_location:
                        # 创建天气标记
                        weather_marker = {
                            'id': 'weather_location',
                            'lng': city_location.longitude,
                            'lat': city_location.latitude,
                            'title': f"{weather_result['city']}天气",
                            'content': f"{weather_result['city']}<br>{weather_result['weather']} {weather_result['temperature']}°C",
                            'type': 'weather',
                            'weather_data': weather_result
                        }

                        return {
                            'text': response_text,
                            'map_data': {
                                'markers': [weather_marker],
                                'center': {
                                    'lng': city_location.longitude,
                                    'lat': city_location.latitude
                                },
                                'zoom': 10,
                                'search_location': weather_result['city']
                            },
                            'map_action': 'show_weather'
                        }
                    else:
                        return {
                            'text': response_text,
                            'map_data': {},
                            'map_action': 'none'
                        }
            else:
                return {
                    'text': weather_result["message"],
                    'map_data': {},
                    'map_action': 'none'
                }

        except Exception as e:
            logger.error(f"❌ 天气查询处理失败: {e}")
            return {
                'text': "抱歉，天气查询遇到问题，请稍后再试。",
                'map_data': {},
                'map_action': 'none'
            }



# 使用示例
async def main():
    """测试地图RAG集成功能"""
    
    # 请设置您的高德地图API密钥
    # export AMAP_API_KEY="your_amap_api_key"
    
    async with MapRAGIntegration() as map_rag:
        
        test_queries = [
            "从北京到上海怎么走",
            "附近有什么好吃的",
            "北京天气怎么样",
            "天安门在哪里",
            "找个酒店",
            "去机场的路线"
        ]
        
        for query in test_queries:
            print(f"\n{'='*50}")
            print(f"查询: {query}")
            print(f"{'='*50}")
            
            result = await map_rag.process_map_query(query, user_location="北京市朝阳区")
            if result:
                print(result)
            else:
                print("非地图相关查询")

if __name__ == "__main__":
    asyncio.run(main())
