#!/usr/bin/env python
"""
简化的RAGAnything测试脚本
只执行基本的文档分析查询
"""

import os
import argparse
import asyncio
import logging
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc, logger
from raganything import RAGAnything, RAGAnythingConfig

# 配置日志
logging.basicConfig(level=logging.INFO)

def detect_and_configure_device():
    """检测并配置最佳设备"""
    try:
        import torch
        
        if torch.backends.mps.is_available():
            device = "mps"
            print(f"🚀 使用MPS GPU加速: {device}")
            os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
            return device
        elif torch.cuda.is_available():
            device = "cuda"
            print(f"🚀 使用CUDA GPU加速: {device}")
            return device
        else:
            device = "cpu"
            print(f"⚠️ 使用CPU: {device}")
            return device
    except ImportError:
        print("⚠️ PyTorch未安装，使用CPU")
        return "cpu"

async def simple_rag_analysis(file_path: str, api_key: str, device: str = None):
    """简单的RAG文档分析"""
    
    # 设置API配置
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # 检测设备
    if not device:
        device = detect_and_configure_device()
    
    print(f"\n📄 分析文档: {file_path}")
    print(f"🔧 使用设备: {device}")
    print(f"🌐 API端点: {base_url}")
    print("=" * 50)
    
    try:
        # 创建配置
        config = RAGAnythingConfig(
            working_dir="./rag_storage",
            mineru_parse_method="auto",
            enable_image_processing=True,
            enable_table_processing=True,
            enable_equation_processing=True,
        )
        
        # 定义模型函数
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return openai_complete_if_cache(
                "qwen-turbo",
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )
        
        def vision_model_func(prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs):
            if image_data:
                return openai_complete_if_cache(
                    "qwen-vl-plus",
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt} if system_prompt else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{image_data}"},
                                },
                            ],
                        } if image_data else {"role": "user", "content": prompt},
                    ],
                    api_key=api_key,
                    base_url=base_url,
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)
        
        embedding_func = EmbeddingFunc(
            embedding_dim=1536,
            max_token_size=8192,
            func=lambda texts: openai_embed(
                texts,
                model="text-embedding-v1",
                api_key=api_key,
                base_url=base_url,
            ),
        )
        
        # 初始化RAGAnything
        rag = RAGAnything(
            config=config,
            llm_model_func=llm_model_func,
            vision_model_func=vision_model_func,
            embedding_func=embedding_func,
        )
        
        print("🏗️ 开始处理文档...")
        
        # 处理文档
        await rag.process_document_complete(
            file_path=file_path,
            output_dir="./output",
            parse_method="auto",
            device=device,
            backend="pipeline"
        )
        
        print("✅ 文档处理完成！")
        print("\n🔍 开始文档分析...")
        
        # 执行基本查询
        queries = [
            "请总结这个文档的主要内容",
            "文档中包含哪些重要信息？",
            "这个文档的核心主题是什么？"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n❓ 查询 {i}: {query}")
            result = await rag.aquery(query, mode="hybrid")
            print(f"📝 回答 {i}: {result}")
            print("-" * 50)
        
        print("\n🎉 分析完成！")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化的RAG文档分析")
    parser.add_argument("file_path", help="要分析的文档路径")
    parser.add_argument("--api-key", 
                       default=os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY"),
                       help="API密钥")
    parser.add_argument("--device", 
                       choices=["auto", "cpu", "cuda", "mps"], 
                       default="auto",
                       help="设备选择")
    
    args = parser.parse_args()
    
    if not args.api_key:
        print("❌ 请提供API密钥")
        print("使用 --api-key 参数或设置环境变量 DASHSCOPE_API_KEY")
        return
    
    device = None if args.device == "auto" else args.device
    
    asyncio.run(simple_rag_analysis(args.file_path, args.api_key, device))

if __name__ == "__main__":
    main()
