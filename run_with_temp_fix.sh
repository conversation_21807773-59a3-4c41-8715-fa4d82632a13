#!/bin/bash
# 临时目录修复脚本

echo "🛠️ 修复临时目录权限问题..."

# 创建项目临时目录
PROJECT_TEMP_DIR="$(pwd)/temp"
mkdir -p "$PROJECT_TEMP_DIR"
chmod 755 "$PROJECT_TEMP_DIR"

# 设置环境变量
export TMPDIR="$PROJECT_TEMP_DIR"
export TMP="$PROJECT_TEMP_DIR"
export TEMP="$PROJECT_TEMP_DIR"
export TEMPDIR="$PROJECT_TEMP_DIR"

echo "✅ 临时目录已设置: $PROJECT_TEMP_DIR"

# 运行RAGAnything
echo "🚀 启动RAGAnything..."
python examples/raganything_example.py "$@"
