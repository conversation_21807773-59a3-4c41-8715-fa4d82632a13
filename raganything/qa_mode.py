#!/usr/bin/env python
"""
Interactive Q&A Mode for RAGAnything

This module provides an interactive question-answering interface for RAGAnything,
supporting multiple query modes, conversation history, and multimodal queries.
"""

import asyncio
import json
import time
import re
import unicodedata
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from raganything.raganything import RAGAnything


def clean_text(text: str) -> str:
    """
    清理文本，移除可能导致编码问题的字符

    Args:
        text: 输入文本

    Returns:
        清理后的文本
    """
    if not text:
        return ""

    try:
        # 1. 规范化Unicode字符
        text = unicodedata.normalize('NFC', text)

        # 2. 移除代理对字符（surrogate pairs）
        text = re.sub(r'[\uD800-\uDFFF]', '', text)

        # 3. 移除不可见控制字符
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # 4. 确保UTF-8兼容性
        text = text.encode('utf-8', 'ignore').decode('utf-8')

        return text
    except Exception as e:
        logging.warning(f"文本清理失败: {e}")
        # 如果清理失败，尝试最基本的清理
        return text.encode('ascii', 'ignore').decode('ascii')


class QASession:
    """Manages a Q&A session with conversation history and context"""
    
    def __init__(self, session_id: str = None):
        self.session_id = session_id or f"qa_session_{int(time.time())}"
        self.conversation_history: List[Dict] = []
        self.start_time = datetime.now()
        self.total_queries = 0
        self.successful_queries = 0
        
    def add_interaction(self, query: str, response: str, mode: str,
                       response_time: float, multimodal: bool = False):
        """Add a Q&A interaction to the session history"""
        # 清理查询和响应文本
        query = clean_text(query)
        response = clean_text(response)

        interaction = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "response": response,
            "mode": mode,
            "response_time": response_time,
            "multimodal": multimodal,
            "query_id": len(self.conversation_history) + 1
        }
        self.conversation_history.append(interaction)
        self.total_queries += 1
        if response and not response.startswith("❌"):
            self.successful_queries += 1
    
    def get_recent_context(self, num_interactions: int = 3) -> str:
        """Get recent conversation context for better continuity"""
        if not self.conversation_history:
            return ""
        
        recent = self.conversation_history[-num_interactions:]
        context_parts = []
        
        for interaction in recent:
            context_parts.append(f"Q: {interaction['query']}")
            context_parts.append(f"A: {interaction['response'][:200]}...")
        
        return "\n".join(context_parts)
    
    def save_session(self, file_path: str):
        """Save session to file"""
        session_data = {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "total_queries": self.total_queries,
            "successful_queries": self.successful_queries,
            "conversation_history": self.conversation_history
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
    
    def load_session(self, file_path: str):
        """Load session from file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        self.session_id = session_data["session_id"]
        self.start_time = datetime.fromisoformat(session_data["start_time"])
        self.total_queries = session_data["total_queries"]
        self.successful_queries = session_data["successful_queries"]
        self.conversation_history = session_data["conversation_history"]


class InteractiveQA:
    """Interactive Q&A interface for RAGAnything"""
    
    def __init__(self, rag_instance: RAGAnything, session: QASession = None):
        self.rag = rag_instance
        self.session = session or QASession()
        self.logger = logging.getLogger(__name__)
        
        # Available query modes
        self.query_modes = {
            "hybrid": "混合检索 - 结合局部和全局策略 (推荐)",
            "local": "局部检索 - 基于文本块相似度",
            "global": "全局检索 - 基于实体关系图",
            "naive": "朴素检索 - 简单向量相似度"
        }
        
        # Current settings
        self.current_mode = "hybrid"
        self.enable_context = True
        self.max_context_length = 3
        
    def display_welcome(self):
        """Display welcome message and instructions"""
        print("🤖 RAGAnything 交互式问答模式")
        print("=" * 50)
        print("欢迎使用RAGAnything智能问答系统！")
        print(f"会话ID: {self.session.session_id}")
        print(f"当前查询模式: {self.current_mode} - {self.query_modes[self.current_mode]}")
        print("\n📋 可用命令:")
        print("  /help     - 显示帮助信息")
        print("  /mode     - 切换查询模式")
        print("  /history  - 查看对话历史")
        print("  /stats    - 查看会话统计")
        print("  /context  - 切换上下文模式")
        print("  /save     - 保存会话")
        print("  /load     - 加载会话")
        print("  /clear    - 清空对话历史")
        print("  /clean    - 清理编码问题")
        print("  /exit     - 退出问答模式")
        print("\n💡 提示:")
        print("  - 直接输入问题开始查询")
        print("  - 支持多模态查询 (输入 /multimodal 了解更多)")
        print("  - 输入 'quit' 或 'exit' 退出")
        print("=" * 50)
    
    def display_help(self):
        """Display detailed help information"""
        print("\n📖 详细帮助信息")
        print("-" * 30)
        print("🔍 查询模式说明:")
        for mode, description in self.query_modes.items():
            marker = "✅" if mode == self.current_mode else "  "
            print(f"  {marker} {mode}: {description}")
        
        print("\n🎨 多模态查询:")
        print("  支持在查询中包含表格、图像等多模态内容")
        print("  示例: '分析这个表格数据' + 表格内容")
        
        print("\n💬 上下文模式:")
        status = "开启" if self.enable_context else "关闭"
        print(f"  当前状态: {status}")
        print("  开启时会考虑最近的对话历史来提供更连贯的回答")
        
        print("\n📊 会话管理:")
        print("  - 自动保存对话历史")
        print("  - 支持会话导出和导入")
        print("  - 提供详细的查询统计")
        print("  - 自动清理编码问题")

        print("\n🛠️ 故障排除:")
        print("  /clean - 清理编码问题，解决UTF-8错误")
        print("  /clear - 清空对话历史，重置会话状态")
        print("  重启系统 - 如果遇到持续问题，可以退出重新启动")
    
    def display_stats(self):
        """Display session statistics"""
        print(f"\n📊 会话统计信息")
        print("-" * 20)
        print(f"会话ID: {self.session.session_id}")
        print(f"开始时间: {self.session.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总查询数: {self.session.total_queries}")
        print(f"成功查询数: {self.session.successful_queries}")
        
        if self.session.total_queries > 0:
            success_rate = (self.session.successful_queries / self.session.total_queries) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        if self.session.conversation_history:
            avg_response_time = sum(h.get('response_time', 0) for h in self.session.conversation_history) / len(self.session.conversation_history)
            print(f"平均响应时间: {avg_response_time:.2f}秒")
            
            multimodal_count = sum(1 for h in self.session.conversation_history if h.get('multimodal', False))
            print(f"多模态查询数: {multimodal_count}")
    
    def display_history(self, num_recent: int = 5):
        """Display recent conversation history"""
        if not self.session.conversation_history:
            print("📝 暂无对话历史")
            return
        
        print(f"\n📝 最近 {num_recent} 条对话记录:")
        print("-" * 40)
        
        recent_history = self.session.conversation_history[-num_recent:]
        for interaction in recent_history:
            timestamp = interaction['timestamp'][:19]  # Remove microseconds
            mode_icon = "🎨" if interaction.get('multimodal', False) else "💬"
            print(f"\n{mode_icon} [{timestamp}] 模式: {interaction['mode']}")
            print(f"❓ Q: {interaction['query']}")
            print(f"📝 A: {interaction['response'][:150]}{'...' if len(interaction['response']) > 150 else ''}")
            print(f"⏱️ 响应时间: {interaction['response_time']:.2f}秒")
    
    async def handle_command(self, command: str) -> bool:
        """Handle special commands. Returns True if should continue, False if should exit"""
        command = command.lower().strip()
        
        if command in ['/exit', '/quit', 'exit', 'quit']:
            return False
        
        elif command == '/help':
            self.display_help()
        
        elif command == '/mode':
            await self.change_mode()
        
        elif command == '/history':
            self.display_history()
        
        elif command == '/stats':
            self.display_stats()
        
        elif command == '/context':
            self.toggle_context_mode()
        
        elif command == '/save':
            await self.save_session()
        
        elif command == '/load':
            await self.load_session()
        
        elif command == '/clear':
            self.clear_history()

        elif command == '/clean':
            self.clean_encoding_issues()

        elif command == '/multimodal':
            self.display_multimodal_help()

        else:
            print(f"❓ 未知命令: {command}")
            print("输入 /help 查看可用命令")
        
        return True
    
    async def change_mode(self):
        """Change query mode interactively"""
        print("\n🔍 选择查询模式:")
        for i, (mode, description) in enumerate(self.query_modes.items(), 1):
            marker = "✅" if mode == self.current_mode else f"{i}."
            print(f"  {marker} {mode}: {description}")
        
        try:
            choice = input("\n请输入模式名称或编号 (回车保持当前): ").strip()
            if not choice:
                return
            
            if choice.isdigit():
                mode_list = list(self.query_modes.keys())
                if 1 <= int(choice) <= len(mode_list):
                    new_mode = mode_list[int(choice) - 1]
                else:
                    print("❌ 无效的编号")
                    return
            else:
                if choice in self.query_modes:
                    new_mode = choice
                else:
                    print("❌ 无效的模式名称")
                    return
            
            self.current_mode = new_mode
            print(f"✅ 查询模式已切换为: {new_mode}")
            
        except KeyboardInterrupt:
            print("\n操作已取消")
    
    def toggle_context_mode(self):
        """Toggle context awareness mode"""
        self.enable_context = not self.enable_context
        status = "开启" if self.enable_context else "关闭"
        print(f"✅ 上下文模式已{status}")
    
    def clear_history(self):
        """Clear conversation history"""
        confirm = input("确认清空对话历史? (y/N): ").strip().lower()
        if confirm in ['y', 'yes', '是']:
            self.session.conversation_history.clear()
            self.session.total_queries = 0
            self.session.successful_queries = 0
            print("✅ 对话历史已清空")
        else:
            print("操作已取消")

    def clean_encoding_issues(self):
        """Clean encoding issues in conversation history"""
        print("🧹 正在清理编码问题...")

        cleaned_count = 0
        for interaction in self.session.conversation_history:
            original_query = interaction.get('query', '')
            original_response = interaction.get('response', '')

            cleaned_query = clean_text(original_query)
            cleaned_response = clean_text(original_response)

            if cleaned_query != original_query or cleaned_response != original_response:
                interaction['query'] = cleaned_query
                interaction['response'] = cleaned_response
                cleaned_count += 1

        print(f"✅ 编码清理完成，处理了 {cleaned_count} 条记录")
        if cleaned_count > 0:
            print("💡 建议保存会话以持久化清理结果")
    
    async def save_session(self):
        """Save current session"""
        try:
            filename = input("输入保存文件名 (默认: session.json): ").strip()
            if not filename:
                filename = "session.json"
            
            if not filename.endswith('.json'):
                filename += '.json'
            
            self.session.save_session(filename)
            print(f"✅ 会话已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    async def load_session(self):
        """Load a session"""
        try:
            filename = input("输入加载文件名: ").strip()
            if not filename:
                print("操作已取消")
                return
            
            if not Path(filename).exists():
                print(f"❌ 文件不存在: {filename}")
                return
            
            self.session.load_session(filename)
            print(f"✅ 会话已从 {filename} 加载")
            print(f"加载了 {len(self.session.conversation_history)} 条对话记录")
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
    
    def display_multimodal_help(self):
        """Display multimodal query help"""
        print("\n🎨 多模态查询帮助")
        print("-" * 25)
        print("RAGAnything支持在查询中包含多模态内容:")
        print("\n📊 表格查询示例:")
        print("  查询: '分析这个销售数据表格'")
        print("  然后系统会提示您输入表格数据")
        print("\n🖼️ 图像查询:")
        print("  目前支持通过文档中已处理的图像进行查询")
        print("\n💡 使用方法:")
        print("  1. 输入包含'表格'、'图像'等关键词的查询")
        print("  2. 系统会自动检测并提示输入相应的多模态内容")
        print("  3. 按提示输入内容即可获得综合分析结果")
    
    async def process_query(self, query: str) -> str:
        """Process a user query and return response"""
        start_time = time.time()

        try:
            # 清理输入文本，防止编码问题
            query = clean_text(query)
            if not query.strip():
                return "❌ 查询内容为空或包含无效字符，请重新输入"

            # Check if this might be a multimodal query
            multimodal_keywords = ['表格', '图像', '图片', '图表', '数据表', 'table', 'image', 'chart']
            is_multimodal_query = any(keyword in query.lower() for keyword in multimodal_keywords)
            
            if is_multimodal_query:
                # Ask user if they want to add multimodal content
                add_multimodal = input("\n🎨 检测到可能的多模态查询，是否添加多模态内容? (y/N): ").strip().lower()
                
                if add_multimodal in ['y', 'yes', '是']:
                    multimodal_content = await self.get_multimodal_content()
                    if multimodal_content:
                        # Add context if enabled
                        enhanced_query = query
                        if self.enable_context:
                            context = self.session.get_recent_context(self.max_context_length)
                            if context:
                                # 清理上下文文本
                                context = clean_text(context)
                                enhanced_query = f"基于以下对话历史:\n{context}\n\n当前问题: {query}"

                        # 清理多模态查询文本
                        enhanced_query = clean_text(enhanced_query)
                        response = await self.rag.aquery_with_multimodal(
                            enhanced_query,
                            multimodal_content=multimodal_content,
                            mode=self.current_mode
                        )
                        
                        response_time = time.time() - start_time
                        self.session.add_interaction(query, response, self.current_mode, response_time, multimodal=True)
                        return response
            
            # Regular text query
            enhanced_query = query
            if self.enable_context:
                context = self.session.get_recent_context(self.max_context_length)
                if context:
                    # 清理上下文文本
                    context = clean_text(context)
                    enhanced_query = f"基于以下对话历史:\n{context}\n\n当前问题: {query}"

            # 清理最终查询文本
            enhanced_query = clean_text(enhanced_query)
            response = await self.rag.aquery(enhanced_query, mode=self.current_mode)
            
            response_time = time.time() - start_time
            self.session.add_interaction(query, response, self.current_mode, response_time, multimodal=False)
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            error_response = f"❌ 查询处理失败: {str(e)}"
            self.session.add_interaction(query, error_response, self.current_mode, response_time)
            return error_response
    
    async def get_multimodal_content(self) -> Optional[List[Dict]]:
        """Get multimodal content from user input"""
        print("\n📝 请选择多模态内容类型:")
        print("1. 表格数据")
        print("2. 取消")
        
        try:
            choice = input("请选择 (1-2): ").strip()
            
            if choice == "1":
                print("\n📊 请输入表格数据 (CSV格式，用回车结束输入):")
                print("示例: 产品,销量,价格\\nA,100,50\\nB,200,30")
                
                table_data = input("表格数据: ").strip()
                if table_data:
                    return [{
                        "type": "table",
                        "table_data": table_data.replace('\\n', '\n')
                    }]
            
            return None
            
        except KeyboardInterrupt:
            print("\n操作已取消")
            return None
    
    async def run(self):
        """Run the interactive Q&A session"""
        self.display_welcome()
        
        try:
            while True:
                try:
                    # Get user input
                    query = input(f"\n[{self.current_mode}] 🤔 请输入您的问题 (或输入命令): ").strip()

                    # 清理用户输入
                    query = clean_text(query)

                    if not query:
                        continue
                    
                    # Handle commands
                    if query.startswith('/') or query.lower() in ['exit', 'quit']:
                        should_continue = await self.handle_command(query)
                        if not should_continue:
                            break
                        continue
                    
                    # Process query
                    print("\n🔍 正在处理您的问题...")
                    response = await self.process_query(query)
                    
                    # Display response
                    print(f"\n🤖 回答:")
                    print("-" * 40)
                    print(response)
                    print("-" * 40)
                    
                except KeyboardInterrupt:
                    print("\n\n👋 感谢使用RAGAnything问答系统！")
                    break
                except EOFError:
                    print("\n\n👋 会话结束")
                    break
        
        finally:
            # Auto-save session
            try:
                auto_save_path = f"auto_save_{self.session.session_id}.json"
                self.session.save_session(auto_save_path)
                print(f"✅ 会话已自动保存到: {auto_save_path}")
            except Exception as e:
                print(f"⚠️ 自动保存失败: {e}")
            
            # Display final stats
            print(f"\n📊 本次会话统计:")
            print(f"总查询数: {self.session.total_queries}")
            print(f"成功查询数: {self.session.successful_queries}")
            if self.session.total_queries > 0:
                success_rate = (self.session.successful_queries / self.session.total_queries) * 100
                print(f"成功率: {success_rate:.1f}%")


# Convenience function for easy access
async def start_qa_mode(rag_instance: RAGAnything, session: QASession = None):
    """Start interactive Q&A mode"""
    qa = InteractiveQA(rag_instance, session)
    await qa.run()
