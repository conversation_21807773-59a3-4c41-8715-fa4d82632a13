#!/usr/bin/env python
"""
简单语音RAG系统 - 回到最稳定的基础版本
基于最初的raganything_example.py，添加最基本的语音功能
"""

import os
import asyncio
import logging
import tempfile
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 修复临时目录问题
def fix_temp_directory():
    """修复临时目录权限问题"""
    try:
        project_temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(project_temp_dir, exist_ok=True)
        os.environ["TMPDIR"] = project_temp_dir
        os.environ["TMP"] = project_temp_dir
        os.environ["TEMP"] = project_temp_dir
        test_file = tempfile.NamedTemporaryFile(delete=True)
        test_file.close()
        print(f"✅ 临时目录已设置: {project_temp_dir}")
        return True
    except Exception as e:
        print(f"⚠️ 临时目录设置失败: {e}")
        return False

# 修复临时目录
fix_temp_directory()

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc
from raganything import RAGAnything, RAGAnythingConfig

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 检查语音依赖
try:
    import edge_tts
    import pygame
    TTS_AVAILABLE = True
    print("✅ 语音合成可用")
except ImportError:
    TTS_AVAILABLE = False
    print("❌ 语音合成不可用，请安装: pip install edge-tts pygame")

try:
    import whisper
    import sounddevice as sd
    import numpy as np
    VOICE_INPUT_AVAILABLE = True
    print("✅ 语音输入可用")
except ImportError:
    VOICE_INPUT_AVAILABLE = False
    print("❌ 语音输入不可用，请安装: pip install openai-whisper sounddevice")


class SimpleVoiceRAG:
    """简单语音RAG系统 - 最基础稳定版本"""
    
    def __init__(self):
        # API配置
        self.api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY或OPENAI_API_KEY环境变量")
        
        print(f"🔑 API密钥: {self.api_key[:8]}...{self.api_key[-4:]}")
        
        # 初始化组件
        self.rag_system = None
        self.whisper_model = None
        
        # 加载Whisper模型
        if VOICE_INPUT_AVAILABLE:
            try:
                self.whisper_model = whisper.load_model("base")
                print("✅ Whisper模型加载成功")
            except Exception as e:
                print(f"❌ Whisper模型加载失败: {e}")
        
        # 初始化pygame
        if TTS_AVAILABLE:
            try:
                pygame.mixer.init()
                print("✅ 音频播放初始化成功")
            except Exception as e:
                print(f"❌ 音频播放初始化失败: {e}")
    
    async def initialize_rag(self):
        """初始化RAG系统 - 使用最简单的方式"""
        print("🔧 正在初始化RAG系统...")
        
        working_dir = "./rag_storage_server"
        if not os.path.exists(working_dir):
            print(f"❌ 知识库目录不存在: {working_dir}")
            return False
        
        try:
            # 使用最基本的配置
            config = RAGAnythingConfig(
                working_dir=working_dir,
                mineru_parse_method="auto",
                enable_image_processing=True,
                enable_table_processing=True,
                enable_equation_processing=True,
            )
            
            # LLM函数
            def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
                return openai_complete_if_cache(
                    "qwen-turbo",
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    api_key=self.api_key,
                    base_url=self.base_url,
                    **kwargs,
                )
            
            # 嵌入函数
            embedding_func = EmbeddingFunc(
                embedding_dim=1536,
                max_token_size=8192,
                func=lambda texts: openai_embed(
                    texts,
                    model="text-embedding-v1",
                    api_key=self.api_key,
                    base_url=self.base_url,
                ),
            )
            
            # 创建RAGAnything实例
            self.rag_system = RAGAnything(
                config=config,
                llm_model_func=llm_model_func,
                embedding_func=embedding_func,
            )
            
            # 确保LightRAG实例被正确初始化
            await self.rag_system._ensure_lightrag_initialized()
            
            print("✅ RAG系统初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ RAG系统初始化失败: {e}")
            return False
    
    def record_audio(self, duration=5):
        """录制音频"""
        if not VOICE_INPUT_AVAILABLE:
            return None
        
        try:
            print(f"🎤 开始录音 {duration} 秒，请说话...")
            sample_rate = 16000
            audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype=np.float32)
            sd.wait()
            print("🎤 录音完成")
            return audio.flatten()
        except Exception as e:
            print(f"❌ 录音失败: {e}")
            return None
    
    def speech_to_text(self, audio):
        """语音转文字"""
        if not self.whisper_model or audio is None:
            return None
        
        try:
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                import scipy.io.wavfile as wav
                wav.write(temp_file.name, 16000, (audio * 32767).astype(np.int16))
                
                # 使用Whisper识别
                result = self.whisper_model.transcribe(temp_file.name, language="zh")
                
                # 清理临时文件
                os.unlink(temp_file.name)
                
                return result["text"].strip()
                
        except Exception as e:
            print(f"❌ 语音识别失败: {e}")
            return None
    
    def detect_language(self, text):
        """检测文本语言"""
        import re

        # 检测中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        # 检测英文字符
        english_chars = re.findall(r'[a-zA-Z]', text)

        # 如果有中文字符，优先认为是中文
        if len(chinese_chars) > 0:
            return "zh"
        elif len(english_chars) > 0:
            return "en"
        else:
            return "zh"  # 默认中文

    def clean_text_for_display_and_speech(self, text):
        """最小化清理文本，只移除井号和星号"""
        import re

        # 处理Unicode编码问题
        try:
            text = text.encode('utf-8', errors='ignore').decode('utf-8')
        except Exception:
            pass

        # 移除代理对字符
        text = re.sub(r'[\ud800-\udfff]', '', text)

        # 只移除用户指定的两个符号
        text = re.sub(r'[#*]+', '', text)  # 只移除井号和星号

        # 移除References标记（这个影响阅读体验）
        text = re.sub(r'References?:', '', text)
        text = re.sub(r'\[KG\].*?\.pdf', '', text)
        text = re.sub(r'\[DC\].*?\.pdf', '', text)

        # 只做最基本的空白处理
        text = re.sub(r'\s+', ' ', text)  # 合并多余空白
        text = text.strip()

        return text

    def clean_input_text(self, text):
        """清理输入文本，处理编码问题"""
        import re

        # 处理Unicode编码问题
        try:
            # 强制清理无效字符
            text = text.encode('utf-8', errors='ignore').decode('utf-8')
        except Exception:
            pass

        # 移除代理对字符
        text = re.sub(r'[\ud800-\udfff]', '', text)

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        return text

    async def text_to_speech(self, text, language=None):
        """文本转语音 - 完整播放，智能停顿"""
        if not TTS_AVAILABLE:
            cleaned_text = self.clean_text_for_display_and_speech(text)
            print(f"🤖 回答: {cleaned_text}")
            return False

        try:
            # 轻度清理文本
            cleaned_text = self.clean_text_for_display_and_speech(text)

            if not cleaned_text.strip():
                print("⚠️ 清理后文本为空")
                return False

            # 检测语言
            if language is None:
                language = self.detect_language(cleaned_text)

            # 根据语言选择语音
            if language == "zh":
                voice = "zh-CN-XiaoxiaoNeural"  # 中文女声
            else:
                voice = "en-US-AriaNeural"      # 英文女声

            # 适当限制文本长度，但保持详细
            if len(cleaned_text) > 1500:
                cleaned_text = cleaned_text[:1500] + "..."

            print(f"🔊 正在生成完整语音... (语言: {'中文' if language == 'zh' else '英文'})")
            print(f"📝 文本长度: {len(cleaned_text)} 字符")

            # 使用Edge-TTS生成完整语音
            communicate = edge_tts.Communicate(cleaned_text, voice)

            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                audio_file = temp_file.name

            # 生成语音文件
            await communicate.save(audio_file)

            # 播放完整音频
            pygame.mixer.music.load(audio_file)
            pygame.mixer.music.play()

            # 等待播放完成
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)

            # 清理临时文件
            os.unlink(audio_file)

            print("✅ 完整语音播放完成")
            return True

        except Exception as e:
            print(f"❌ 语音合成失败: {e}")
            cleaned_text = self.clean_text_for_display_and_speech(text)
            print(f"🤖 回答: {cleaned_text}")
            return False
    
    async def query(self, question):
        """查询知识库"""
        try:
            # 清理输入问题，处理编码问题
            cleaned_question = self.clean_input_text(question)
            print(f"❓ 查询: {cleaned_question}")

            # 检测问题语言
            question_lang = self.detect_language(cleaned_question)

            # 构建详细查询提示
            if question_lang == "zh":
                # 中文问题，简化提示词，让模型自由发挥
                enhanced_question = f"请详细回答：{cleaned_question}。请提供尽可能详细和具体的信息，包括具体的数字、时间、地点、方式等。用中文回答。"
            else:
                # 英文问题，要求简洁但详细的回答
                enhanced_question = f"Please provide a concise but detailed answer to: {cleaned_question}. Include specific information like numbers, times, locations, and methods. Keep the answer under 500 words. Answer in English."

            # 使用最有效的查询策略
            print("🔍 正在查询知识库...")

            # 优先使用global模式（通常最详细）
            response = await self.rag_system.aquery(enhanced_question, mode="global")

            print(f"📊 查询结果长度: {len(response)} 字符")

            # 如果回答太短，尝试hybrid模式
            if len(response) < 800:
                print("🔄 回答较短，尝试hybrid模式...")
                response_hybrid = await self.rag_system.aquery(enhanced_question, mode="hybrid")
                if len(response_hybrid) > len(response):
                    response = response_hybrid
                    print(f"📋 使用hybrid模式结果，长度: {len(response)} 字符")

            # 清理回答文本用于显示
            cleaned_response = self.clean_text_for_display_and_speech(response)

            print(f"\n📝 回答:\n{cleaned_response}")

            # 返回原始回答（包含语言信息）
            return {
                "text": response,
                "cleaned_text": cleaned_response,
                "language": question_lang
            }

        except Exception as e:
            print(f"❌ 查询失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def voice_query(self):
        """语音查询"""
        if not VOICE_INPUT_AVAILABLE:
            print("❌ 语音输入不可用")
            return None

        # 录音
        audio = self.record_audio(duration=5)
        if audio is None:
            return None

        # 语音识别
        question = self.speech_to_text(audio)
        if not question:
            print("❌ 语音识别失败")
            return None

        print(f"🎤 识别结果: {question}")

        # 查询并播放回答
        result = await self.query(question)
        if result:
            await self.text_to_speech(result["text"], result["language"])

        return result
    
    async def interactive_mode(self):
        """交互模式"""
        print("\n🎤 简单语音RAG系统")
        print("=" * 40)
        print("1. 语音输入")
        print("2. 文字输入")
        print("3. 退出")
        print("=" * 40)
        
        while True:
            try:
                choice = input("\n请选择 (1/2/3): ").strip()
                
                if choice == "3":
                    print("👋 再见！")
                    break
                elif choice == "1":
                    await self.voice_query()
                elif choice == "2":
                    question = input("💬 请输入问题: ").strip()
                    if question:
                        result = await self.query(question)
                        if result:
                            tts_choice = input("🔊 播放语音? (y/n): ").strip().lower()
                            if tts_choice in ['y', 'yes']:
                                await self.text_to_speech(result["text"], result["language"])
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break


async def main():
    """主函数"""
    try:
        # 创建系统
        voice_rag = SimpleVoiceRAG()
        
        # 初始化
        if not await voice_rag.initialize_rag():
            return
        
        # 开始交互
        await voice_rag.interactive_mode()
        
    except Exception as e:
        print(f"❌ 系统错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
