#!/usr/bin/env python
"""
RAGAnything Interactive Q&A Mode Example

This script demonstrates how to use the interactive Q&A mode with RAGAnything.
It supports both processing new documents and querying existing knowledge bases.

Usage:
    # Process a document and start Q&A mode
    python qa_mode_example.py document.pdf --api-key your-api-key

    # Start Q&A mode with existing knowledge base
    python qa_mode_example.py --working-dir ./existing_rag_storage --api-key your-api-key

    # Load a previous Q&A session
    python qa_mode_example.py --working-dir ./rag_storage --session session.json --api-key your-api-key
"""

import os
import argparse
import asyncio
import logging
from pathlib import Path
import sys

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc, logger
from raganything import RAGAnything, RAGAnythingConfig
from raganything.qa_mode import InteractiveQA, QASession, start_qa_mode


def detect_and_configure_device():
    """Detect and configure the best available device"""
    try:
        import torch
        
        if torch.backends.mps.is_available():
            device = "mps"
            print(f"🚀 检测到MPS支持，使用Apple GPU加速: {device}")
            # Configure MPS environment
            os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
            return device
        elif torch.cuda.is_available():
            device = "cuda"
            print(f"🚀 检测到CUDA支持，使用NVIDIA GPU加速: {device}")
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
            return device
        else:
            device = "cpu"
            print(f"⚠️ 未检测到GPU支持，使用CPU: {device}")
            return device
    except ImportError:
        print("⚠️ PyTorch未安装，使用CPU")
        return "cpu"


async def initialize_rag_system(
    api_key: str,
    base_url: str = None,
    working_dir: str = "./rag_storage",
    device: str = None
) -> RAGAnything:
    """Initialize RAGAnything system"""
    
    # Set default API endpoint for Alibaba Cloud DashScope
    if not base_url:
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        logger.info(f"使用默认阿里云百炼端点: {base_url}")
    
    # Detect device if not specified
    if not device:
        device = detect_and_configure_device()
    else:
        print(f"🔧 使用指定设备: {device}")
    
    # Create configuration
    config = RAGAnythingConfig(
        working_dir=working_dir,
        mineru_parse_method="auto",
        enable_image_processing=True,
        enable_table_processing=True,
        enable_equation_processing=True,
    )

    # Define LLM model function - using Alibaba Cloud DashScope
    def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
        return openai_complete_if_cache(
            "qwen-turbo",  # Use Qwen model
            prompt,
            system_prompt=system_prompt,
            history_messages=history_messages,
            api_key=api_key,
            base_url=base_url,
            **kwargs,
        )

    # Define vision model function - supports multimodal
    def vision_model_func(
        prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs
    ):
        if image_data:
            return openai_complete_if_cache(
                "qwen-vl-plus",  # Use Qwen Vision model
                "",
                system_prompt=None,
                history_messages=[],
                messages=[
                    {"role": "system", "content": system_prompt}
                    if system_prompt
                    else None,
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                },
                            },
                        ],
                    }
                    if image_data
                    else {"role": "user", "content": prompt},
                ],
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )
        else:
            return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

    # Define embedding function - using Alibaba Cloud DashScope with batch size limit
    async def safe_embedding_func(texts):
        """Safe embedding function with batch size limit for DashScope API"""
        if isinstance(texts, str):
            texts = [texts]

        # DashScope API限制：批处理大小不能超过25
        batch_size = 20  # 设置为20以确保安全
        results = []

        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_result = await openai_embed(
                batch,
                model="text-embedding-v1",
                api_key=api_key,
                base_url=base_url,
            )
            results.extend(batch_result)

        return results

    embedding_func = EmbeddingFunc(
        embedding_dim=1536,  # DashScope embedding dimension
        max_token_size=8192,
        func=safe_embedding_func,
    )

    # Initialize RAGAnything
    rag = RAGAnything(
        config=config,
        llm_model_func=llm_model_func,
        vision_model_func=vision_model_func,
        embedding_func=embedding_func,
    )

    # Ensure LightRAG is initialized
    await rag._ensure_lightrag_initialized()

    print("🏗️ RAGAnything系统初始化完成")
    return rag


async def process_document_if_needed(rag: RAGAnything, file_path: str, output_dir: str, device: str):
    """Process document if file path is provided"""
    if not file_path:
        return
    
    print(f"\n📖 开始处理文档: {file_path}")
    
    try:
        await rag.process_document_complete(
            file_path=file_path,
            output_dir=output_dir,
            parse_method="auto",
            device=device,
            backend="pipeline"
        )
        print("✅ 文档处理完成！")
        
        # Quick test query to verify the system is working
        print("\n🧪 执行系统测试查询...")
        test_result = await rag.aquery("这个文档的主要内容是什么？", mode="hybrid")
        print(f"✅ 系统测试成功，知识库已就绪")
        
    except Exception as e:
        print(f"❌ 文档处理失败: {e}")
        print("您仍然可以使用现有的知识库进行问答")


def load_session_if_specified(session_file: str) -> QASession:
    """Load Q&A session if specified"""
    if not session_file:
        return None
    
    if not Path(session_file).exists():
        print(f"⚠️ 会话文件不存在: {session_file}")
        return None
    
    try:
        session = QASession()
        session.load_session(session_file)
        print(f"✅ 已加载会话: {session.session_id}")
        print(f"📝 包含 {len(session.conversation_history)} 条对话记录")
        return session
    except Exception as e:
        print(f"❌ 加载会话失败: {e}")
        return None


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="RAGAnything 交互式问答模式")
    parser.add_argument(
        "file_path", 
        nargs="?", 
        help="要处理的文档路径 (可选，如果不提供则使用现有知识库)"
    )
    parser.add_argument(
        "--working-dir", "-w", 
        default="./rag_storage", 
        help="工作目录路径"
    )
    parser.add_argument(
        "--output", "-o", 
        default="./output", 
        help="输出目录路径"
    )
    parser.add_argument(
        "--api-key",
        default=os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY"),
        help="阿里云百炼API密钥（默认从环境变量获取）",
    )
    parser.add_argument(
        "--base-url", 
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        help="API端点（默认阿里云百炼）"
    )
    parser.add_argument(
        "--device",
        choices=["auto", "cpu", "mps", "cuda"],
        default="auto",
        help="设备选择（auto=自动检测, cpu, mps, cuda）"
    )
    parser.add_argument(
        "--session", "-s",
        help="加载指定的会话文件"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细日志输出"
    )

    args = parser.parse_args()

    # Configure logging
    if args.verbose:
        logging.basicConfig(level=logging.INFO)
    else:
        logging.basicConfig(level=logging.WARNING)

    # Check API key
    if not args.api_key:
        print("❌ 错误: 需要阿里云百炼API密钥")
        print("请设置环境变量 DASHSCOPE_API_KEY 或使用 --api-key 参数")
        print("示例: export DASHSCOPE_API_KEY=sk-your-api-key")
        return

    # Validate API key format
    if not args.api_key.startswith('sk-'):
        print("⚠️ 警告: API密钥格式可能不正确，应以'sk-'开头")

    # Check document file if provided
    if args.file_path and not os.path.exists(args.file_path):
        print(f"❌ 错误: 文档文件不存在: {args.file_path}")
        return

    # Create output directory
    os.makedirs(args.output, exist_ok=True)
    os.makedirs(args.working_dir, exist_ok=True)

    # Determine device
    device = None if args.device == "auto" else args.device

    print("🚀 RAGAnything 交互式问答模式")
    print("=" * 50)
    print(f"📁 工作目录: {args.working_dir}")
    if args.file_path:
        print(f"📄 文档: {args.file_path}")
    print(f"🌐 API端点: {args.base_url}")
    print(f"🔑 API密钥: {args.api_key[:8]}...{args.api_key[-4:]}")
    print("=" * 50)

    try:
        # Initialize RAG system
        print("\n🔧 初始化RAGAnything系统...")
        rag = await initialize_rag_system(
            args.api_key, args.base_url, args.working_dir, device
        )

        # Process document if provided
        if args.file_path:
            await process_document_if_needed(rag, args.file_path, args.output, device or "auto")

        # Load session if specified
        session = load_session_if_specified(args.session)

        # Start interactive Q&A mode
        print("\n🎯 启动交互式问答模式...")
        await start_qa_mode(rag, session)

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        if args.verbose:
            import traceback
            print(f"错误详情:\n{traceback.format_exc()}")


if __name__ == "__main__":
    asyncio.run(main())
