#!/usr/bin/env python
"""
RAGAnything Windows版本示例脚本
适配阿里云百炼API和Windows CUDA GPU加速

基于Mac版本的成功实现，适配Windows环境：
1. CUDA GPU加速替代MPS
2. Windows路径处理
3. 批处理脚本支持
4. 阿里云百炼API集成
"""

import os
import argparse
import asyncio
import logging
import logging.config
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc, logger, set_verbose_debug
from raganything import RAGAnything, RAGAnythingConfig

def detect_and_configure_device():
    """检测并配置Windows最佳设备"""
    try:
        import torch
        
        if torch.cuda.is_available():
            device = "cuda"
            gpu_name = torch.cuda.get_device_name(0)
            gpu_count = torch.cuda.device_count()
            print(f"🚀 检测到CUDA支持，使用GPU加速: {device}")
            print(f"🎮 GPU设备: {gpu_name}")
            print(f"🔢 GPU数量: {gpu_count}")
            
            # 设置CUDA优化环境变量
            os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
            return device
        else:
            device = "cpu"
            print(f"⚠️ 未检测到CUDA支持，使用CPU: {device}")
            print("💡 如需GPU加速，请安装NVIDIA驱动和CUDA Toolkit")
            return device
    except ImportError:
        print("⚠️ PyTorch未安装，使用CPU")
        return "cpu"

def configure_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('raganything_windows.log', encoding='utf-8')
        ]
    )

async def process_with_rag(
    file_path: str,
    output_dir: str,
    api_key: str,
    base_url: str = None,
    working_dir: str = None,
    device: str = None,
):
    """
    使用RAGAnything处理文档 - Windows版本

    Args:
        file_path: 文档路径
        output_dir: 输出目录
        api_key: 阿里云百炼API密钥
        base_url: API端点（默认使用阿里云百炼）
        working_dir: 工作目录
        device: 设备选择（auto-detect如果未指定）
    """
    try:
        # 设置默认API端点
        if not base_url:
            base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
            logger.info(f"使用默认阿里云百炼端点: {base_url}")
        
        # 检测并配置设备
        if not device:
            device = detect_and_configure_device()
        else:
            print(f"🔧 使用指定设备: {device}")
        
        # Windows路径处理
        file_path = str(Path(file_path).resolve())
        output_dir = str(Path(output_dir).resolve())
        working_dir = str(Path(working_dir or "./rag_storage").resolve())
        
        print(f"📁 文档路径: {file_path}")
        print(f"📁 输出目录: {output_dir}")
        print(f"📁 工作目录: {working_dir}")
        
        # 创建RAGAnything配置
        config = RAGAnythingConfig(
            working_dir=working_dir,
            mineru_parse_method="auto",
            enable_image_processing=True,
            enable_table_processing=True,
            enable_equation_processing=True,
        )

        # 定义LLM模型函数 - 使用阿里云百炼
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return openai_complete_if_cache(
                "qwen-turbo",  # 使用千问模型
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )

        # 定义视觉模型函数 - 支持多模态
        def vision_model_func(
            prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs
        ):
            if image_data:
                return openai_complete_if_cache(
                    "qwen-vl-plus",  # 使用千问视觉模型
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt}
                        if system_prompt
                        else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{image_data}"
                                    },
                                },
                            ],
                        }
                        if image_data
                        else {"role": "user", "content": prompt},
                    ],
                    api_key=api_key,
                    base_url=base_url,
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

        # 定义embedding函数 - 使用阿里云百炼
        embedding_func = EmbeddingFunc(
            embedding_dim=1536,  # 阿里云百炼embedding维度
            max_token_size=8192,
            func=lambda texts: openai_embed(
                texts,
                model="text-embedding-v1",  # 使用阿里云百炼embedding模型
                api_key=api_key,
                base_url=base_url,
            ),
        )

        # 初始化RAGAnything
        rag = RAGAnything(
            config=config,
            llm_model_func=llm_model_func,
            vision_model_func=vision_model_func,
            embedding_func=embedding_func,
        )

        print("🏗️ RAGAnything初始化完成")

        # 处理文档
        print(f"\n📖 开始处理文档: {Path(file_path).name}")
        await rag.process_document_complete(
            file_path=file_path,
            output_dir=output_dir,
            parse_method="auto",
            device=device,  # 使用检测到的设备
            backend="pipeline"  # 使用pipeline后端获得更好的GPU支持
        )

        print("✅ 文档处理完成！")

        # 执行示例查询
        print("\n🔍 开始查询测试...")
        
        # 纯文本查询
        text_queries = [
            "这个文档的主要内容是什么？",
            "文档中讨论了哪些关键主题？",
        ]

        for i, query in enumerate(text_queries, 1):
            print(f"\n❓ 文本查询 {i}: {query}")
            try:
                result = await rag.aquery(query, mode="hybrid")
                print(f"📝 回答 {i}: {result}")
            except Exception as e:
                print(f"❌ 查询 {i} 失败: {e}")

        # 多模态查询示例
        print(f"\n🔍 多模态查询测试...")
        try:
            multimodal_result = await rag.aquery_with_multimodal(
                "分析这个性能数据表格，与文档中的相关内容进行对比",
                multimodal_content=[{
                    "type": "table",
                    "table_data": """方法,准确率,处理时间
                                    RAGAnything,95.2%,120ms
                                    传统RAG,87.3%,180ms
                                    基线方法,82.1%,200ms"""
                }]
            )
            print(f"📊 多模态分析结果: {multimodal_result}")
        except Exception as e:
            print(f"⚠️ 多模态查询跳过: {e}")

        print(f"\n🎉 所有测试完成！")
        print(f"📁 结果保存在: {output_dir}")

    except Exception as e:
        logger.error(f"处理失败: {e}")
        import traceback
        print(f"❌ 错误详情:\n{traceback.format_exc()}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RAGAnything Windows版本 - 阿里云百炼API")
    parser.add_argument("file_path", help="要处理的文档路径")
    parser.add_argument(
        "--working_dir", "-w", default="./rag_storage", help="工作目录路径"
    )
    parser.add_argument(
        "--output", "-o", default="./output", help="输出目录路径"
    )
    parser.add_argument(
        "--api-key",
        default=os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY"),
        help="阿里云百炼API密钥（默认从环境变量获取）",
    )
    parser.add_argument(
        "--base-url", 
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        help="API端点（默认阿里云百炼）"
    )
    parser.add_argument(
        "--device",
        choices=["auto", "cpu", "cuda"],
        default="auto",
        help="设备选择（auto=自动检测, cpu, cuda）"
    )

    args = parser.parse_args()

    # 检查API密钥
    if not args.api_key:
        print("❌ 错误: 需要阿里云百炼API密钥")
        print("请设置环境变量 DASHSCOPE_API_KEY 或使用 --api-key 参数")
        print("示例: set DASHSCOPE_API_KEY=sk-your-api-key")
        return

    # 验证API密钥格式
    if not args.api_key.startswith('sk-'):
        print("⚠️ 警告: API密钥格式可能不正确，应以'sk-'开头")

    # 检查文档文件
    if not os.path.exists(args.file_path):
        print(f"❌ 错误: 文档文件不存在: {args.file_path}")
        return

    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)

    # 确定设备
    device = None if args.device == "auto" else args.device
    if device:
        logger.info(f"使用指定设备: {device}")

    print("🚀 RAGAnything Windows版本 - 阿里云百炼API")
    print("=" * 55)
    print(f"📄 文档: {args.file_path}")
    print(f"🌐 API端点: {args.base_url}")
    print(f"🔑 API密钥: {args.api_key[:8]}...{args.api_key[-4:]}")
    print("=" * 55)

    # 运行处理
    asyncio.run(
        process_with_rag(
            args.file_path, args.output, args.api_key, args.base_url, args.working_dir, device
        )
    )

if __name__ == "__main__":
    # 配置日志
    configure_logging()

    print("🖥️ RAGAnything Windows版本")
    print("基于阿里云百炼API的多模态RAG系统")
    print("支持CUDA GPU加速")
    print("=" * 50)

    main()
