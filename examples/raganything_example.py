#!/usr/bin/env python
"""
Example script demonstrating the integration of MinerU parser with RAGAnything
Optimized for Alibaba Cloud DashScope (Qwen) API

This example shows how to:
1. Process documents with RAGAnything using MinerU parser
2. Perform pure text queries using aquery() method
3. Perform multimodal queries with specific multimodal content using aquery_with_multimodal() method
4. Handle different types of multimodal content (tables, equations) in queries
5. Use Alibaba Cloud DashScope API with OpenAI-compatible interface
"""

import os
import argparse
import asyncio
import logging
import logging.config
import tempfile
from pathlib import Path

# Add project root directory to Python path
import sys

sys.path.append(str(Path(__file__).parent.parent))

# 修复临时目录问题
def fix_temp_directory():
    """修复临时目录权限问题"""
    try:
        # 创建项目专用临时目录
        project_temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(project_temp_dir, exist_ok=True)

        # 设置临时目录环境变量
        os.environ["TMPDIR"] = project_temp_dir
        os.environ["TMP"] = project_temp_dir
        os.environ["TEMP"] = project_temp_dir

        # 验证临时目录是否可用
        test_file = tempfile.NamedTemporaryFile(delete=True)
        test_file.close()

        print(f"✅ 临时目录已设置: {project_temp_dir}")
        return True
    except Exception as e:
        print(f"⚠️ 临时目录设置失败: {e}")
        return False

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc, logger, set_verbose_debug
from raganything import RAGAnything, RAGAnythingConfig

# GPU加速检测和配置
def detect_and_configure_device():
    """检测并配置最佳设备"""
    try:
        import torch

        if torch.backends.mps.is_available():
            device = "mps"
            print(f"🚀 检测到MPS支持，使用GPU加速: {device}")
            # 设置MPS环境变量
            os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
            return device
        elif torch.cuda.is_available():
            device = "cuda"
            print(f"🚀 检测到CUDA支持，使用GPU加速: {device}")
            return device
        else:
            device = "cpu"
            print(f"⚠️ 未检测到GPU支持，使用CPU: {device}")
            return device
    except ImportError:
        print("⚠️ PyTorch未安装，使用CPU")
        return "cpu"


def configure_logging():
    """Configure logging for the application"""
    # Get log directory path from environment variable or use current directory
    log_dir = os.getenv("LOG_DIR", os.getcwd())
    log_file_path = os.path.abspath(os.path.join(log_dir, "raganything_example.log"))

    print(f"\nRAGAnything example log file: {log_file_path}\n")
    os.makedirs(os.path.dirname(log_dir), exist_ok=True)

    # Get log file max size and backup count from environment variables
    log_max_bytes = int(os.getenv("LOG_MAX_BYTES", 10485760))  # Default 10MB
    log_backup_count = int(os.getenv("LOG_BACKUP_COUNT", 5))  # Default 5 backups

    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(levelname)s: %(message)s",
                },
                "detailed": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "console": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr",
                },
                "file": {
                    "formatter": "detailed",
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": log_file_path,
                    "maxBytes": log_max_bytes,
                    "backupCount": log_backup_count,
                    "encoding": "utf-8",
                },
            },
            "loggers": {
                "lightrag": {
                    "handlers": ["console", "file"],
                    "level": "INFO",
                    "propagate": False,
                },
            },
        }
    )

    # Set the logger level to INFO
    logger.setLevel(logging.INFO)
    # Enable verbose debug if needed
    set_verbose_debug(os.getenv("VERBOSE", "false").lower() == "true")


async def process_with_rag(
    file_path: str,
    output_dir: str,
    api_key: str,
    base_url: str = None,
    working_dir: str = None,
    device: str = None,
):
    """
    Process document with RAGAnything

    Args:
        file_path: Path to the document
        output_dir: Output directory for RAG results
        api_key: DashScope API key
        base_url: Optional base URL for API (defaults to DashScope compatible endpoint)
        working_dir: Working directory for RAG storage
        device: Device for inference (auto-detected if not provided)
    """
    try:
        # Set default base_url for DashScope if not provided
        if not base_url:
            base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
            logger.info(f"Using default DashScope compatible endpoint: {base_url}")

        # 检测并配置设备
        if not device:
            device = detect_and_configure_device()
        else:
            print(f"🔧 使用指定设备: {device}")

        # Create RAGAnything configuration
        config = RAGAnythingConfig(
            working_dir=working_dir or "./rag_storage",
            mineru_parse_method="ocr",  # Use OCR mode for better text extraction
            enable_image_processing=True,
            enable_table_processing=True,
            enable_equation_processing=True,
        )

        # Define LLM model function using Qwen model
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return openai_complete_if_cache(
                "qwen-turbo",  # Use Qwen model instead of GPT
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )

        # Define vision model function for image processing
        def vision_model_func(
            prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs
        ):
            if image_data:
                return openai_complete_if_cache(
                    "qwen-vl-plus",  # Use Qwen multimodal model
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt}
                        if system_prompt
                        else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{image_data}"
                                    },
                                },
                            ],
                        }
                        if image_data
                        else {"role": "user", "content": prompt},
                    ],
                    api_key=api_key,
                    base_url=base_url,
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

        # Define embedding function using DashScope embedding model with batch size limit
        async def safe_embedding_func(texts):
            """Safe embedding function with batch size limit for DashScope API"""
            if isinstance(texts, str):
                texts = [texts]

            # DashScope API限制：批处理大小不能超过25
            batch_size = 20  # 设置为20以确保安全
            results = []

            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_result = await openai_embed(
                    batch,
                    model="text-embedding-v1",
                    api_key=api_key,
                    base_url=base_url,
                )
                results.extend(batch_result)

            return results

        embedding_func = EmbeddingFunc(
            embedding_dim=1536,  # Qwen embedding dimension
            max_token_size=8192,
            func=safe_embedding_func,
        )

        # Initialize RAGAnything with new dataclass structure
        rag = RAGAnything(
            config=config,
            llm_model_func=llm_model_func,
            vision_model_func=vision_model_func,
            embedding_func=embedding_func,
        )

        # Process document with device configuration
        await rag.process_document_complete(
            file_path=file_path,
            output_dir=output_dir,
            parse_method="auto",
            device=device,  # 使用检测到的设备
            backend="pipeline"  # 使用pipeline后端以获得更好的GPU支持
        )

        # Example queries - demonstrating different query approaches
        logger.info("\nQuerying processed document:")

        # 1. Pure text queries using aquery()
        text_queries = [
            "What is the main content of the document?",
            "What are the key topics discussed?",
        ]

        for query in text_queries:
            logger.info(f"\n[Text Query]: {query}")
            result = await rag.aquery(query, mode="hybrid")
            logger.info(f"Answer: {result}")

        # 2. Multimodal query with specific multimodal content using aquery_with_multimodal()
        logger.info(
            "\n[Multimodal Query]: Analyzing performance data in context of document"
        )
        multimodal_result = await rag.aquery_with_multimodal(
            "Compare this performance data with any similar results mentioned in the document",
            multimodal_content=[
                {
                    "type": "table",
                    "table_data": """Method,Accuracy,Processing_Time
                                RAGAnything,95.2%,120ms
                                Traditional_RAG,87.3%,180ms
                                Baseline,82.1%,200ms""",
                    "table_caption": "Performance comparison results",
                }
            ],
            mode="hybrid",
        )
        logger.info(f"Answer: {multimodal_result}")

        # 3. Another multimodal query with equation content
        logger.info("\n[Multimodal Query]: Mathematical formula analysis")
        equation_result = await rag.aquery_with_multimodal(
            "Explain this formula and relate it to any mathematical concepts in the document",
            multimodal_content=[
                {
                    "type": "equation",
                    "latex": "F1 = 2 \\cdot \\frac{precision \\cdot recall}{precision + recall}",
                    "equation_caption": "F1-score calculation formula",
                }
            ],
            mode="hybrid",
        )
        logger.info(f"Answer: {equation_result}")

    except Exception as e:
        logger.error(f"Error processing with RAG: {str(e)}")
        import traceback

        logger.error(traceback.format_exc())


async def process_with_rag_and_qa(
    file_path: str,
    output_dir: str,
    api_key: str,
    base_url: str = None,
    working_dir: str = None,
    device: str = None,
):
    """Process document with RAG and start interactive Q&A mode"""
    try:
        # First process the document normally and get the RAG instance
        rag = await process_with_rag_return_instance(file_path, output_dir, api_key, base_url, working_dir, device)

        # Then start Q&A mode with the same RAG instance
        print("\n" + "="*50)
        print("🎯 启动交互式问答模式")
        print("="*50)

        # Import and start Q&A mode with the existing RAG instance
        from raganything.qa_mode import start_qa_mode
        await start_qa_mode(rag)

    except Exception as e:
        logger.error(f"Q&A模式启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())


async def process_with_rag_return_instance(
    file_path: str,
    output_dir: str,
    api_key: str,
    base_url: str = None,
    working_dir: str = None,
    device: str = None,
):
    """Process document with RAG and return the RAG instance"""
    try:
        # Set default API endpoint
        if not base_url:
            base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
            logger.info(f"使用默认阿里云百炼端点: {base_url}")

        # Detect device if needed
        if not device or device == "auto":
            device = detect_and_configure_device()

        # Set default working directory
        if not working_dir:
            working_dir = "./rag_storage"

        # Create configuration
        config = RAGAnythingConfig(
            working_dir=working_dir,
            mineru_parse_method="txt",  # Use txt mode to avoid model download
            enable_image_processing=False,
            enable_table_processing=False,
            enable_equation_processing=False,
        )

        # Define model functions
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return openai_complete_if_cache(
                "qwen-turbo",
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )

        def vision_model_func(prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs):
            if image_data:
                return openai_complete_if_cache(
                    "qwen-vl-plus",
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt} if system_prompt else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}},
                            ],
                        } if image_data else {"role": "user", "content": prompt},
                    ],
                    api_key=api_key,
                    base_url=base_url,
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

        # Define safe embedding function with batch size limit
        async def safe_embedding_func_qa(texts):
            """Safe embedding function with batch size limit for DashScope API"""
            if isinstance(texts, str):
                texts = [texts]

            # DashScope API限制：批处理大小不能超过25
            batch_size = 20  # 设置为20以确保安全
            results = []

            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_result = await openai_embed(
                    batch,
                    model="text-embedding-v1",
                    api_key=api_key,
                    base_url=base_url,
                )
                results.extend(batch_result)

            return results

        embedding_func = EmbeddingFunc(
            embedding_dim=1536,
            max_token_size=8192,
            func=safe_embedding_func_qa,
        )

        # Initialize RAGAnything
        rag = RAGAnything(
            config=config,
            llm_model_func=llm_model_func,
            vision_model_func=vision_model_func,
            embedding_func=embedding_func,
        )

        logger.info("RAGAnything系统初始化完成")

        # Process document
        await rag.process_document_complete(
            file_path=file_path,
            output_dir=output_dir,
            parse_method="auto",
            device=device,
            backend="pipeline"
        )

        logger.info("文档处理完成")

        # Return the RAG instance with processed data
        return rag

    except Exception as e:
        logger.error(f"处理失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


async def initialize_rag_for_qa(api_key: str, base_url: str, working_dir: str, device: str):
    """Initialize RAG system for Q&A mode"""
    # Set default API endpoint
    if not base_url:
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"

    # Detect device if needed
    if not device or device == "auto":
        device = detect_and_configure_device()

    # Create configuration
    config = RAGAnythingConfig(
        working_dir=working_dir,
        mineru_parse_method="auto",
        enable_image_processing=True,
        enable_table_processing=True,
        enable_equation_processing=True,
    )

    # Define model functions
    def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
        return openai_complete_if_cache(
            "qwen-turbo",
            prompt,
            system_prompt=system_prompt,
            history_messages=history_messages,
            api_key=api_key,
            base_url=base_url,
            **kwargs,
        )

    def vision_model_func(prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs):
        if image_data:
            return openai_complete_if_cache(
                "qwen-vl-plus",
                "",
                system_prompt=None,
                history_messages=[],
                messages=[
                    {"role": "system", "content": system_prompt} if system_prompt else None,
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}},
                        ],
                    } if image_data else {"role": "user", "content": prompt},
                ],
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )
        else:
            return llm_model_func(prompt, system_prompt, history_messages, **kwargs)

    # Define safe embedding function with batch size limit
    async def safe_embedding_func_main(texts):
        """Safe embedding function with batch size limit for DashScope API"""
        if isinstance(texts, str):
            texts = [texts]

        # DashScope API限制：批处理大小不能超过25
        batch_size = 20  # 设置为20以确保安全
        results = []

        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_result = await openai_embed(
                batch,
                model="text-embedding-v1",
                api_key=api_key,
                base_url=base_url,
            )
            results.extend(batch_result)

        return results

    embedding_func = EmbeddingFunc(
        embedding_dim=1536,
        max_token_size=8192,
        func=safe_embedding_func_main,
    )

    # Initialize RAGAnything
    rag = RAGAnything(
        config=config,
        llm_model_func=llm_model_func,
        vision_model_func=vision_model_func,
        embedding_func=embedding_func,
    )

    return rag


def main():
    """Main function to run the example"""
    # 首先修复临时目录问题
    if not fix_temp_directory():
        print("❌ 临时目录修复失败，程序可能无法正常运行")

    parser = argparse.ArgumentParser(description="RAGAnything with DashScope API Example")
    parser.add_argument("file_path", help="Path to the document to process")
    parser.add_argument(
        "--working_dir", "-w", default="./rag_storage", help="Working directory path"
    )
    parser.add_argument(
        "--output", "-o", default="./output", help="Output directory path"
    )
    parser.add_argument(
        "--api-key",
        default=os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY"),
        help="DashScope API key (defaults to DASHSCOPE_API_KEY or OPENAI_API_KEY env var)",
    )
    parser.add_argument(
        "--base-url",
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        help="Base URL for API (defaults to DashScope compatible endpoint)"
    )
    parser.add_argument(
        "--device",
        choices=["auto", "cpu", "cuda", "mps"],
        default="auto",
        help="Device for inference (auto=auto-detect, cpu, cuda, mps)"
    )
    parser.add_argument(
        "--qa-mode", "-q",
        action="store_true",
        help="Start interactive Q&A mode after processing the document"
    )

    args = parser.parse_args()

    # Check if API key is provided
    if not args.api_key:
        logger.error("Error: DashScope API key is required")
        logger.error("Set DASHSCOPE_API_KEY or OPENAI_API_KEY environment variable or use --api-key option")
        logger.error("Example: export DASHSCOPE_API_KEY=sk-your-api-key")
        return

    # Validate API key format
    if not args.api_key.startswith('sk-'):
        logger.warning("Warning: API key should start with 'sk-'. Please verify your key format.")

    # Create output directory if specified
    if args.output:
        os.makedirs(args.output, exist_ok=True)

    logger.info(f"Using API endpoint: {args.base_url}")
    logger.info(f"API key: {args.api_key[:8]}...{args.api_key[-4:]}")

    # 确定设备
    device = None if args.device == "auto" else args.device
    if device:
        logger.info(f"Using specified device: {device}")

    # Process with RAG
    if args.qa_mode:
        # Import Q&A mode functionality
        try:
            from raganything.qa_mode import start_qa_mode
            asyncio.run(
                process_with_rag_and_qa(
                    args.file_path, args.output, args.api_key, args.base_url, args.working_dir, device
                )
            )
        except ImportError:
            logger.error("Q&A mode requires the qa_mode module. Please ensure it's available.")
            asyncio.run(
                process_with_rag(
                    args.file_path, args.output, args.api_key, args.base_url, args.working_dir, device
                )
            )
    else:
        asyncio.run(
            process_with_rag(
                args.file_path, args.output, args.api_key, args.base_url, args.working_dir, device
            )
        )


if __name__ == "__main__":
    # Configure logging first
    configure_logging()

    print("RAGAnything with DashScope API Example")
    print("=" * 45)
    print("Processing document with multimodal RAG pipeline")
    print("Using Alibaba Cloud DashScope (Qwen) API")
    print("=" * 45)

    main()
