<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-Anything 流程与模型详细分析</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #007AFF;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #007AFF;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #5856D6;
            font-size: 1.5em;
            margin: 20px 0 15px 0;
        }

        .diagram-container {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .process-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .process-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #007AFF;
            transition: transform 0.3s ease;
            position: relative;
        }

        .process-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .process-number {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #007AFF;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .process-card h4 {
            color: #007AFF;
            font-size: 1.3em;
            margin-bottom: 15px;
            padding-right: 50px;
        }

        .model-info {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 500;
        }

        .input-output {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }

        .input-box, .output-box {
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .input-box {
            background: linear-gradient(45deg, #FFE082, #FFCC02);
            border-color: #FF9500;
        }

        .output-box {
            background: linear-gradient(45deg, #A8E6CF, #7FCDCD);
            border-color: #34C759;
        }

        .input-box h5, .output-box h5 {
            margin-bottom: 8px;
            font-weight: bold;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .feature-list li:before {
            content: "▶ ";
            color: #007AFF;
            font-weight: bold;
        }

        .algorithm-tag {
            display: inline-block;
            background: linear-gradient(45deg, #FF9500, #FF6B35);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin: 2px;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 122, 255, 0.9);
            padding: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .nav-link {
            display: block;
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 10px;
            margin: 5px 0;
            transition: background 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .highlight-box {
            background: linear-gradient(45deg, #FFE082, #FFCC02);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #FF9500;
        }

        .highlight-box h4 {
            color: #E65100;
            margin-bottom: 10px;
        }

        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border-left: 4px solid #007AFF;
        }

        .step-icon {
            background: #007AFF;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .step-content h5 {
            color: #007AFF;
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .process-grid {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
            }
            
            .input-output {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#overview" class="nav-link">🏗️ 总体流程</a>
        <a href="#stages" class="nav-link">📋 七大阶段</a>
        <a href="#models" class="nav-link">🤖 模型详解</a>
        <a href="#algorithms" class="nav-link">⚙️ 核心算法</a>
        <a href="#dataflow" class="nav-link">🔄 数据流向</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔍 RAG-Anything 流程与模型分析</h1>
            <p>深度解析10+个AI模型的协同工作机制</p>
            <p>从文档解析到智能问答的完整技术流程</p>
        </div>

        <section id="overview" class="section">
            <h2>🏗️ 系统总体流程概览</h2>
            
            <div class="highlight-box">
                <h4>🎯 核心设计理念</h4>
                <p>RAG-Anything采用七阶段流程设计，每个阶段都有专门的AI模型负责处理，形成了一个完整的多模态知识处理管道。从PDF文档输入到最终的智能问答输出，整个过程实现了文档→知识→智能的完整转换。</p>
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
                    graph TB
                        subgraph "阶段1: 文档解析"
                            A[📄 PDF/Office文档] --> B[MinerU 2.0<br/>解析引擎]
                            B --> C[结构化内容列表]
                        end
                        
                        subgraph "阶段2: 内容分离"
                            C --> D{内容类型识别}
                            D -->|文本| E[📝 纯文本内容]
                            D -->|图像| F[🖼️ 图像内容]
                            D -->|表格| G[📊 表格内容]
                            D -->|公式| H[🔢 公式内容]
                        end
                        
                        subgraph "阶段3: 多模态处理"
                            F --> I[ImageModalProcessor<br/>Qwen-VL-Plus]
                            G --> J[TableModalProcessor<br/>Qwen-turbo]
                            H --> K[EquationModalProcessor<br/>Qwen-turbo]
                        end
                        
                        subgraph "阶段4: 知识抽取"
                            E --> L[LightRAG引擎<br/>Qwen-turbo]
                            I --> L
                            J --> L
                            K --> L
                            L --> M[实体识别 NER]
                            L --> N[关系抽取 RE]
                        end
                        
                        subgraph "阶段5: 图谱构建"
                            M --> O[知识图谱<br/>NetworkX]
                            N --> O
                            O --> P[图节点 Entities]
                            O --> Q[图边 Relations]
                        end
                        
                        subgraph "阶段6: 向量化存储"
                            P --> R[text-embedding-v1<br/>1536维向量]
                            Q --> R
                            E --> R
                            R --> S[向量数据库<br/>Vector Store]
                        end
                        
                        subgraph "阶段7: 智能问答"
                            T[🎤 用户查询] --> U[Whisper Base<br/>语音识别]
                            U --> V[查询处理]
                            V --> W[向量检索<br/>Similarity Search]
                            S --> W
                            W --> X[LLM推理<br/>Qwen-turbo]
                            O --> X
                            X --> Y[Edge-TTS<br/>语音合成]
                            Y --> Z[🎭 数字人展示<br/>MediaPipe]
                        end
                        
                        style A fill:#e1f5fe
                        style B fill:#f3e5f5
                        style L fill:#e8f5e8
                        style O fill:#fff3e0
                        style R fill:#fce4ec
                        style X fill:#f1f8e9
                        style Z fill:#e0f2f1
                </div>
            </div>
        </section>

        <section id="stages" class="section">
            <h2>📋 七大处理阶段详解</h2>
            
            <div class="process-grid">
                <div class="process-card">
                    <div class="process-number">1</div>
                    <h4>📄 文档解析阶段</h4>
                    <div class="model-info">🤖 核心模型: MinerU 2.0 解析引擎</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>PDF文档</li>
                                <li>Office文档</li>
                                <li>图像文件</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>结构化内容列表</li>
                                <li>页面布局信息</li>
                                <li>元数据信息</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">OCR文字识别</div>
                    <div class="algorithm-tag">布局分析</div>
                    <div class="algorithm-tag">表格检测</div>
                    <div class="algorithm-tag">公式识别</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 将非结构化文档转换为结构化数据，为后续处理提供标准化输入。</p>
                </div>

                <div class="process-card">
                    <div class="process-number">2</div>
                    <h4>🔍 内容分离阶段</h4>
                    <div class="model-info">🤖 核心模型: 内容类型分类器</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>结构化内容列表</li>
                                <li>内容类型标识</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>纯文本内容</li>
                                <li>多模态内容</li>
                                <li>分类标签</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">内容类型识别</div>
                    <div class="algorithm-tag">数据分流</div>
                    <div class="algorithm-tag">格式标准化</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 根据内容类型将数据分流到不同的处理管道，实现专业化处理。</p>
                </div>

                <div class="process-card">
                    <div class="process-number">3</div>
                    <h4>🎨 多模态处理阶段</h4>
                    <div class="model-info">🤖 核心模型: Qwen-VL-Plus + Qwen-turbo</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>图像内容</li>
                                <li>表格数据</li>
                                <li>数学公式</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>图像描述文本</li>
                                <li>表格语义理解</li>
                                <li>公式语义解析</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">视觉理解</div>
                    <div class="algorithm-tag">OCR识别</div>
                    <div class="algorithm-tag">结构分析</div>
                    <div class="algorithm-tag">语义解析</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 将非文本内容转换为文本描述，实现多模态内容的统一表示。</p>
                </div>

                <div class="process-card">
                    <div class="process-number">4</div>
                    <h4>🧠 知识抽取阶段</h4>
                    <div class="model-info">🤖 核心模型: LightRAG + Qwen-turbo</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>文本内容</li>
                                <li>多模态描述</li>
                                <li>上下文信息</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>实体列表</li>
                                <li>关系三元组</li>
                                <li>属性信息</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">命名实体识别 NER</div>
                    <div class="algorithm-tag">关系抽取 RE</div>
                    <div class="algorithm-tag">文本分块</div>
                    <div class="algorithm-tag">语义理解</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 从文本中抽取结构化知识，为知识图谱构建提供原料。</p>
                </div>

                <div class="process-card">
                    <div class="process-number">5</div>
                    <h4>🕸️ 图谱构建阶段</h4>
                    <div class="model-info">🤖 核心模型: NetworkX + 图算法</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>实体列表</li>
                                <li>关系三元组</li>
                                <li>属性信息</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>知识图谱</li>
                                <li>图节点</li>
                                <li>图边关系</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">图构建算法</div>
                    <div class="algorithm-tag">实体链接</div>
                    <div class="algorithm-tag">关系建模</div>
                    <div class="algorithm-tag">图优化</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 构建结构化知识图谱，建立实体间的语义关联。</p>
                </div>

                <div class="process-card">
                    <div class="process-number">6</div>
                    <h4>🔢 向量化存储阶段</h4>
                    <div class="model-info">🤖 核心模型: text-embedding-v1</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>文本内容</li>
                                <li>实体描述</li>
                                <li>关系描述</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>1536维向量</li>
                                <li>向量索引</li>
                                <li>相似度矩阵</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">文本嵌入</div>
                    <div class="algorithm-tag">向量索引</div>
                    <div class="algorithm-tag">相似度计算</div>
                    <div class="algorithm-tag">批量处理</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 将文本转换为高维向量，支持语义相似度检索。</p>
                </div>

                <div class="process-card">
                    <div class="process-number">7</div>
                    <h4>💬 智能问答阶段</h4>
                    <div class="model-info">🤖 核心模型: Whisper + Qwen-turbo + Edge-TTS + MediaPipe</div>
                    
                    <div class="input-output">
                        <div class="input-box">
                            <h5>📥 输入</h5>
                            <ul class="feature-list">
                                <li>用户语音</li>
                                <li>文本查询</li>
                                <li>多模态查询</li>
                            </ul>
                        </div>
                        <div class="output-box">
                            <h5>📤 输出</h5>
                            <ul class="feature-list">
                                <li>智能回答</li>
                                <li>语音合成</li>
                                <li>数字人动画</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p><strong>核心算法:</strong></p>
                    <div class="algorithm-tag">语音识别</div>
                    <div class="algorithm-tag">向量检索</div>
                    <div class="algorithm-tag">LLM推理</div>
                    <div class="algorithm-tag">语音合成</div>
                    <div class="algorithm-tag">面部动画</div>
                    
                    <p style="margin-top: 15px;"><strong>作用:</strong> 提供完整的智能问答体验，包括语音交互和视觉反馈。</p>
                </div>
            </div>
        </section>

        <section id="models" class="section">
            <h2>🤖 核心AI模型详细解析</h2>
            
            <h3>🎯 模型分工与协作机制</h3>
            
            <div class="flow-step">
                <div class="step-icon">1</div>
                <div class="step-content">
                    <h5>MinerU 2.0 - 文档解析引擎</h5>
                    <p><strong>作用:</strong> PDF/Office文档的结构化解析，支持OCR、表格检测、公式识别</p>
                    <p><strong>技术:</strong> 深度学习 + 计算机视觉 + OCR技术栈</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">2</div>
                <div class="step-content">
                    <h5>Qwen-VL-Plus - 多模态视觉理解</h5>
                    <p><strong>作用:</strong> 图像内容理解、OCR识别、视觉问答</p>
                    <p><strong>技术:</strong> Vision Transformer + 多模态融合 + 阿里云百炼API</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">3</div>
                <div class="step-content">
                    <h5>Qwen-turbo - 大语言模型</h5>
                    <p><strong>作用:</strong> 文本理解、实体抽取、关系识别、智能问答生成</p>
                    <p><strong>技术:</strong> Transformer架构 + 预训练 + 微调优化</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">4</div>
                <div class="step-content">
                    <h5>text-embedding-v1 - 文本嵌入模型</h5>
                    <p><strong>作用:</strong> 文本向量化、语义相似度计算、向量检索</p>
                    <p><strong>技术:</strong> 1536维向量空间 + 余弦相似度 + 批量处理</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">5</div>
                <div class="step-content">
                    <h5>LightRAG - 知识图谱引擎</h5>
                    <p><strong>作用:</strong> 知识图谱构建、实体关系抽取、图算法优化</p>
                    <p><strong>技术:</strong> 图神经网络 + NetworkX + 实体链接算法</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">6</div>
                <div class="step-content">
                    <h5>Whisper Base - 语音识别</h5>
                    <p><strong>作用:</strong> 多语言语音识别、音频转文字</p>
                    <p><strong>技术:</strong> Transformer + 端到端训练 + 多语言支持</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">7</div>
                <div class="step-content">
                    <h5>Edge-TTS - 语音合成</h5>
                    <p><strong>作用:</strong> 高质量语音合成、多语言支持</p>
                    <p><strong>技术:</strong> 神经网络语音合成 + Microsoft技术栈</p>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-icon">8</div>
                <div class="step-content">
                    <h5>MediaPipe FaceMesh - 面部检测</h5>
                    <p><strong>作用:</strong> 468个面部关键点检测、实时面部追踪</p>
                    <p><strong>技术:</strong> CNN + 关键点检测 + Google MediaPipe框架</p>
                </div>
            </div>
        </section>

        <section id="algorithms" class="section">
            <h2>⚙️ 核心算法技术栈</h2>
            
            <div class="diagram-container">
                <h3>算法分层架构</h3>
                <div class="mermaid">
                    graph TB
                        subgraph "深度学习算法层"
                            A[Transformer架构<br/>注意力机制]
                            B[CNN卷积网络<br/>图像处理]
                            C[RNN循环网络<br/>序列建模]
                        end
                        
                        subgraph "自然语言处理算法"
                            D[命名实体识别 NER<br/>BiLSTM + CRF]
                            E[关系抽取 RE<br/>BERT + 分类器]
                            F[文本分块<br/>语义切分]
                        end
                        
                        subgraph "计算机视觉算法"
                            G[目标检测<br/>YOLO/R-CNN]
                            H[OCR识别<br/>CRNN + CTC]
                            I[关键点检测<br/>HRNet]
                        end
                        
                        subgraph "图算法"
                            J[图构建算法<br/>实体链接]
                            K[图遍历算法<br/>BFS/DFS]
                            L[社区检测<br/>Louvain算法]
                        end
                        
                        subgraph "向量算法"
                            M[文本嵌入<br/>Word2Vec/BERT]
                            N[相似度计算<br/>余弦相似度]
                            O[向量索引<br/>FAISS/Annoy]
                        end
                        
                        A --> D
                        A --> E
                        B --> G
                        B --> H
                        B --> I
                        C --> F
                        
                        D --> J
                        E --> J
                        F --> M
                        
                        G --> H
                        H --> I
                        
                        J --> K
                        K --> L
                        
                        M --> N
                        N --> O
                        
                        style A fill:#e3f2fd
                        style D fill:#e8f5e8
                        style G fill:#fff3e0
                        style J fill:#f3e5f5
                        style M fill:#fce4ec
                </div>
            </div>
        </section>

        <section id="dataflow" class="section">
            <h2>🔄 数据流向与处理管道</h2>
            
            <div class="highlight-box">
                <h4>🎯 数据流设计原则</h4>
                <p>采用管道式设计，每个阶段的输出作为下一阶段的输入，实现数据的流式处理。同时支持并行处理和异步操作，提高整体处理效率。</p>
            </div>
            
            <div class="diagram-container">
                <h3>完整数据流向图</h3>
                <div class="mermaid">
                    flowchart TD
                        A[📄 原始文档] --> B{文档类型}
                        B -->|PDF| C[MinerU PDF解析]
                        B -->|Office| D[MinerU Office解析]
                        B -->|图像| E[MinerU 图像解析]
                        
                        C --> F[结构化内容]
                        D --> F
                        E --> F
                        
                        F --> G{内容分流}
                        G -->|文本| H[文本处理管道]
                        G -->|图像| I[图像处理管道]
                        G -->|表格| J[表格处理管道]
                        G -->|公式| K[公式处理管道]
                        
                        H --> L[LightRAG处理]
                        I --> M[Qwen-VL-Plus分析]
                        J --> N[表格结构分析]
                        K --> O[公式语义解析]
                        
                        L --> P[实体抽取]
                        M --> P
                        N --> P
                        O --> P
                        
                        P --> Q[关系抽取]
                        Q --> R[知识图谱构建]
                        
                        R --> S[向量化处理]
                        H --> S
                        
                        S --> T[向量数据库]
                        R --> U[图数据库]
                        
                        V[🎤 用户查询] --> W[Whisper识别]
                        W --> X[查询处理]
                        X --> Y[混合检索]
                        
                        T --> Y
                        U --> Y
                        
                        Y --> Z[LLM推理]
                        Z --> AA[回答生成]
                        AA --> BB[Edge-TTS合成]
                        BB --> CC[🎭 数字人展示]
                        
                        style A fill:#e1f5fe
                        style F fill:#f3e5f5
                        style P fill:#e8f5e8
                        style R fill:#fff3e0
                        style Y fill:#fce4ec
                        style CC fill:#f1f8e9
                </div>
            </div>
        </section>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 添加页面加载动画
        window.addEventListener('load', function() {
            document.querySelectorAll('.process-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
