# RAGAnything 运行命令大全（含问答模式）

## 🚀 快速开始命令

### 📋 环境准备
```bash
# 1. 克隆项目
git clone https://github.com/your-repo/RAG-Anything.git
cd RAG-Anything

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt
pip install openai dashscope

# 5. 配置API密钥
echo "DASHSCOPE_API_KEY=sk-your-api-key-here" > .env
echo "OPENAI_API_KEY=sk-your-api-key-here" >> .env
echo "OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1" >> .env
```

## 🎯 问答模式运行命令

### 方法1：使用启动脚本（推荐）

#### Windows用户
```bash
# 处理新文档并启动问答模式
run_qa_mode.bat "documents/sample.pdf"
run_qa_mode.bat "documents/research_paper.pdf"
run_qa_mode.bat "C:\Users\<USER>\Documents\report.pdf"

# 使用现有知识库启动问答
run_qa_mode.bat --existing

# 加载之前保存的会话
run_qa_mode.bat --session "my_session.json"
run_qa_mode.bat --session "research_session_20241218.json"

# 交互式选择模式
run_qa_mode.bat
```

#### Linux/Mac用户
```bash
# 处理新文档并启动问答模式
./run_qa_mode.sh "documents/sample.pdf"
./run_qa_mode.sh "documents/research_paper.pdf"
./run_qa_mode.sh "/home/<USER>/documents/report.pdf"

# 使用现有知识库启动问答
./run_qa_mode.sh --existing

# 加载之前保存的会话
./run_qa_mode.sh --session "my_session.json"
./run_qa_mode.sh --session "research_session_20241218.json"

# 交互式选择模式
./run_qa_mode.sh
```

### 方法2：直接使用Python脚本

#### 基础命令
```bash
# 处理新文档并启动问答
python examples/qa_mode_example.py "documents/sample.pdf" --api-key sk-your-api-key-here

# 使用现有知识库
python examples/qa_mode_example.py --working-dir ./rag_storage --api-key sk-your-api-key-here

# 加载会话文件
python examples/qa_mode_example.py --working-dir ./rag_storage --session "session.json" --api-key sk-your-api-key-here
```

#### 完整参数命令
```bash
# 完整参数示例
python examples/qa_mode_example.py \
    "documents/research_paper.pdf" \
    --working-dir "./my_rag_storage" \
    --output "./my_output" \
    --api-key "sk-your-api-key-here" \
    --base-url "https://dashscope.aliyuncs.com/compatible-mode/v1" \
    --device "auto" \
    --verbose

# 指定GPU设备
python examples/qa_mode_example.py "document.pdf" --device cuda --api-key sk-your-api-key-here
python examples/qa_mode_example.py "document.pdf" --device mps --api-key sk-your-api-key-here

# 使用环境变量（推荐）
export DASHSCOPE_API_KEY=sk-your-api-key-here
python examples/qa_mode_example.py "document.pdf"
```

### 方法3：集成到现有脚本

#### 在原有示例脚本中启用问答模式
```bash
# 处理文档后自动启动问答模式
python examples/raganything_example.py "document.pdf" --qa-mode --api-key sk-your-api-key-here

# 使用MPS GPU加速 + 问答模式
python examples/raganything_example.py "document.pdf" --device mps --qa-mode --api-key sk-your-api-key-here

# 使用CUDA GPU加速 + 问答模式
python examples/raganything_example.py "document.pdf" --device cuda --qa-mode --api-key sk-your-api-key-here
```

## 📄 不同文档类型的运行命令

### PDF文档
```bash
# 学术论文
./run_qa_mode.sh "papers/machine_learning_survey.pdf"

# 技术文档
./run_qa_mode.sh "docs/api_documentation.pdf"

# 报告文档
./run_qa_mode.sh "reports/quarterly_report.pdf"
```

### Office文档
```bash
# Word文档
./run_qa_mode.sh "documents/project_proposal.docx"

# PowerPoint演示文稿
./run_qa_mode.sh "presentations/product_demo.pptx"

# Excel表格
./run_qa_mode.sh "data/sales_analysis.xlsx"
```

### 图像文件
```bash
# 图表图像
./run_qa_mode.sh "images/performance_chart.png"

# 扫描文档
./run_qa_mode.sh "scans/contract_scan.jpg"
```

## 🎨 多模态查询示例命令

### 启动后的交互命令
```bash
# 启动问答模式
./run_qa_mode.sh "document.pdf"

# 在问答界面中的操作：
# 1. 基础查询
这个文档的主要内容是什么？

# 2. 多模态查询（系统会自动检测）
分析这个销售数据表格
请解释这个性能图表
这个公式的含义是什么？

# 3. 使用命令
/help          # 显示帮助
/mode          # 切换查询模式
/history       # 查看对话历史
/stats         # 查看统计信息
/save          # 保存会话
/exit          # 退出
```

## 🔧 高级配置命令

### 自定义工作目录
```bash
# 指定自定义工作目录
python examples/qa_mode_example.py "document.pdf" \
    --working-dir "./project_knowledge_base" \
    --api-key sk-your-api-key-here

# 多项目管理
python examples/qa_mode_example.py "project_a_doc.pdf" \
    --working-dir "./project_a_kb" \
    --api-key sk-your-api-key-here

python examples/qa_mode_example.py "project_b_doc.pdf" \
    --working-dir "./project_b_kb" \
    --api-key sk-your-api-key-here
```

### 性能优化命令
```bash
# 启用详细日志
python examples/qa_mode_example.py "document.pdf" --verbose --api-key sk-your-api-key-here

# 指定输出目录
python examples/qa_mode_example.py "document.pdf" \
    --output "./custom_output" \
    --api-key sk-your-api-key-here

# 使用自定义API端点
python examples/qa_mode_example.py "document.pdf" \
    --base-url "https://your-custom-api-endpoint.com/v1" \
    --api-key sk-your-api-key-here
```

## 📊 批量处理命令

### 处理多个文档
```bash
# 方法1：逐个处理并保存会话
./run_qa_mode.sh "doc1.pdf"
# 在问答模式中使用 /save 保存为 doc1_session.json

./run_qa_mode.sh "doc2.pdf" 
# 保存为 doc2_session.json

# 方法2：使用脚本批量处理
for file in documents/*.pdf; do
    echo "Processing $file"
    python examples/qa_mode_example.py "$file" --api-key sk-your-api-key-here
done
```

### 会话管理命令
```bash
# 保存当前会话
# 在问答模式中输入：
/save my_research_session.json

# 加载之前的会话
./run_qa_mode.sh --session "my_research_session.json"

# 继续之前的对话
python examples/qa_mode_example.py \
    --working-dir "./rag_storage" \
    --session "my_research_session.json" \
    --api-key sk-your-api-key-here
```

## 🧪 测试和演示命令

### 功能测试
```bash
# 运行功能测试
python test_qa_mode.py

# 运行演示脚本
python demo_qa_mode.py

# 测试特定功能
python -c "
import asyncio
from raganything.qa_mode import QASession
session = QASession('test')
print('QA Session created successfully')
"
```

### 性能测试
```bash
# 测试不同查询模式的性能
python examples/qa_mode_example.py "document.pdf" --api-key sk-your-api-key-here
# 在问答模式中：
/mode
# 选择不同模式测试性能差异
```

## 🔍 故障排除命令

### 诊断命令
```bash
# 检查环境配置
python -c "
import os
print('API Key:', os.getenv('DASHSCOPE_API_KEY', 'Not set'))
print('Base URL:', os.getenv('OPENAI_BASE_URL', 'Not set'))
"

# 检查依赖
python -c "
try:
    import torch
    print('PyTorch:', torch.__version__)
    print('CUDA available:', torch.cuda.is_available())
    print('MPS available:', torch.backends.mps.is_available())
except ImportError:
    print('PyTorch not installed')

try:
    import openai
    print('OpenAI library: OK')
except ImportError:
    print('OpenAI library not installed')
"

# 检查API连接
python -c "
import requests
import os
api_key = os.getenv('DASHSCOPE_API_KEY')
if api_key:
    headers = {'Authorization': f'Bearer {api_key}'}
    try:
        response = requests.get('https://dashscope.aliyuncs.com', headers=headers, timeout=5)
        print('API endpoint reachable')
    except:
        print('API endpoint not reachable')
else:
    print('API key not set')
"
```

### 清理命令
```bash
# 清理缓存和临时文件
rm -rf ./rag_storage/
rm -rf ./demo_rag_storage/
rm -rf ./test_rag_storage/
rm -f *.json
rm -f auto_save_*.json

# 重新创建工作目录
mkdir -p rag_storage
mkdir -p output
```

## 📱 一键运行命令模板

### 快速开始模板
```bash
#!/bin/bash
# 一键启动RAGAnything问答模式

# 设置API密钥
export DASHSCOPE_API_KEY="sk-your-api-key-here"

# 激活虚拟环境
source venv/bin/activate

# 启动问答模式
./run_qa_mode.sh "$1"
```

### Windows批处理模板
```batch
@echo off
REM 一键启动RAGAnything问答模式

REM 设置API密钥
set DASHSCOPE_API_KEY=sk-your-api-key-here

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 启动问答模式
run_qa_mode.bat %1
```

## 🎯 常用命令组合

### 研究工作流
```bash
# 1. 处理研究论文
./run_qa_mode.sh "research/paper1.pdf"
# 保存会话为 research_session.json

# 2. 继续研究会话
./run_qa_mode.sh --session "research_session.json"

# 3. 添加新论文到现有知识库
python examples/qa_mode_example.py "research/paper2.pdf" \
    --working-dir "./rag_storage" \
    --session "research_session.json" \
    --api-key sk-your-api-key-here
```

### 企业文档分析
```bash
# 1. 处理企业文档
./run_qa_mode.sh "company/annual_report.pdf"

# 2. 分析财务数据
# 在问答模式中输入多模态查询：
分析这个财务数据表格

# 3. 保存分析会话
/save company_analysis_session.json
```

### 学习辅助
```bash
# 1. 处理教材
./run_qa_mode.sh "textbooks/machine_learning.pdf"

# 2. 互动学习
# 提问：什么是神经网络？
# 提问：解释反向传播算法
# 提问：比较不同的优化算法

# 3. 保存学习记录
/save ml_learning_session.json
```

---

## 🎉 总结

以上命令涵盖了RAGAnything问答模式的所有使用场景：

- ✅ **基础运行命令** - 快速开始使用
- ✅ **高级配置命令** - 自定义参数和优化
- ✅ **多文档处理** - 批量和会话管理
- ✅ **故障排除** - 诊断和修复问题
- ✅ **实用模板** - 一键运行脚本

选择适合您需求的命令开始使用RAGAnything的强大问答功能吧！🚀
