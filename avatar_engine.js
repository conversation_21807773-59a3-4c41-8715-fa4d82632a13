/**
 * macOS优化的数字人动画引擎
 * 基于Canvas 2D，针对Retina屏幕优化
 */
class MacOSAvatarEngine {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.isAnimating = false;
        this.animationFrame = null;
        
        // 动画状态
        this.mouthOpenness = 0;
        this.eyeBlinkState = 0;
        this.headTilt = 0;
        
        // 动画参数
        this.speechIntensity = 0;
        this.lastSpeechTime = 0;
        
        // 初始化
        this.initializeCanvas();
        this.drawBaseAvatar();
        this.startIdleAnimation();
    }

    initializeCanvas() {
        // 针对macOS Retina屏幕优化
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        this.ctx.scale(dpr, dpr);
        
        // 设置画布样式
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        // 抗锯齿
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }

    drawBaseAvatar() {
        const centerX = this.canvas.width / (window.devicePixelRatio || 1) / 2;
        const centerY = this.canvas.height / (window.devicePixelRatio || 1) / 2;
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制背景光晕
        this.drawBackgroundGlow(centerX, centerY);
        
        // 绘制头部
        this.drawHead(centerX, centerY - 20);
        
        // 绘制眼睛
        this.drawEyes(centerX, centerY - 40);
        
        // 绘制嘴巴
        this.drawMouth(centerX, centerY + 10);
        
        // 绘制装饰元素
        this.drawDecorations(centerX, centerY);
    }

    drawBackgroundGlow(x, y) {
        // 创建径向渐变背景
        const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, 150);
        gradient.addColorStop(0, 'rgba(0, 122, 255, 0.1)');
        gradient.addColorStop(1, 'rgba(0, 122, 255, 0.02)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    drawHead(x, y) {
        // 头部阴影
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.beginPath();
        this.ctx.ellipse(x + 2, y + 2, 85, 110, this.headTilt * 0.1, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 头部主体
        this.ctx.fillStyle = '#FFE4C4';
        this.ctx.beginPath();
        this.ctx.ellipse(x, y, 80, 105, this.headTilt * 0.1, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 头部高光
        const headGradient = this.ctx.createLinearGradient(x - 40, y - 50, x + 40, y + 50);
        headGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        headGradient.addColorStop(1, 'rgba(255, 255, 255, 0.1)');
        
        this.ctx.fillStyle = headGradient;
        this.ctx.beginPath();
        this.ctx.ellipse(x, y, 80, 105, this.headTilt * 0.1, 0, 2 * Math.PI);
        this.ctx.fill();
    }

    drawEyes(x, y) {
        const eyeY = y + this.eyeBlinkState * 5;
        const eyeHeight = 15 - this.eyeBlinkState * 12;
        
        // 左眼
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.beginPath();
        this.ctx.ellipse(x - 25, eyeY, 12, eyeHeight, 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.fillStyle = '#2C3E50';
        this.ctx.beginPath();
        this.ctx.ellipse(x - 25, eyeY, 8, Math.max(eyeHeight - 3, 1), 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 右眼
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.beginPath();
        this.ctx.ellipse(x + 25, eyeY, 12, eyeHeight, 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.fillStyle = '#2C3E50';
        this.ctx.beginPath();
        this.ctx.ellipse(x + 25, eyeY, 8, Math.max(eyeHeight - 3, 1), 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 眼部高光
        if (eyeHeight > 5) {
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.beginPath();
            this.ctx.ellipse(x - 27, eyeY - 2, 2, 3, 0, 0, 2 * Math.PI);
            this.ctx.ellipse(x + 23, eyeY - 2, 2, 3, 0, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    drawMouth(x, y) {
        const mouthWidth = 20 + this.mouthOpenness * 15;
        const mouthHeight = 3 + this.mouthOpenness * 12;
        
        // 嘴巴阴影
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.ellipse(x, y + 1, mouthWidth, mouthHeight, 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 嘴巴主体
        if (this.mouthOpenness > 0.1) {
            // 张嘴状态
            this.ctx.fillStyle = '#8B4513';
            this.ctx.beginPath();
            this.ctx.ellipse(x, y, mouthWidth, mouthHeight, 0, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // 牙齿
            if (this.mouthOpenness > 0.3) {
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.beginPath();
                this.ctx.ellipse(x, y - mouthHeight * 0.3, mouthWidth * 0.8, mouthHeight * 0.3, 0, 0, 2 * Math.PI);
                this.ctx.fill();
            }
        } else {
            // 闭嘴状态
            this.ctx.fillStyle = '#CD853F';
            this.ctx.beginPath();
            this.ctx.ellipse(x, y, mouthWidth, 2, 0, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    drawDecorations(x, y) {
        // 绘制一些装饰性元素，让数字人更有科技感
        
        // 状态指示器
        const time = Date.now() * 0.003;
        const pulseAlpha = 0.3 + 0.2 * Math.sin(time);
        
        this.ctx.strokeStyle = `rgba(0, 122, 255, ${pulseAlpha})`;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.arc(x, y - 20, 120, 0, 2 * Math.PI);
        this.ctx.stroke();
        
        // 小装饰点
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2 + time;
            const dotX = x + Math.cos(angle) * 130;
            const dotY = y - 20 + Math.sin(angle) * 130;
            
            this.ctx.fillStyle = `rgba(0, 122, 255, ${0.4 + 0.3 * Math.sin(time + i)})`;
            this.ctx.beginPath();
            this.ctx.arc(dotX, dotY, 2, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    startIdleAnimation() {
        // 待机动画
        const animate = () => {
            if (!this.isAnimating) {
                const time = Date.now() * 0.001;
                
                // 眨眼动画
                if (Math.random() < 0.005) {
                    this.blink();
                }
                
                // 轻微的头部摆动
                this.headTilt = Math.sin(time * 0.5) * 2;
                
                this.drawBaseAvatar();
            }
            
            this.animationFrame = requestAnimationFrame(animate);
        };
        
        animate();
    }

    blink() {
        // 眨眼动画
        let blinkProgress = 0;
        const blinkDuration = 200; // 毫秒
        const startTime = Date.now();
        
        const blinkFrame = () => {
            const elapsed = Date.now() - startTime;
            blinkProgress = elapsed / blinkDuration;
            
            if (blinkProgress < 0.5) {
                this.eyeBlinkState = blinkProgress * 2;
            } else {
                this.eyeBlinkState = 2 - blinkProgress * 2;
            }
            
            if (blinkProgress < 1) {
                requestAnimationFrame(blinkFrame);
            } else {
                this.eyeBlinkState = 0;
            }
        };
        
        blinkFrame();
    }

    startSpeechAnimation() {
        // 开始说话动画
        this.isAnimating = true;
        this.lastSpeechTime = Date.now();
        
        const speechFrame = () => {
            if (!this.isAnimating) return;
            
            const timeSinceLastSpeech = Date.now() - this.lastSpeechTime;
            
            // 模拟语音驱动的嘴部动画
            if (timeSinceLastSpeech < 100) {
                this.mouthOpenness = 0.3 + Math.random() * 0.5;
                this.speechIntensity = 0.8 + Math.random() * 0.2;
            } else {
                this.mouthOpenness *= 0.95;
                this.speechIntensity *= 0.9;
            }
            
            // 说话时的头部轻微摆动
            this.headTilt = Math.sin(Date.now() * 0.01) * this.speechIntensity * 3;
            
            this.drawBaseAvatar();
            
            if (this.mouthOpenness > 0.01 || this.speechIntensity > 0.01) {
                requestAnimationFrame(speechFrame);
            } else {
                this.stopSpeechAnimation();
            }
        };
        
        speechFrame();
    }

    updateSpeechAnimation() {
        // 更新说话动画（在语音播放过程中调用）
        this.lastSpeechTime = Date.now();
    }

    stopSpeechAnimation() {
        // 停止说话动画
        this.isAnimating = false;
        this.mouthOpenness = 0;
        this.speechIntensity = 0;
        this.headTilt = 0;
        this.drawBaseAvatar();
    }

    setStatus(status) {
        // 根据状态改变数字人表现
        switch (status) {
            case 'listening':
                this.startListeningAnimation();
                break;
            case 'thinking':
                this.startThinkingAnimation();
                break;
            case 'speaking':
                this.startSpeechAnimation();
                break;
            case 'idle':
            default:
                this.stopSpeechAnimation();
                break;
        }
    }

    startListeningAnimation() {
        // 听取动画（眼睛稍微放大，表示专注）
        this.eyeBlinkState = -0.2; // 眼睛稍微睁大
    }

    startThinkingAnimation() {
        // 思考动画（眼睛向上看）
        // 可以添加更复杂的思考表情
    }

    destroy() {
        // 清理资源
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        this.isAnimating = false;
    }
}

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MacOSAvatarEngine;
}
