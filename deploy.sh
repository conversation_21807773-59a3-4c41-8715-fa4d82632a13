#!/bin/bash

# RAGAnything 服务器自动部署脚本
# 使用方法: bash deploy.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署 RAGAnything 到服务器"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    apt update && apt upgrade -y
    
    log_info "安装基础依赖..."
    apt install -y python3 python3-pip python3-venv git curl wget
    apt install -y build-essential libssl-dev libffi-dev python3-dev
    apt install -y libjpeg-dev zlib1g-dev libpng-dev
    apt install -y poppler-utils tesseract-ocr nginx
}

# 创建项目环境
setup_environment() {
    log_info "创建项目环境..."
    
    # 创建项目目录
    mkdir -p /opt/raganything
    cd /opt/raganything
    
    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip setuptools wheel
    
    log_info "Python环境创建完成"
}

# 安装Python依赖
install_dependencies() {
    log_info "安装Python依赖包..."
    
    source /opt/raganything/venv/bin/activate
    
    # 安装PyTorch (CPU版本)
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    
    # 安装核心依赖
    pip install openai dashscope requests python-dotenv
    pip install magic-pdf pymupdf pillow numpy pandas networkx
    pip install fastapi uvicorn streamlit
    pip install faiss-cpu
    pip install lightrag
    
    log_info "Python依赖安装完成"
}

# 配置环境变量
setup_config() {
    log_info "配置环境变量..."
    
    # 创建.env文件
    cat > /opt/raganything/.env << EOF
# API配置
DASHSCOPE_API_KEY=sk-75e6425669944a9385ec758a64f009d2
OPENAI_API_KEY=sk-75e6425669944a9385ec758a64f009d2
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 路径配置
WORKING_DIR=/opt/raganything/data
OUTPUT_DIR=/opt/raganything/output

# 服务配置
HOST=0.0.0.0
PORT=8000
EOF
    
    chmod 600 /opt/raganything/.env
    
    log_info "环境变量配置完成"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p /opt/raganything/data
    mkdir -p /opt/raganything/output
    mkdir -p /opt/raganything/logs
    mkdir -p /opt/raganything/uploads
    mkdir -p /opt/raganything/RAG-Anything
    
    chown -R root:root /opt/raganything/
    chmod -R 755 /opt/raganything/
    
    log_info "目录结构创建完成"
}

# 创建Web服务
create_web_service() {
    log_info "创建Web服务..."
    
    cat > /opt/raganything/web_server.py << 'EOF'
#!/usr/bin/env python3
"""
RAGAnything Web服务
"""

import os
import asyncio
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# 加载环境变量
load_dotenv("/opt/raganything/.env")

app = FastAPI(title="RAGAnything API", version="1.0.0")

# 添加CORS支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "RAGAnything API Server", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "raganything"}

@app.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """上传文档并处理"""
    try:
        upload_dir = "/opt/raganything/uploads"
        file_path = os.path.join(upload_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        return {"message": "文件上传成功", "file_path": file_path}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query")
async def query_document(query: str, working_dir: str = None):
    """查询文档"""
    try:
        response = f"模拟回答: {query}"
        return {"query": query, "response": response}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    
    uvicorn.run(
        "web_server:app",
        host=host,
        port=port,
        reload=False,
        workers=1
    )
EOF
    
    chmod +x /opt/raganything/web_server.py
    
    log_info "Web服务创建完成"
}

# 创建系统服务
create_systemd_service() {
    log_info "创建系统服务..."
    
    cat > /etc/systemd/system/raganything.service << EOF
[Unit]
Description=RAGAnything Web Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/raganything
Environment=PATH=/opt/raganything/venv/bin
ExecStart=/opt/raganything/venv/bin/python web_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable raganything
    
    log_info "系统服务创建完成"
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."
    
    cat > /etc/nginx/sites-available/raganything << EOF
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    ln -sf /etc/nginx/sites-available/raganything /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    nginx -t
    
    log_info "Nginx配置完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    ufw --force enable
    ufw allow ssh
    ufw allow 80
    ufw allow 443
    
    log_info "防火墙配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    systemctl start raganything
    systemctl start nginx
    
    # 等待服务启动
    sleep 5
    
    log_info "服务启动完成"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 测试Python环境
    source /opt/raganything/venv/bin/activate
    python3 -c "
import torch
import openai
print('✅ Python环境正常')
print(f'PyTorch版本: {torch.__version__}')
"
    
    # 测试Web服务
    if curl -s http://localhost:8000/health > /dev/null; then
        log_info "✅ Web服务正常"
    else
        log_error "❌ Web服务异常"
    fi
    
    # 显示服务状态
    systemctl status raganything --no-pager -l
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 RAGAnything 部署完成！"
    echo "=================================="
    echo "📍 项目路径: /opt/raganything"
    echo "🌐 Web服务: http://$(curl -s ifconfig.me):80"
    echo "🔍 健康检查: http://$(curl -s ifconfig.me):80/health"
    echo "📝 日志查看: journalctl -u raganything -f"
    echo "🔧 服务管理:"
    echo "   启动: systemctl start raganything"
    echo "   停止: systemctl stop raganything"
    echo "   重启: systemctl restart raganything"
    echo "   状态: systemctl status raganything"
    echo ""
    echo "📋 下一步:"
    echo "1. 上传您的RAGAnything项目代码到 /opt/raganything/RAG-Anything/"
    echo "2. 根据需要修改配置文件 /opt/raganything/.env"
    echo "3. 重启服务: systemctl restart raganything"
    echo ""
}

# 主函数
main() {
    check_root
    update_system
    setup_environment
    install_dependencies
    setup_config
    create_directories
    create_web_service
    create_systemd_service
    setup_nginx
    setup_firewall
    start_services
    test_deployment
    show_deployment_info
}

# 执行主函数
main "$@"
