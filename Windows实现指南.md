# RAGAnything项目Windows实现指南

## 📋 项目实现复盘分析

基于您在Mac上的成功实现，以下是完整的实现路径和Windows适配版本。

## 🎯 您的实现路径回顾

### 第一阶段：环境准备
1. ✅ 克隆RAGAnything项目
2. ✅ 获取阿里云百炼API密钥
3. ✅ 配置Python环境

### 第二阶段：API配置
1. ✅ 配置阿里云百炼API
2. ✅ 测试API连接
3. ✅ 适配OpenAI兼容接口

### 第三阶段：脚本优化
1. ✅ 修改raganything_example.py
2. ✅ 添加GPU加速支持（MPS）
3. ✅ 配置环境变量

### 第四阶段：测试验证
1. ✅ 测试PDF文档处理
2. ✅ 验证多模态查询
3. ✅ 性能优化

---

## 🖥️ Windows版本实现指南

### 步骤1：环境准备

#### 1.1 安装Python
```powershell
# 下载并安装Python 3.9+
# 从 https://www.python.org/downloads/ 下载
# 确保勾选"Add Python to PATH"
```

#### 1.2 克隆项目
```powershell
# 打开PowerShell或命令提示符
git clone https://github.com/your-repo/RAG-Anything.git
cd RAG-Anything
```

#### 1.3 创建虚拟环境
```powershell
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate

# 升级pip
python -m pip install --upgrade pip
```

### 步骤2：安装依赖

#### 2.1 安装PyTorch（Windows CUDA版本）
```powershell
# 如果有NVIDIA GPU
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 如果只有CPU
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

#### 2.2 安装项目依赖
```powershell
pip install -r requirements.txt
pip install openai
pip install dashscope
```

### 步骤3：获取阿里云百炼API

#### 3.1 注册阿里云账号
1. 访问 https://www.aliyun.com/
2. 注册并完成实名认证

#### 3.2 开通百炼服务
1. 访问 https://bailian.console.aliyun.com/
2. 开通百炼服务
3. 创建API密钥

#### 3.3 记录API信息
```
API密钥格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点：https://dashscope.aliyuncs.com/compatible-mode/v1
```

### 步骤4：配置环境变量

#### 4.1 创建.env文件
```powershell
# 在项目根目录创建.env文件
New-Item -Path ".env" -ItemType File
```

#### 4.2 编辑.env文件内容
```env
# 阿里云百炼API配置
DASHSCOPE_API_KEY=sk-your-api-key-here
OPENAI_API_KEY=sk-your-api-key-here
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# LLM配置
LLM_BINDING=openai
LLM_MODEL=qwen-turbo
LLM_BINDING_HOST=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_BINDING_API_KEY=sk-your-api-key-here

# Embedding配置
EMBEDDING_BINDING=openai
EMBEDDING_MODEL=text-embedding-v1
EMBEDDING_DIM=1536
EMBEDDING_BINDING_API_KEY=sk-your-api-key-here
EMBEDDING_BINDING_HOST=https://dashscope.aliyuncs.com/compatible-mode/v1

# RAGAnything配置
WORKING_DIR=./rag_storage
MINERU_PARSE_METHOD=auto
ENABLE_IMAGE_PROCESSING=true
ENABLE_TABLE_PROCESSING=true
ENABLE_EQUATION_PROCESSING=true

# Windows GPU配置（如果有NVIDIA GPU）
MINERU_DEVICE=cuda
PYTORCH_ENABLE_MPS_FALLBACK=0
```

### 步骤5：修改脚本适配Windows

#### 5.1 创建Windows版本的raganything_example.py
```python
#!/usr/bin/env python
"""
RAGAnything Windows版本示例脚本
适配阿里云百炼API和Windows GPU加速
"""

import os
import argparse
import asyncio
import logging
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc, logger
from raganything import RAGAnything, RAGAnythingConfig

def detect_and_configure_device():
    """检测并配置Windows最佳设备"""
    try:
        import torch
        
        if torch.cuda.is_available():
            device = "cuda"
            print(f"🚀 检测到CUDA支持，使用GPU加速: {device}")
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
            return device
        else:
            device = "cpu"
            print(f"⚠️ 未检测到CUDA支持，使用CPU: {device}")
            return device
    except ImportError:
        print("⚠️ PyTorch未安装，使用CPU")
        return "cpu"

async def process_with_rag(
    file_path: str,
    output_dir: str,
    api_key: str,
    base_url: str = None,
    working_dir: str = None,
    device: str = None,
):
    """处理文档的主函数"""
    try:
        # 设置默认API端点
        if not base_url:
            base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
            logger.info(f"使用默认阿里云百炼端点: {base_url}")
        
        # 检测设备
        if not device:
            device = detect_and_configure_device()
        else:
            print(f"🔧 使用指定设备: {device}")
        
        # 创建配置
        config = RAGAnythingConfig(
            working_dir=working_dir or "./rag_storage",
            mineru_parse_method="auto",
            enable_image_processing=True,
            enable_table_processing=True,
            enable_equation_processing=True,
        )
        
        # 定义LLM函数
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return openai_complete_if_cache(
                "qwen-turbo",
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key=api_key,
                base_url=base_url,
                **kwargs,
            )
        
        # 定义视觉模型函数
        def vision_model_func(prompt, system_prompt=None, history_messages=[], image_data=None, **kwargs):
            if image_data:
                return openai_complete_if_cache(
                    "qwen-vl-plus",
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt} if system_prompt else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{image_data}"},
                                },
                            ],
                        } if image_data else {"role": "user", "content": prompt},
                    ],
                    api_key=api_key,
                    base_url=base_url,
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)
        
        # 定义embedding函数
        embedding_func = EmbeddingFunc(
            embedding_dim=1536,
            max_token_size=8192,
            func=lambda texts: openai_embed(
                texts,
                model="text-embedding-v1",
                api_key=api_key,
                base_url=base_url,
            ),
        )
        
        # 初始化RAGAnything
        rag = RAGAnything(
            config=config,
            llm_model_func=llm_model_func,
            vision_model_func=vision_model_func,
            embedding_func=embedding_func,
        )
        
        print("🏗️ RAGAnything初始化完成")
        
        # 处理文档
        print(f"📖 开始处理文档: {file_path}")
        await rag.process_document_complete(
            file_path=file_path,
            output_dir=output_dir,
            parse_method="auto",
            device=device,
            backend="pipeline"
        )
        
        print("✅ 文档处理完成！")
        
        # 执行查询测试
        print("\n🔍 开始查询测试...")
        
        queries = [
            "这个文档的主要内容是什么？",
            "文档中包含哪些重要信息？"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n❓ 查询 {i}: {query}")
            result = await rag.aquery(query, mode="hybrid")
            print(f"📝 回答 {i}: {result}")
        
        print("\n🎉 处理完成！")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RAGAnything Windows版本")
    parser.add_argument("file_path", help="要处理的文档路径")
    parser.add_argument("--working_dir", "-w", default="./rag_storage", help="工作目录")
    parser.add_argument("--output", "-o", default="./output", help="输出目录")
    parser.add_argument("--api-key", 
                       default=os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY"),
                       help="阿里云百炼API密钥")
    parser.add_argument("--base-url", 
                       default="https://dashscope.aliyuncs.com/compatible-mode/v1",
                       help="API端点")
    parser.add_argument("--device", 
                       choices=["auto", "cpu", "cuda"], 
                       default="auto",
                       help="设备选择")
    
    args = parser.parse_args()
    
    if not args.api_key:
        print("❌ 请提供API密钥")
        print("使用 --api-key 参数或设置环境变量 DASHSCOPE_API_KEY")
        return
    
    if not args.api_key.startswith('sk-'):
        print("⚠️ API密钥格式可能不正确，应以'sk-'开头")
    
    os.makedirs(args.output, exist_ok=True)
    
    device = None if args.device == "auto" else args.device
    
    print("🚀 RAGAnything Windows版本")
    print("=" * 40)
    print(f"📄 文档: {args.file_path}")
    print(f"🌐 API端点: {args.base_url}")
    print(f"🔑 API密钥: {args.api_key[:8]}...{args.api_key[-4:]}")
    
    asyncio.run(process_with_rag(
        args.file_path, args.output, args.api_key, args.base_url, args.working_dir, device
    ))

if __name__ == "__main__":
    main()
```

### 步骤6：创建Windows批处理脚本

#### 6.1 创建run_windows.bat
```batch
@echo off
echo 🚀 RAGAnything Windows运行脚本
echo ========================================

REM 激活虚拟环境
call venv\Scripts\activate

REM 设置环境变量
set DASHSCOPE_API_KEY=sk-your-api-key-here
set OPENAI_API_KEY=sk-your-api-key-here
set OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

REM 检查参数
if "%1"=="" (
    echo ❌ 请提供文档路径
    echo 用法: run_windows.bat your_document.pdf
    pause
    exit /b 1
)

echo 📄 处理文档: %1
echo 🎯 使用设备: CUDA GPU加速

REM 运行脚本
python examples/raganything_example_windows.py "%1" --device auto

echo ✅ 处理完成！
pause
```

### 步骤7：测试和验证

#### 7.1 API连接测试
```powershell
# 创建测试脚本
python -c "
import requests
headers = {'Authorization': 'Bearer sk-your-api-key-here', 'Content-Type': 'application/json'}
data = {'model': 'qwen-turbo', 'messages': [{'role': 'user', 'content': '测试'}], 'max_tokens': 10}
response = requests.post('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', headers=headers, json=data)
print('API状态:', response.status_code)
print('响应:', response.json() if response.status_code == 200 else response.text)
"
```

#### 7.2 GPU检测测试
```powershell
python -c "
import torch
print('PyTorch版本:', torch.__version__)
print('CUDA可用:', torch.cuda.is_available())
if torch.cuda.is_available():
    print('GPU设备:', torch.cuda.get_device_name(0))
    print('GPU数量:', torch.cuda.device_count())
else:
    print('将使用CPU模式')
"
```

#### 7.3 运行测试
```powershell
# 使用批处理脚本
run_windows.bat your_document.pdf

# 或直接运行Python脚本
python examples/raganything_example_windows.py your_document.pdf --api-key sk-your-api-key-here
```

### 步骤8：性能优化（Windows特定）

#### 8.1 CUDA优化
```env
# 在.env文件中添加
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

#### 8.2 内存优化
```env
# 大文档处理优化
MAX_CONCURRENT_FILES=1
CONTEXT_WINDOW=1
MAX_CONTEXT_TOKENS=2000
```

## 🎯 关键差异对比

| 项目 | Mac版本 | Windows版本 |
|------|---------|-------------|
| GPU加速 | MPS | CUDA |
| 虚拟环境激活 | `source venv/bin/activate` | `venv\Scripts\activate` |
| 路径分隔符 | `/` | `\` |
| 脚本扩展名 | `.sh` | `.bat` |
| 设备检测 | `torch.backends.mps.is_available()` | `torch.cuda.is_available()` |

## 📋 故障排除

### 常见问题
1. **CUDA不可用**: 安装NVIDIA驱动和CUDA Toolkit
2. **API调用失败**: 检查网络和API密钥
3. **内存不足**: 减少并发文件数量
4. **依赖冲突**: 使用虚拟环境隔离

### 性能预期
- **CPU模式**: 基础性能
- **CUDA模式**: 2-4倍性能提升
- **处理时间**: 10页PDF约2-5分钟

## 🎉 总结

您的Mac实现路径完全适用于Windows，主要差异在于GPU加速方式（MPS→CUDA）和系统命令。按照此指南，Windows用户可以获得相同的功能和性能。
