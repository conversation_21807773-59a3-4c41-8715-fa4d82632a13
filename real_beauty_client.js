/**
 * 真实美女数字人客户端
 * 基于用户提供的美女图片，实现面部动画
 */
class RealBeautyClient {
    constructor() {
        // 核心组件
        this.websocket = null;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        
        // 状态管理
        this.isConnected = false;
        this.isListening = false;
        this.isSpeaking = false;
        this.audioContextInitialized = false;
        
        // 动画状态
        this.isBlinking = false;
        this.blinkInterval = null;
        this.breathingEnabled = true;
        
        // DOM元素
        this.elements = {
            beautyImage: document.getElementById('beautyImage'),
            leftEye: document.getElementById('leftEye'),
            rightEye: document.getElementById('rightEye'),
            mouth: document.getElementById('mouth'),
            avatarStatus: document.getElementById('avatarStatus'),
            connectionStatus: document.getElementById('connectionStatus'),
            messagesContainer: document.getElementById('messagesContainer'),
            textInput: document.getElementById('textInput'),
            voiceBtn: document.getElementById('voiceBtn'),
            sendBtn: document.getElementById('sendBtn'),
            loadingIndicator: document.getElementById('loadingIndicator'),
            blinkBtn: document.getElementById('blinkBtn'),
            speakBtn: document.getElementById('speakBtn'),
            breatheBtn: document.getElementById('breatheBtn')
        };
        
        // 初始化
        this.initialize();
    }

    async initialize() {
        try {
            console.log('💄 初始化真实美女数字人客户端...');
            
            // 初始化WebSocket连接
            this.initializeWebSocket();
            
            // 初始化语音系统
            this.initializeSpeechSynthesis();
            this.initializeSpeechRecognition();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化音频上下文
            this.initializeAudioContext();
            
            // 启动自动眨眼
            this.startAutoBlinking();
            
            console.log('✅ 真实美女数字人客户端初始化成功');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.updateConnectionStatus('❌ 初始化失败', false);
            this.updateAvatarStatus('❌ 初始化失败');
        }
    }

    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            this.isConnected = true;
            this.updateConnectionStatus('✅ 已连接', true);
            console.log('✅ WebSocket连接成功');
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleServerMessage(data);
            } catch (error) {
                console.error('❌ 消息解析失败:', error);
            }
        };

        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            this.updateConnectionStatus('❌ 连接错误', false);
        };

        this.websocket.onclose = () => {
            this.isConnected = false;
            this.updateConnectionStatus('🔄 连接断开', false);
            console.log('🔌 WebSocket连接关闭');
            
            // 尝试重连
            setTimeout(() => {
                if (!this.isConnected) {
                    console.log('🔄 尝试重新连接...');
                    this.initializeWebSocket();
                }
            }, 3000);
        };
    }

    initializeSpeechSynthesis() {
        if (this.synthesis) {
            this.synthesis.addEventListener('voiceschanged', () => {
                const voices = this.synthesis.getVoices();
                console.log(`🎤 语音列表已加载，共 ${voices.length} 个语音`);
            });
        }
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'zh-CN';
            
            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateVoiceButton(true);
                this.updateAvatarStatus('👂 正在听取...');
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.textInput.value = transcript;
                this.sendQuery(transcript);
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceButton(false);
                this.updateAvatarStatus('✨ 真实美女已就绪');
            };

            this.recognition.onerror = (event) => {
                console.error('❌ 语音识别错误:', event.error);
                this.isListening = false;
                this.updateVoiceButton(false);
                this.updateAvatarStatus('❌ 语音识别失败');
                
                if (event.error === 'not-allowed') {
                    alert('请允许麦克风权限以使用语音输入功能');
                }
            };

            console.log('✅ 语音识别初始化成功');
        }
    }

    setupEventListeners() {
        // 发送按钮
        this.elements.sendBtn.addEventListener('click', () => {
            const text = this.elements.textInput.value.trim();
            if (text) {
                this.sendQuery(text);
                this.elements.textInput.value = '';
            }
        });

        // 回车发送
        this.elements.textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.elements.sendBtn.click();
            }
        });

        // 语音按钮
        this.elements.voiceBtn.addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // 控制按钮
        this.elements.blinkBtn.addEventListener('click', () => {
            this.testBlink();
        });

        this.elements.speakBtn.addEventListener('click', () => {
            this.testSpeak();
        });

        this.elements.breatheBtn.addEventListener('click', () => {
            this.toggleBreathing();
        });

        // 输入框焦点处理
        this.elements.textInput.addEventListener('focus', () => {
            this.updateAvatarStatus('💭 等待您的问题...');
        });

        this.elements.textInput.addEventListener('blur', () => {
            if (!this.isListening && !this.isSpeaking) {
                this.updateAvatarStatus('✨ 真实美女已就绪');
            }
        });
    }

    initializeAudioContext() {
        this.audioContextInitialized = false;
        
        const initAudio = () => {
            if (!this.audioContextInitialized) {
                try {
                    const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    silentAudio.volume = 0;
                    silentAudio.play().then(() => {
                        this.audioContextInitialized = true;
                        console.log('✅ 音频上下文已初始化');
                        document.removeEventListener('click', initAudio);
                        document.removeEventListener('touchstart', initAudio);
                    }).catch(() => {
                        console.log('⚠️ 音频上下文初始化失败');
                    });
                } catch (error) {
                    console.log('⚠️ 音频上下文初始化异常:', error);
                }
            }
        };
        
        document.addEventListener('click', initAudio);
        document.addEventListener('touchstart', initAudio);
    }

    startAutoBlinking() {
        // 自动眨眼，每3-6秒随机眨一次
        this.blinkInterval = setInterval(() => {
            if (!this.isBlinking) {
                this.blink();
            }
        }, 3000 + Math.random() * 3000);
        
        console.log('👁️ 自动眨眼已启动');
    }

    blink() {
        if (this.isBlinking) return;
        
        this.isBlinking = true;
        
        // 眨眼动画
        this.elements.leftEye.classList.add('blink');
        this.elements.rightEye.classList.add('blink');
        
        setTimeout(() => {
            this.elements.leftEye.classList.remove('blink');
            this.elements.rightEye.classList.remove('blink');
            this.isBlinking = false;
        }, 150);
    }

    startSpeechAnimation() {
        this.isSpeaking = true;
        this.elements.mouth.classList.add('speaking');
        this.updateAvatarStatus('💋 正在说话...');
        
        // 说话时的嘴部动画
        this.speechAnimationInterval = setInterval(() => {
            if (this.isSpeaking) {
                // 随机嘴形变化
                const intensity = 0.5 + Math.random() * 0.5;
                this.elements.mouth.style.transform = `translateX(-50%) scaleY(${intensity})`;
            }
        }, 100);
    }

    stopSpeechAnimation() {
        this.isSpeaking = false;
        this.elements.mouth.classList.remove('speaking');
        this.elements.mouth.style.transform = 'translateX(-50%)';
        
        if (this.speechAnimationInterval) {
            clearInterval(this.speechAnimationInterval);
            this.speechAnimationInterval = null;
        }
        
        this.updateAvatarStatus('✨ 真实美女已就绪');
    }

    sendQuery(text) {
        if (!this.isConnected) {
            alert('未连接到服务器');
            return;
        }

        if (!text.trim()) {
            return;
        }

        // 显示用户消息
        this.addMessage('user', text);
        
        // 显示加载状态
        this.showLoading(true);
        this.updateAvatarStatus('🧠 正在思考...');

        // 设置超时处理
        const queryTimeout = setTimeout(() => {
            console.warn('⚠️ 查询超时');
            this.showLoading(false);
            this.updateAvatarStatus('⏰ 查询超时，请重试');
            this.addMessage('system', '查询超时，请尝试更简短的问题或重新提问');
        }, 30000);

        const message = {
            type: 'query',
            text: text.trim(),
            timestamp: Date.now()
        };

        try {
            this.websocket.send(JSON.stringify(message));
            console.log('📤 发送查询:', text);
            this.currentQueryTimeout = queryTimeout;
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
            clearTimeout(queryTimeout);
            this.showLoading(false);
            this.updateAvatarStatus('❌ 发送失败');
        }
    }

    handleServerMessage(data) {
        console.log('📥 收到服务器消息:', data);

        switch (data.type) {
            case 'response':
                this.handleResponse(data);
                break;
            case 'error':
                this.handleError(data);
                break;
            default:
                console.warn('⚠️ 未知消息类型:', data.type);
        }
    }

    handleResponse(data) {
        // 清除查询超时
        if (this.currentQueryTimeout) {
            clearTimeout(this.currentQueryTimeout);
            this.currentQueryTimeout = null;
        }
        
        // 隐藏加载状态
        this.showLoading(false);
        
        // 显示回答
        this.addMessage('assistant', data.text);
        
        // 播放语音并同步面部动画
        if (data.audio && data.audio_type === 'edge-tts') {
            console.log('🎤 检测到Edge-TTS音频，开始播放');
            this.playEdgeTTSAudio(data.audio, data.text);
        } else {
            console.log('🔄 使用浏览器TTS播放');
            this.speakResponse(data.text, data.language || 'zh-CN');
        }
    }

    handleError(data) {
        if (this.currentQueryTimeout) {
            clearTimeout(this.currentQueryTimeout);
            this.currentQueryTimeout = null;
        }
        
        this.showLoading(false);
        this.updateAvatarStatus('❌ 处理失败');
        
        this.addMessage('system', `错误: ${data.message || '未知错误'}`);
        console.error('❌ 服务器错误:', data);
    }

    playEdgeTTSAudio(audioBase64, text) {
        try {
            console.log('🎤 开始播放Edge-TTS音频');
            
            const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;
            const audio = new Audio();
            audio.preload = 'auto';
            audio.volume = 0.9;
            
            audio.addEventListener('loadstart', () => {
                this.startSpeechAnimation();
            });
            
            audio.addEventListener('play', () => {
                console.log('🎤 音频开始播放');
            });
            
            audio.addEventListener('ended', () => {
                console.log('✅ Edge-TTS语音播放完成');
                this.stopSpeechAnimation();
            });
            
            audio.addEventListener('error', (event) => {
                console.error('❌ 音频播放失败:', event);
                this.stopSpeechAnimation();
                
                // 降级到浏览器TTS
                setTimeout(() => {
                    this.speakResponse(text, 'zh-CN');
                }, 500);
            });
            
            audio.src = audioDataUrl;
            
            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('✅ 音频播放启动成功');
                }).catch(error => {
                    console.error('❌ 音频播放启动失败:', error);
                    this.updateAvatarStatus('请点击页面后重试');
                    setTimeout(() => {
                        this.speakResponse(text, 'zh-CN');
                    }, 1000);
                });
            }
            
        } catch (error) {
            console.error('❌ Edge-TTS音频处理失败:', error);
            this.speakResponse(text, 'zh-CN');
        }
    }

    speakResponse(text, language = 'zh-CN') {
        if (!this.synthesis) {
            console.warn('⚠️ 浏览器不支持语音合成');
            return;
        }

        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language;
        utterance.rate = 0.85;
        utterance.pitch = 1.1;
        utterance.volume = 0.9;

        utterance.onstart = () => {
            this.startSpeechAnimation();
        };

        utterance.onend = () => {
            this.stopSpeechAnimation();
        };

        utterance.onerror = (event) => {
            console.error('❌ 语音合成错误:', event);
            this.stopSpeechAnimation();
        };

        this.synthesis.speak(utterance);
    }

    toggleVoiceInput() {
        if (!this.recognition) {
            alert('您的浏览器不支持语音识别功能');
            return;
        }

        if (this.isListening) {
            this.recognition.stop();
        } else {
            try {
                this.recognition.start();
            } catch (error) {
                console.error('❌ 启动语音识别失败:', error);
                alert('启动语音识别失败，请检查麦克风权限');
            }
        }
    }

    testBlink() {
        this.blink();
        this.updateAvatarStatus('👁️ 眨眼测试完成');
        setTimeout(() => {
            this.updateAvatarStatus('✨ 真实美女已就绪');
        }, 1500);
    }

    testSpeak() {
        this.startSpeechAnimation();
        this.updateAvatarStatus('💋 说话测试中...');
        
        setTimeout(() => {
            this.stopSpeechAnimation();
        }, 2000);
    }

    toggleBreathing() {
        this.breathingEnabled = !this.breathingEnabled;
        
        if (this.breathingEnabled) {
            this.elements.beautyImage.classList.add('breathing');
            this.elements.breatheBtn.classList.add('active');
            this.elements.breatheBtn.textContent = '🫁 停止呼吸';
            this.updateAvatarStatus('🫁 呼吸动画已启用');
        } else {
            this.elements.beautyImage.classList.remove('breathing');
            this.elements.breatheBtn.classList.remove('active');
            this.elements.breatheBtn.textContent = '🫁 启用呼吸';
            this.updateAvatarStatus('🫁 呼吸动画已停用');
        }
        
        setTimeout(() => {
            this.updateAvatarStatus('✨ 真实美女已就绪');
        }, 2000);
    }

    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        let roleText = '';
        switch (role) {
            case 'user':
                roleText = '您';
                break;
            case 'assistant':
                roleText = '美女助手';
                break;
            case 'system':
                roleText = '系统';
                break;
        }

        messageDiv.innerHTML = `
            <div class="message-content">
                <strong>${roleText}:</strong><br>
                ${content.replace(/\n/g, '<br>')}
            </div>
            <div class="message-time">${timeString}</div>
        `;

        this.elements.messagesContainer.appendChild(messageDiv);
        this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
    }

    showLoading(show) {
        if (show) {
            this.elements.loadingIndicator.classList.add('show');
        } else {
            this.elements.loadingIndicator.classList.remove('show');
        }
    }

    updateVoiceButton(isRecording) {
        if (isRecording) {
            this.elements.voiceBtn.classList.add('recording');
            this.elements.voiceBtn.innerHTML = '🔴 停止';
        } else {
            this.elements.voiceBtn.classList.remove('recording');
            this.elements.voiceBtn.innerHTML = '🎤';
        }
    }

    updateConnectionStatus(message, isConnected) {
        this.elements.connectionStatus.textContent = message;
        this.elements.connectionStatus.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
    }

    updateAvatarStatus(status) {
        this.elements.avatarStatus.textContent = status;
    }

    destroy() {
        if (this.websocket) {
            this.websocket.close();
        }
        if (this.recognition) {
            this.recognition.stop();
        }
        if (this.synthesis) {
            this.synthesis.cancel();
        }
        if (this.blinkInterval) {
            clearInterval(this.blinkInterval);
        }
        if (this.speechAnimationInterval) {
            clearInterval(this.speechAnimationInterval);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('💄 初始化真实美女数字人客户端...');
    window.realBeautyClient = new RealBeautyClient();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.realBeautyClient) {
        window.realBeautyClient.destroy();
    }
});
