<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实美女数字人 - 基于您的图片</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            min-height: 700px;
        }

        .avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .avatar-container {
            width: 100%;
            height: 500px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
            border: 3px solid #007AFF;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .avatar-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .beauty-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 17px;
            position: absolute;
            top: 0;
            left: 0;
        }

        .face-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .eye {
            position: absolute;
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            transition: all 0.1s ease;
        }

        .eye.left {
            top: 35%;
            left: 42%;
        }

        .eye.right {
            top: 35%;
            right: 42%;
        }

        .eye.blink {
            height: 2px;
            background: rgba(255, 255, 255, 0.5);
        }

        .mouth {
            position: absolute;
            top: 55%;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 8px;
            background: rgba(255, 100, 100, 0.7);
            border-radius: 50%;
            transition: all 0.1s ease;
        }

        .mouth.speaking {
            height: 15px;
            width: 25px;
            background: rgba(255, 100, 100, 0.9);
        }

        .avatar-controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 15px;
            background: #007AFF;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: #34C759;
        }

        .avatar-status {
            margin-top: 15px;
            padding: 12px 20px;
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(52, 199, 89, 0.3);
        }

        .chat-section {
            display: flex;
            flex-direction: column;
            height: 640px;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .chat-header h1 {
            color: #1d1d1f;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .chat-header p {
            color: #86868b;
            font-size: 18px;
            font-weight: 500;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 20px;
            margin-bottom: 20px;
            border: 2px solid #e5e5ea;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 20px;
            max-width: 85%;
            word-wrap: break-word;
            animation: messageSlideIn 0.4s ease-out;
            position: relative;
        }

        .message.user {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 8px;
        }

        .message.assistant {
            background: linear-gradient(45deg, #E5E5EA, #F2F2F7);
            color: #1d1d1f;
            border-bottom-left-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .message.system {
            background: linear-gradient(45deg, #FF9500, #FF6B35);
            color: white;
            margin: 0 auto;
            text-align: center;
            border-radius: 15px;
        }

        .input-section {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: 2px solid #e5e5ea;
        }

        .text-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .text-input:focus {
            background: white;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        .btn-voice {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
        }

        .btn-voice:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 199, 89, 0.3);
        }

        .btn-voice.recording {
            background: linear-gradient(45deg, #FF3B30, #FF6B35);
            animation: pulse 1.5s infinite;
        }

        .connection-status {
            position: fixed;
            top: 25px;
            right: 25px;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .connection-status.connected {
            background: rgba(52, 199, 89, 0.9);
            color: white;
        }

        .connection-status.disconnected {
            background: rgba(255, 59, 48, 0.9);
            color: white;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 15px;
            color: #007AFF;
            font-weight: 600;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 15px;
            margin: 10px 0;
        }

        .loading.show {
            display: block;
            animation: loadingPulse 1.5s infinite;
        }

        @keyframes messageSlideIn {
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes loadingPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .beauty-image.breathing {
            animation: breathe 4s ease-in-out infinite;
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .avatar-container {
                height: 400px;
            }
            
            .chat-section {
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔄 连接中...</div>
    
    <div class="container">
        <!-- 真实美女数字人区域 -->
        <div class="avatar-section">
            <div class="avatar-container" id="avatarContainer">
                <img src="my.webp" alt="美女数字人" class="beauty-image breathing" id="beautyImage">
                <div class="face-overlay">
                    <div class="eye left" id="leftEye"></div>
                    <div class="eye right" id="rightEye"></div>
                    <div class="mouth" id="mouth"></div>
                </div>
            </div>
            
            <div class="avatar-controls">
                <button class="control-btn" id="blinkBtn">
                    👁️ 眨眼测试
                </button>
                <button class="control-btn" id="speakBtn">
                    💋 说话测试
                </button>
                <button class="control-btn" id="breatheBtn">
                    🫁 呼吸动画
                </button>
            </div>
            
            <div class="avatar-status" id="avatarStatus">✨ 真实美女已就绪</div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>💄 真实美女数字人</h1>
                <p>基于您的美女图片，拥有逼真的面部动画</p>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>美女助手:</strong><br>
                        您好！我是基于您提供的真实美女图片创建的数字人助手。我拥有：<br><br>
                        💄 <strong>真实美女外观</strong> - 使用您的美女图片<br>
                        👁️ <strong>自然眨眼动画</strong> - 模拟真实眨眼<br>
                        💋 <strong>说话嘴形同步</strong> - 根据语音驱动嘴部<br>
                        🫁 <strong>呼吸动画效果</strong> - 自然的呼吸起伏<br>
                        🎤 <strong>高质量语音</strong> - Edge-TTS女声<br><br>
                        请随时向我提问，我会用生动的面部动画为您解答！
                    </div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>

            <div class="loading" id="loadingIndicator">
                <span>🧠 美女正在思考中...</span>
            </div>

            <div class="input-section">
                <input 
                    type="text" 
                    id="textInput" 
                    class="text-input" 
                    placeholder="输入您的问题..."
                    maxlength="500"
                >
                <button id="voiceBtn" class="btn btn-voice" title="语音输入">
                    🎤
                </button>
                <button id="sendBtn" class="btn btn-primary" title="发送">
                    发送
                </button>
            </div>
        </div>
    </div>

    <script src="real_beauty_client.js"></script>
    
    <script>
        // 设置欢迎消息时间
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();
        
        console.log('💄 真实美女数字人系统已加载');
    </script>
</body>
</html>
