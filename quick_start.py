#!/usr/bin/env python3
"""
快速启动脚本 - 自动设置API密钥并启动Web RAG系统
"""

import os
import sys
import subprocess
import time

def main():
    print("🚀 RAG数字人Web系统 - 快速启动")
    print("=" * 50)
    
    # 设置API密钥
    api_key = "sk-e182b143987f48a385e70370515db60a"
    os.environ["DASHSCOPE_API_KEY"] = api_key
    
    print(f"✅ API密钥已设置: {api_key[:8]}...{api_key[-4:]}")
    print("🔑 使用阿里云百炼API")
    
    # 启动Web服务器
    print("\n🚀 启动Web RAG服务器...")
    
    try:
        # 直接启动服务器
        subprocess.run([sys.executable, "web_rag_server.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 手动启动方法:")
        print("1. export DASHSCOPE_API_KEY='sk-e182b143987f48a385e70370515db60a'")
        print("2. python web_rag_server.py")
        print("3. 访问 http://localhost:8000")

if __name__ == "__main__":
    main()
