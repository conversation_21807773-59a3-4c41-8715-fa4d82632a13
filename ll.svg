<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1023.876953125 3486" style="max-width: 1023.876953125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789"><style>#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .error-icon{fill:#a44141;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edge-thickness-normal{stroke-width:1px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .marker.cross{stroke:lightgrey;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 p{margin:0;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .cluster-label text{fill:#F9FFFE;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .cluster-label span{color:#F9FFFE;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .cluster-label span p{background-color:transparent;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .label text,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 span{fill:#ccc;color:#ccc;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node rect,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node circle,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node ellipse,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node polygon,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .rough-node .label text,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node .label text,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .image-shape .label,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .icon-shape .label{text-anchor:middle;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .rough-node .label,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node .label,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .image-shape .label,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .icon-shape .label{text-align:center;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .node.clickable{cursor:pointer;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .arrowheadPath{fill:lightgrey;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .cluster text{fill:#F9FFFE;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .cluster span{color:#F9FFFE;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 rect.text{fill:none;stroke-width:0;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .icon-shape,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .icon-shape p,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .icon-shape rect,#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M624.391,62L624.391,66.167C624.391,70.333,624.391,78.667,624.461,86.417C624.531,94.167,624.672,101.334,624.742,104.917L624.812,108.501"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M567.114,204.723L514.039,220.436C460.965,236.149,354.816,267.574,301.742,288.787C248.668,310,248.668,321,248.668,326.5L248.668,332"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_2" d="M585.31,222.919L570.963,235.599C556.617,248.279,527.924,273.64,513.577,291.82C499.23,310,499.23,321,499.23,326.5L499.23,332"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_E_3" d="M664.472,222.919L678.651,235.599C692.831,248.279,721.191,273.64,735.371,296.987C749.551,320.333,749.551,341.667,749.551,363C749.551,384.333,749.551,405.667,749.551,427C749.551,448.333,749.551,469.667,749.551,489C749.551,508.333,749.551,525.667,749.551,537.833C749.551,550,749.551,557,749.551,560.5L749.551,564"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C1_4" d="M248.668,390L248.668,396.167C248.668,402.333,248.668,414.667,248.668,426.333C248.668,438,248.668,449,248.668,454.5L248.668,460"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C1_C2_5" d="M248.668,518L248.668,522.167C248.668,526.333,248.668,534.667,248.668,542.333C248.668,550,248.668,557,248.668,560.5L248.668,564"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_F_6" d="M248.668,622L248.668,626.167C248.668,630.333,248.668,638.667,296.13,649.402C343.592,660.137,438.516,673.275,485.978,679.844L533.44,686.412"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D1_7" d="M499.23,390L499.23,396.167C499.23,402.333,499.23,414.667,499.23,426.333C499.23,438,499.23,449,499.23,454.5L499.23,460"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_D2_8" d="M499.23,518L499.23,522.167C499.23,526.333,499.23,534.667,499.23,542.333C499.23,550,499.23,557,499.23,560.5L499.23,564"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_F_9" d="M499.23,622L499.23,626.167C499.23,630.333,499.23,638.667,508.644,646.744C518.057,654.822,536.883,662.644,546.297,666.554L555.71,670.465"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_10" d="M749.551,622L749.551,626.167C749.551,630.333,749.551,638.667,740.138,646.744C730.724,654.822,711.898,662.644,702.485,666.554L693.072,670.465"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_F1_11" d="M624.391,726L624.391,730.167C624.391,734.333,624.391,742.667,624.391,750.333C624.391,758,624.391,765,624.391,768.5L624.391,772"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F1_F2_12" d="M624.391,854L624.391,858.167C624.391,862.333,624.391,870.667,624.391,878.333C624.391,886,624.391,893,624.391,896.5L624.391,900"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F2_F3_13" d="M624.391,982L624.391,986.167C624.391,990.333,624.391,998.667,624.391,1006.333C624.391,1014,624.391,1021,624.391,1024.5L624.391,1028"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_G1_14" d="M543.98,1067.051L470.818,1074.375C397.656,1081.7,251.332,1096.35,178.17,1107.175C105.008,1118,105.008,1125,105.008,1128.5L105.008,1132"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_G2_15" d="M543.98,1073.725L510.055,1079.937C476.129,1086.15,408.277,1098.575,374.352,1110.287C340.426,1122,340.426,1133,340.426,1138.5L340.426,1144"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_G3_16" d="M603.954,1086L600.8,1090.167C597.646,1094.333,591.339,1102.667,588.185,1110.333C585.031,1118,585.031,1125,585.031,1128.5L585.031,1132"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_G4_17" d="M704.801,1078.028L728.024,1083.523C751.247,1089.018,797.694,1100.009,820.917,1109.005C844.141,1118,844.141,1125,844.141,1128.5L844.141,1132"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G1_H_18" d="M105.008,1214L105.008,1218.167C105.008,1222.333,105.008,1230.667,174.348,1241.776C243.689,1252.885,382.37,1266.769,451.711,1273.711L521.051,1280.654"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G2_H_19" d="M340.426,1202L340.426,1208.167C340.426,1214.333,340.426,1226.667,370.538,1238.347C400.649,1250.028,460.873,1261.056,490.985,1266.571L521.097,1272.085"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G3_H_20" d="M585.031,1214L585.031,1218.167C585.031,1222.333,585.031,1230.667,587.783,1238.468C590.534,1246.27,596.037,1253.54,598.788,1257.176L601.54,1260.811"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G4_H_21" d="M844.141,1214L844.141,1218.167C844.141,1222.333,844.141,1230.667,824.724,1239.428C805.308,1248.189,766.475,1257.378,747.059,1261.973L727.643,1266.567"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_22" d="M624.391,1318L624.391,1322.167C624.391,1326.333,624.391,1334.667,624.391,1342.333C624.391,1350,624.391,1357,624.391,1360.5L624.391,1364"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I1_23" d="M535.98,1411.732L504.922,1417.61C473.863,1423.488,411.745,1435.244,380.686,1444.622C349.627,1454,349.627,1461,349.627,1464.5L349.627,1468"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I2_24" d="M616.463,1422L615.24,1426.167C614.017,1430.333,611.57,1438.667,610.346,1446.333C609.123,1454,609.123,1461,609.123,1464.5L609.123,1468"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I3_25" d="M712.801,1414.368L737.628,1419.806C762.455,1425.245,812.109,1436.123,836.937,1445.061C861.764,1454,861.764,1461,861.764,1464.5L861.764,1468"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I1_J_26" d="M349.627,1526L349.627,1530.167C349.627,1534.333,349.627,1542.667,349.627,1550.333C349.627,1558,349.627,1565,349.627,1568.5L349.627,1572"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_J1_27" d="M349.627,1630L349.627,1634.167C349.627,1638.333,349.627,1646.667,349.627,1654.333C349.627,1662,349.627,1669,349.627,1672.5L349.627,1676"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J1_J2_28" d="M349.627,1758L349.627,1762.167C349.627,1766.333,349.627,1774.667,349.627,1782.333C349.627,1790,349.627,1797,349.627,1800.5L349.627,1804"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J2_J3_29" d="M349.627,1886L349.627,1890.167C349.627,1894.333,349.627,1902.667,349.627,1910.333C349.627,1918,349.627,1925,349.627,1928.5L349.627,1932"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_K_30" d="M609.123,1526L609.123,1530.167C609.123,1534.333,609.123,1542.667,609.123,1550.333C609.123,1558,609.123,1565,609.123,1568.5L609.123,1572"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_K1_31" d="M609.123,1630L609.123,1634.167C609.123,1638.333,609.123,1646.667,609.123,1654.333C609.123,1662,609.123,1669,609.123,1672.5L609.123,1676"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K1_K2_32" d="M609.123,1758L609.123,1762.167C609.123,1766.333,609.123,1774.667,609.123,1782.333C609.123,1790,609.123,1797,609.123,1800.5L609.123,1804"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K2_K3_33" d="M609.123,1886L609.123,1890.167C609.123,1894.333,609.123,1902.667,609.123,1912.333C609.123,1922,609.123,1933,609.123,1938.5L609.123,1944"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I3_L_34" d="M861.764,1526L861.764,1530.167C861.764,1534.333,861.764,1542.667,861.764,1550.333C861.764,1558,861.764,1565,861.764,1568.5L861.764,1572"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_L1_35" d="M861.764,1630L861.764,1634.167C861.764,1638.333,861.764,1646.667,861.764,1654.333C861.764,1662,861.764,1669,861.764,1672.5L861.764,1676"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L1_L2_36" d="M861.764,1758L861.764,1762.167C861.764,1766.333,861.764,1774.667,861.764,1782.333C861.764,1790,861.764,1797,861.764,1800.5L861.764,1804"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L2_L3_37" d="M861.764,1886L861.764,1890.167C861.764,1894.333,861.764,1902.667,861.764,1912.333C861.764,1922,861.764,1933,861.764,1938.5L861.764,1944"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J3_M_38" d="M349.627,2014L349.627,2018.167C349.627,2022.333,349.627,2030.667,367.402,2039.675C385.176,2048.683,420.726,2058.365,438.501,2063.206L456.275,2068.048"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K3_M_39" d="M609.123,2002L609.123,2008.167C609.123,2014.333,609.123,2026.667,604.159,2036.597C599.195,2046.528,589.268,2054.055,584.304,2057.819L579.34,2061.583"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L3_M_40" d="M861.764,2002L861.764,2008.167C861.764,2014.333,861.764,2026.667,822.287,2039.224C782.81,2051.781,703.857,2064.562,664.38,2070.953L624.904,2077.344"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_M1_41" d="M540.545,2118L540.545,2122.167C540.545,2126.333,540.545,2134.667,540.545,2142.333C540.545,2150,540.545,2157,540.545,2160.5L540.545,2164"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M1_M2_42" d="M540.545,2246L540.545,2250.167C540.545,2254.333,540.545,2262.667,540.545,2276.333C540.545,2290,540.545,2309,540.545,2318.5L540.545,2328"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_43" d="M305.287,2234L305.287,2240.167C305.287,2246.333,305.287,2258.667,305.357,2268.417C305.428,2278.167,305.568,2285.334,305.638,2288.917L305.709,2292.501"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P1_44" d="M269.689,2410.402L258.301,2422.502C246.913,2434.602,224.137,2458.801,212.749,2476.4C201.361,2494,201.361,2505,201.361,2510.5L201.361,2516"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P2_45" d="M330.279,2422.009L335.165,2432.174C340.051,2442.339,349.823,2462.67,360.555,2478.536C371.287,2494.402,382.978,2505.805,388.824,2511.506L394.67,2517.207"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P3_46" d="M360.491,2391.797L401.601,2406.997C442.71,2422.198,524.93,2452.599,572.962,2473.54C620.994,2494.482,634.837,2505.964,641.759,2511.705L648.68,2517.446"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P4_47" d="M368.236,2384.052L450.618,2400.543C532.999,2417.034,697.763,2450.017,784.581,2472.151C871.399,2494.285,880.271,2505.57,884.707,2511.213L889.143,2516.855"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M2_P1_48" d="M460.135,2409.661L434.712,2421.884C409.288,2434.107,358.442,2458.554,324.941,2476.556C291.44,2494.558,275.285,2506.115,267.207,2511.894L259.13,2517.673"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M2_P2_49" d="M545.631,2410L547.217,2422.167C548.804,2434.333,551.977,2458.667,544.579,2476.638C537.181,2494.61,519.212,2506.22,510.228,2512.024L501.243,2517.829"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M2_P3_50" d="M620.955,2404.357L652.55,2417.465C684.146,2430.572,747.337,2456.786,770.416,2475.685C793.495,2494.584,776.463,2506.167,767.947,2511.959L759.431,2517.751"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M2_P4_51" d="M620.955,2391.764L679.842,2406.97C738.729,2422.176,856.502,2452.588,911.546,2473.41C966.59,2494.233,958.904,2505.466,955.061,2511.082L951.218,2516.699"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P1_Q_52" d="M201.361,2598L201.361,2602.167C201.361,2606.333,201.361,2614.667,212.525,2622.778C223.688,2630.889,246.016,2638.778,257.179,2642.723L268.343,2646.667"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P2_Q_53" d="M437.521,2598L437.521,2602.167C437.521,2606.333,437.521,2614.667,430.966,2622.664C424.411,2630.661,411.3,2638.321,404.745,2642.152L398.189,2645.982"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P3_Q_54" d="M698.779,2598L698.779,2602.167C698.779,2606.333,698.779,2614.667,655.798,2625.214C612.818,2635.762,526.856,2648.525,483.875,2654.906L440.894,2661.287"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P4_Q_55" d="M922.275,2598L922.275,2602.167C922.275,2606.333,922.275,2614.667,842.05,2626.104C761.824,2637.542,601.373,2652.084,521.147,2659.355L440.921,2666.626"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_56" d="M348.527,2702L348.527,2706.167C348.527,2710.333,348.527,2718.667,348.527,2726.333C348.527,2734,348.527,2741,348.527,2744.5L348.527,2748"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_R1_57" d="M348.527,2806L348.527,2810.167C348.527,2814.333,348.527,2822.667,348.527,2830.333C348.527,2838,348.527,2845,348.527,2848.5L348.527,2852"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R1_R2_58" d="M348.527,2934L348.527,2938.167C348.527,2942.333,348.527,2950.667,348.527,2958.333C348.527,2966,348.527,2973,348.527,2976.5L348.527,2980"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R2_S_59" d="M348.527,3038L348.527,3042.167C348.527,3046.333,348.527,3054.667,348.527,3062.333C348.527,3070,348.527,3077,348.527,3080.5L348.527,3084"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_T1_60" d="M119.488,3038L119.488,3042.167C119.488,3046.333,119.488,3054.667,119.488,3062.333C119.488,3070,119.488,3077,119.488,3080.5L119.488,3084"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T1_T2_61" d="M119.488,3142L119.488,3146.167C119.488,3150.333,119.488,3158.667,128.058,3166.724C136.627,3174.782,153.765,3182.564,162.334,3186.455L170.904,3190.346"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T2_T3_62" d="M234.008,3246L234.008,3250.167C234.008,3254.333,234.008,3262.667,234.008,3270.333C234.008,3278,234.008,3285,234.008,3288.5L234.008,3292"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T3_T4_63" d="M234.008,3374L234.008,3378.167C234.008,3382.333,234.008,3390.667,234.008,3398.333C234.008,3406,234.008,3413,234.008,3416.5L234.008,3420"></path><path marker-end="url(#mermaid-a75cd3b2-dd5a-401e-8fe3-6a034586e789_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T2_64" d="M348.527,3142L348.527,3146.167C348.527,3150.333,348.527,3158.667,339.958,3166.724C331.389,3174.782,314.25,3182.564,305.681,3186.455L297.112,3190.346"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(248.66796875, 299)" class="edgeLabel"><g transform="translate(-31.51953125, -12)" class="label"><foreignObject height="24" width="63.0390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>.txt/.md</p></span></div></foreignObject></g></g><g transform="translate(499.23046875, 299)" class="edgeLabel"><g transform="translate(-79.46484375, -12)" class="label"><foreignObject height="24" width="158.9296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>.doc/.docx/.ppt/.xlsx</p></span></div></foreignObject></g></g><g transform="translate(749.55078125, 427)" class="edgeLabel"><g transform="translate(-53.32421875, -12)" class="label"><foreignObject height="24" width="106.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>.jpg/.png/.pdf</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(201.361328125, 2483)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>文本查询</p></span></div></foreignObject></g></g><g transform="translate(359.595703125, 2483)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>复杂查询</p></span></div></foreignObject></g></g><g transform="translate(607.150390625, 2483)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>混合查询</p></span></div></foreignObject></g></g><g transform="translate(862.52734375, 2483)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>地理查询</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(624.390625, 35)" id="flowchart-A-2628" class="node default"><rect height="54" width="144.8203125" y="-27" x="-72.41015625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-42.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 文档输入</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 187)" id="flowchart-B-2629" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>文件格式检测</p></span></div></foreignObject></g></g><g transform="translate(248.66796875, 363)" id="flowchart-C-2631" class="node default"><rect height="54" width="156.015625" y="-27" x="-78.0078125" style="" class="basic label-container"></rect><g transform="translate(-48.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 TXT处理器</p></span></div></foreignObject></g></g><g transform="translate(499.23046875, 363)" id="flowchart-D-2633" class="node default"><rect height="54" width="172.640625" y="-27" x="-86.3203125" style="" class="basic label-container"></rect><g transform="translate(-56.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Office处理器</p></span></div></foreignObject></g></g><g transform="translate(749.55078125, 595)" id="flowchart-E-2635" class="node default"><rect height="54" width="196.34375" y="-27" x="-98.171875" style="" class="basic label-container"></rect><g transform="translate(-68.171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="136.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📑 PDF/图像处理器</p></span></div></foreignObject></g></g><g transform="translate(248.66796875, 491)" id="flowchart-C1-2637" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>编码检测算法</p></span></div></foreignObject></g></g><g transform="translate(248.66796875, 595)" id="flowchart-C2-2639" class="node default"><rect height="54" width="196.828125" y="-27" x="-98.4140625" style="" class="basic label-container"></rect><g transform="translate(-68.4140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="136.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReportLab PDF转换</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 699)" id="flowchart-F-2641" class="node default"><rect height="54" width="173.9765625" y="-27" x="-86.98828125" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-56.98828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="113.9765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MinerU解析引擎</p></span></div></foreignObject></g></g><g transform="translate(499.23046875, 491)" id="flowchart-D1-2643" class="node default"><rect height="54" width="220.34375" y="-27" x="-110.171875" style="" class="basic label-container"></rect><g transform="translate(-80.171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LibreOffice可用性检测</p></span></div></foreignObject></g></g><g transform="translate(499.23046875, 595)" id="flowchart-D2-2645" class="node default"><rect height="54" width="204.296875" y="-27" x="-102.1484375" style="" class="basic label-container"></rect><g transform="translate(-72.1484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LibreOffice PDF转换</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 815)" id="flowchart-F1-2651" class="node default"><rect height="78" width="184.703125" y="-39" x="-92.3515625" style="" class="basic label-container"></rect><g transform="translate(-62.3515625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="124.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 布局分析模型<br>CNN+Transformer</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 943)" id="flowchart-F2-2653" class="node default"><rect height="78" width="176.8203125" y="-39" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 目标检测算法<br>YOLO/R-CNN</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 1059)" id="flowchart-F3-2655" class="node default"><rect height="54" width="160.8203125" y="-27" x="-80.41015625" style="" class="basic label-container"></rect><g transform="translate(-50.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 内容分类器</p></span></div></foreignObject></g></g><g transform="translate(105.0078125, 1175)" id="flowchart-G1-2657" class="node default"><rect height="78" width="194.015625" y="-39" x="-97.0078125" style="" class="basic label-container"></rect><g transform="translate(-67.0078125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="134.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 OCR文字识别<br>CRNN+Transformer</p></span></div></foreignObject></g></g><g transform="translate(340.42578125, 1175)" id="flowchart-G2-2659" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🖼️ 图像提取算法</p></span></div></foreignObject></g></g><g transform="translate(585.03125, 1175)" id="flowchart-G3-2661" class="node default"><rect height="78" width="212.390625" y="-39" x="-106.1953125" style="" class="basic label-container"></rect><g transform="translate(-76.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="152.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 表格识别模型<br>结构分析+单元格分割</p></span></div></foreignObject></g></g><g transform="translate(844.140625, 1175)" id="flowchart-G4-2663" class="node default"><rect height="78" width="205.828125" y="-39" x="-102.9140625" style="" class="basic label-container"></rect><g transform="translate(-72.9140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="145.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🧮 公式识别模型<br>符号识别+LaTeX转换</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 1291)" id="flowchart-H-2665" class="node default"><rect height="54" width="198.71875" y="-27" x="-99.359375" style="" class="basic label-container"></rect><g transform="translate(-69.359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="138.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 content_list生成</p></span></div></foreignObject></g></g><g transform="translate(624.390625, 1395)" id="flowchart-I-2673" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔄 内容分离算法</p></span></div></foreignObject></g></g><g transform="translate(349.626953125, 1499)" id="flowchart-I1-2675" class="node default"><rect height="54" width="160.8203125" y="-27" x="-80.41015625" style="" class="basic label-container"></rect><g transform="translate(-50.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 纯文本内容</p></span></div></foreignObject></g></g><g transform="translate(609.123046875, 1499)" id="flowchart-I2-2677" class="node default"><rect height="54" width="160.8203125" y="-27" x="-80.41015625" style="" class="basic label-container"></rect><g transform="translate(-50.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎨 多模态内容</p></span></div></foreignObject></g></g><g transform="translate(861.763671875, 1499)" id="flowchart-I3-2679" class="node default"><rect height="54" width="144.8203125" y="-27" x="-72.41015625" style="" class="basic label-container"></rect><g transform="translate(-42.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗺️ 地理信息</p></span></div></foreignObject></g></g><g transform="translate(349.626953125, 1603)" id="flowchart-J-2681" class="node default"><rect height="54" width="242.171875" y="-27" x="-121.0859375" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-91.0859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="182.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🧠 LightRAG知识图谱构建</p></span></div></foreignObject></g></g><g transform="translate(349.626953125, 1719)" id="flowchart-J1-2683" class="node default"><rect height="78" width="172.9140625" y="-39" x="-86.45703125" style="" class="basic label-container"></rect><g transform="translate(-56.45703125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="112.9140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🏷️ NER实体识别<br>BERT序列标注</p></span></div></foreignObject></g></g><g transform="translate(349.626953125, 1847)" id="flowchart-J2-2685" class="node default"><rect height="78" width="194.3203125" y="-39" x="-97.16015625" style="" class="basic label-container"></rect><g transform="translate(-67.16015625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="134.3203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔗 关系抽取算法<br>Transformer分类器</p></span></div></foreignObject></g></g><g transform="translate(349.626953125, 1975)" id="flowchart-J3-2687" class="node default"><rect height="78" width="175.9921875" y="-39" x="-87.99609375" style="" class="basic label-container"></rect><g transform="translate(-57.99609375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="115.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 图嵌入算法<br>TransE/ComplEx</p></span></div></foreignObject></g></g><g transform="translate(609.123046875, 1603)" id="flowchart-K-2689" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎨 多模态处理器</p></span></div></foreignObject></g></g><g transform="translate(609.123046875, 1719)" id="flowchart-K1-2691" class="node default"><rect height="78" width="192.890625" y="-39" x="-96.4453125" style="" class="basic label-container"></rect><g transform="translate(-66.4453125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="132.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>👁️ 视觉理解模型<br>Vision Transformer</p></span></div></foreignObject></g></g><g transform="translate(609.123046875, 1847)" id="flowchart-K2-2693" class="node default"><rect height="78" width="176.8203125" y="-39" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 图像描述生成<br>VL-BERT/CLIP</p></span></div></foreignObject></g></g><g transform="translate(609.123046875, 1975)" id="flowchart-K3-2695" class="node default"><rect height="54" width="192.8203125" y="-27" x="-96.41015625" style="" class="basic label-container"></rect><g transform="translate(-66.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="132.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔍 上下文提取算法</p></span></div></foreignObject></g></g><g transform="translate(861.763671875, 1603)" id="flowchart-L-2697" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗺️ 空间信息处理</p></span></div></foreignObject></g></g><g transform="translate(861.763671875, 1719)" id="flowchart-L1-2699" class="node default"><rect height="78" width="212.390625" y="-39" x="-106.1953125" style="" class="basic label-container"></rect><g transform="translate(-76.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="152.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 地理查询路由<br>关键词匹配+语义分析</p></span></div></foreignObject></g></g><g transform="translate(861.763671875, 1847)" id="flowchart-L2-2701" class="node default"><rect height="78" width="188.546875" y="-39" x="-94.2734375" style="" class="basic label-container"></rect><g transform="translate(-64.2734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌍 高德地图API<br>POI搜索/路线规划</p></span></div></foreignObject></g></g><g transform="translate(861.763671875, 1975)" id="flowchart-L3-2703" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📍 地理编码算法</p></span></div></foreignObject></g></g><g transform="translate(540.544921875, 2091)" id="flowchart-M-2705" class="node default"><rect height="54" width="160.8203125" y="-27" x="-80.41015625" style="" class="basic label-container"></rect><g transform="translate(-50.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 向量化存储</p></span></div></foreignObject></g></g><g transform="translate(540.544921875, 2207)" id="flowchart-M1-2711" class="node default"><rect height="78" width="225.6953125" y="-39" x="-112.84765625" style="" class="basic label-container"></rect><g transform="translate(-82.84765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="165.6953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔤 文本嵌入模型<br>BERT/RoBERTa Encoder</p></span></div></foreignObject></g></g><g transform="translate(540.544921875, 2371)" id="flowchart-M2-2713" class="node default"><rect height="78" width="160.8203125" y="-39" x="-80.41015625" style="" class="basic label-container"></rect><g transform="translate(-50.41015625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗄️ 向量数据库<br>FAISS索引</p></span></div></foreignObject></g></g><g transform="translate(305.287109375, 2207)" id="flowchart-N-2714" class="node default"><rect height="54" width="144.8203125" y="-27" x="-72.41015625" style="" class="basic label-container"></rect><g transform="translate(-42.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>❓ 用户查询</p></span></div></foreignObject></g></g><g transform="translate(305.287109375, 2371)" id="flowchart-O-2715" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询类型识别</p></span></div></foreignObject></g></g><g transform="translate(201.361328125, 2559)" id="flowchart-P1-2717" class="node default"><rect height="78" width="150.5546875" y="-39" x="-75.27734375" style="" class="basic label-container"></rect><g transform="translate(-45.27734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="90.5546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔍 Local检索<br>余弦相似度</p></span></div></foreignObject></g></g><g transform="translate(437.521484375, 2559)" id="flowchart-P2-2719" class="node default"><rect height="78" width="165.1484375" y="-39" x="-82.57421875" style="" class="basic label-container"></rect><g transform="translate(-52.57421875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="105.1484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 Global检索<br>图遍历算法BFS</p></span></div></foreignObject></g></g><g transform="translate(698.779296875, 2559)" id="flowchart-P3-2721" class="node default"><rect height="78" width="159.7890625" y="-39" x="-79.89453125" style="" class="basic label-container"></rect><g transform="translate(-49.89453125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="99.7890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔄 Hybrid检索<br>加权融合算法</p></span></div></foreignObject></g></g><g transform="translate(922.275390625, 2559)" id="flowchart-P4-2723" class="node default"><rect height="78" width="187.203125" y="-39" x="-93.6015625" style="" class="basic label-container"></rect><g transform="translate(-63.6015625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="127.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗺️ 空间检索<br>地理路由+API调用</p></span></div></foreignObject></g></g><g transform="translate(348.52734375, 2675)" id="flowchart-Q-2733" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔗 结果融合算法</p></span></div></foreignObject></g></g><g transform="translate(348.52734375, 2779)" id="flowchart-R-2741" class="node default"><rect height="54" width="172.375" y="-27" x="-86.1875" style="" class="basic label-container"></rect><g transform="translate(-56.1875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🧠 LLM答案生成</p></span></div></foreignObject></g></g><g transform="translate(348.52734375, 2895)" id="flowchart-R1-2743" class="node default"><rect height="78" width="210.046875" y="-39" x="-105.0234375" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-75.0234375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="150.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 通义千问模型<br>Transformer Decoder</p></span></div></foreignObject></g></g><g transform="translate(348.52734375, 3011)" id="flowchart-R2-2745" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 提示工程优化</p></span></div></foreignObject></g></g><g transform="translate(348.52734375, 3115)" id="flowchart-S-2747" class="node default"><rect height="54" width="176.8203125" y="-27" x="-88.41015625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-58.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>✅ 最终答案输出</p></span></div></foreignObject></g></g><g transform="translate(119.48828125, 3011)" id="flowchart-T-2748" class="node default"><rect height="54" width="143.359375" y="-27" x="-71.6796875" style="" class="basic label-container"></rect><g transform="translate(-41.6796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 Web界面</p></span></div></foreignObject></g></g><g transform="translate(119.48828125, 3115)" id="flowchart-T1-2749" class="node default"><rect height="54" width="181.2578125" y="-27" x="-90.62890625" style="" class="basic label-container"></rect><g transform="translate(-60.62890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="121.2578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚡ FastAPI服务器</p></span></div></foreignObject></g></g><g transform="translate(234.0078125, 3219)" id="flowchart-T2-2751" class="node default"><rect height="54" width="222.703125" y="-27" x="-111.3515625" style="" class="basic label-container"></rect><g transform="translate(-81.3515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="162.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔌 WebSocket实时通信</p></span></div></foreignObject></g></g><g transform="translate(234.0078125, 3335)" id="flowchart-T3-2753" class="node default"><rect height="78" width="192.0546875" y="-39" x="-96.02734375" style="" class="basic label-container"></rect><g transform="translate(-66.02734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="132.0546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎤 语音交互<br>Whisper+Edge-TTS</p></span></div></foreignObject></g></g><g transform="translate(234.0078125, 3451)" id="flowchart-T4-2755" class="node default"><rect height="54" width="179.0234375" y="-27" x="-89.51171875" style="" class="basic label-container"></rect><g transform="translate(-59.51171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="119.0234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>👤 3D数字人渲染</p></span></div></foreignObject></g></g></g></g></g></svg>