#!/usr/bin/env python3
"""
服务器临时目录修复脚本
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path


def fix_temp_directory():
    """修复临时目录问题"""
    print("🛠️ 修复服务器临时目录...")
    
    try:
        # 创建项目专用临时目录
        project_temp_dir = "/data/RAG-Anything/temp"
        os.makedirs(project_temp_dir, exist_ok=True)
        os.chmod(project_temp_dir, 0o755)
        
        # 设置环境变量
        env_vars = {
            'TMPDIR': project_temp_dir,
            'TMP': project_temp_dir, 
            'TEMP': project_temp_dir,
            'TEMPDIR': project_temp_dir
        }
        
        for var, value in env_vars.items():
            os.environ[var] = value
            print(f"✅ 设置环境变量: {var}={value}")
        
        # 验证
        test_file = tempfile.NamedTemporaryFile(delete=True)
        test_file.close()
        print("✅ Python tempfile模块工作正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 临时目录修复失败: {e}")
        return False


def check_mineru():
    """检查MinerU状态"""
    print("\n🔍 检查MinerU状态...")
    
    try:
        result = subprocess.run(['mineru', '--version'], 
                              capture_output=True, text=True)
        print(f"✅ MinerU版本: {result.stdout.strip()}")
        return True
    except Exception as e:
        print(f"❌ MinerU检查失败: {e}")
        return False


def test_pdf_parsing():
    """测试PDF解析"""
    print("\n🧪 测试PDF解析...")
    
    pdf_file = "/data/RAG-Anything/sample_questions.pdf"
    if not os.path.exists(pdf_file):
        print(f"❌ PDF文件不存在: {pdf_file}")
        return False
    
    print(f"✅ PDF文件存在: {pdf_file}")
    print(f"文件大小: {os.path.getsize(pdf_file)} bytes")
    
    # 测试MinerU命令
    try:
        cmd = [
            'mineru', '-p', pdf_file, 
            '-o', '/data/RAG-Anything/test_output',
            '-m', 'auto', '-b', 'pipeline',
            '--source', 'huggingface', '-d', 'cuda'
        ]
        
        print(f"测试命令: {' '.join(cmd)}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['TMPDIR'] = '/data/RAG-Anything/temp'
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              timeout=60, env=env)
        
        if result.returncode == 0:
            print("✅ MinerU解析测试成功")
            return True
        else:
            print(f"❌ MinerU解析失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ MinerU解析超时")
        return False
    except Exception as e:
        print(f"❌ MinerU解析测试失败: {e}")
        return False


def main():
    """主函数"""
    print("RAGAnything 服务器环境修复工具")
    print("=" * 40)
    
    # 修复临时目录
    if not fix_temp_directory():
        print("❌ 临时目录修复失败")
        sys.exit(1)
    
    # 检查MinerU
    if not check_mineru():
        print("❌ MinerU检查失败")
        sys.exit(1)
    
    # 测试PDF解析
    if not test_pdf_parsing():
        print("❌ PDF解析测试失败")
        sys.exit(1)
    
    print("\n✅ 所有检查通过！环境已修复")


if __name__ == "__main__":
    main()
