#!/usr/bin/env python
"""
语音RAG测试系统 - 简化版本
解决Edge-TTS连接问题，使用系统TTS作为备用方案
"""

import asyncio
import os
import sys
import tempfile
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from raganything import RAGAnything, RAGAnythingConfig
from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc

# 检查语音依赖
try:
    import edge_tts
    import pygame
    EDGE_TTS_AVAILABLE = True
    logger.info("✅ Edge-TTS可用")
except ImportError:
    EDGE_TTS_AVAILABLE = False
    logger.warning("❌ Edge-TTS不可用")

# 检查系统TTS
SYSTEM_TTS_AVAILABLE = False
try:
    import platform
    import subprocess
    if platform.system() == "Darwin":  # macOS
        SYSTEM_TTS_AVAILABLE = True
        logger.info("✅ macOS系统TTS可用")
except ImportError:
    logger.warning("❌ 系统TTS不可用")


class SimpleVoiceRAG:
    """简化的语音RAG系统"""
    
    def __init__(self):
        self.api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.rag_system = None
        
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY或OPENAI_API_KEY环境变量")
        
        logger.info(f"🔑 API密钥: {self.api_key[:8]}...{self.api_key[-4:]}")
    
    async def initialize_rag(self):
        """初始化RAG系统"""
        logger.info("🔧 正在初始化RAG系统...")
        
        # 检查现有知识库
        working_dir = "./rag_storage_server"
        if not os.path.exists(working_dir):
            logger.error(f"❌ 知识库目录不存在: {working_dir}")
            return False
        
        try:
            # 创建LightRAG实例
            from lightrag import LightRAG
            from lightrag.kg.shared_storage import initialize_pipeline_status
            
            def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
                return openai_complete_if_cache(
                    "qwen-turbo",
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    api_key=self.api_key,
                    base_url=self.base_url,
                    **kwargs,
                )
            
            embedding_func = EmbeddingFunc(
                embedding_dim=1536,
                max_token_size=8192,
                func=lambda texts: openai_embed(
                    texts,
                    model="text-embedding-v1",
                    api_key=self.api_key,
                    base_url=self.base_url,
                ),
            )
            
            # 加载现有知识库
            lightrag_instance = LightRAG(
                working_dir=working_dir,
                llm_model_func=llm_model_func,
                embedding_func=embedding_func,
            )
            
            await lightrag_instance.initialize_storages()
            await initialize_pipeline_status()
            
            self.rag_system = RAGAnything(lightrag=lightrag_instance)
            
            logger.info("✅ RAG系统初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ RAG系统初始化失败: {e}")
            return False
    
    def clean_text_for_speech(self, text):
        """清理文本用于语音播放"""
        import re
        
        # 处理编码问题
        try:
            text = text.encode('utf-8', errors='ignore').decode('utf-8')
        except:
            pass
        
        # 移除代理对字符
        text = re.sub(r'[\ud800-\udfff]', '', text)
        
        # 移除星号和特殊标记
        text = re.sub(r'\*+', '', text)
        text = re.sub(r'[#\[\]]+', '', text)
        text = re.sub(r'References?:', '', text)
        text = re.sub(r'\[KG\].*?\.pdf', '', text)
        text = re.sub(r'\[DC\].*?\.pdf', '', text)
        
        # 清理空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 限制长度
        if len(text) > 500:
            sentences = text.split('。')
            result = ""
            for sentence in sentences:
                if len(result + sentence + '。') <= 500:
                    result += sentence + '。'
                else:
                    break
            text = result.rstrip('。') + '。'
        
        return text
    
    async def system_tts_speak(self, text):
        """使用系统TTS播放"""
        try:
            import platform
            if platform.system() == "Darwin":  # macOS
                import subprocess
                # 使用macOS的say命令
                process = subprocess.Popen(
                    ["say", text],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                await asyncio.get_event_loop().run_in_executor(None, process.wait)
                return True
        except Exception as e:
            logger.error(f"❌ 系统TTS失败: {e}")
            return False
        
        return False
    
    async def text_to_speech(self, text):
        """文本转语音"""
        cleaned_text = self.clean_text_for_speech(text)
        
        if not cleaned_text.strip():
            logger.warning("⚠️ 清理后文本为空")
            return False
        
        logger.info(f"🔊 准备播放语音，文本长度: {len(cleaned_text)} 字符")
        
        # 尝试Edge-TTS
        if EDGE_TTS_AVAILABLE:
            try:
                logger.info("🎵 尝试Edge-TTS...")
                communicate = edge_tts.Communicate(cleaned_text, "zh-CN-XiaoxiaoNeural")
                
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    audio_file = temp_file.name
                
                await asyncio.wait_for(communicate.save(audio_file), timeout=30.0)
                
                if os.path.exists(audio_file) and os.path.getsize(audio_file) > 0:
                    # 播放音频
                    pygame.mixer.init()
                    pygame.mixer.music.load(audio_file)
                    pygame.mixer.music.play()
                    
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)
                    
                    os.unlink(audio_file)
                    logger.info("✅ Edge-TTS播放成功")
                    return True
                    
            except Exception as e:
                logger.warning(f"⚠️ Edge-TTS失败: {e}")
        
        # 尝试系统TTS
        if SYSTEM_TTS_AVAILABLE:
            logger.info("🎵 尝试系统TTS...")
            if await self.system_tts_speak(cleaned_text):
                logger.info("✅ 系统TTS播放成功")
                return True
        
        # 都失败了，只显示文本
        logger.warning("⚠️ 所有TTS方案都失败，仅显示文本")
        print(f"🤖 回答: {cleaned_text}")
        return False
    
    async def query_and_speak(self, question):
        """查询并播放语音回答"""
        try:
            logger.info(f"❓ 查询问题: {question}")
            
            # 查询知识库
            response = await self.rag_system.aquery(question, mode="hybrid")
            
            print(f"\n📝 文字回答:\n{response}")
            
            # 语音播放
            await self.text_to_speech(response)
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 查询失败: {e}")
            return None


async def main():
    """主函数"""
    print("🚀 语音RAG测试系统")
    print("=" * 50)
    
    try:
        # 初始化系统
        voice_rag = SimpleVoiceRAG()
        
        if not await voice_rag.initialize_rag():
            print("❌ 系统初始化失败")
            return
        
        # 测试查询
        test_questions = [
            "北京有哪些著名的景点？",
            "北京的美食有什么特色？",
            "北京的交通系统如何？"
        ]
        
        for question in test_questions:
            print(f"\n🔄 测试问题: {question}")
            print("-" * 30)
            
            response = await voice_rag.query_and_speak(question)
            
            if response:
                print("✅ 查询成功")
            else:
                print("❌ 查询失败")
            
            print("-" * 30)
            await asyncio.sleep(2)  # 间隔2秒
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
