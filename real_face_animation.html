<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实人脸动画 - AI驱动的面部动画</title>
    
    <!-- MediaPipe CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/face_mesh.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            min-height: 700px;
        }

        .avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .face-container {
            width: 100%;
            height: 500px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            border: 3px solid #007AFF;
            position: relative;
            overflow: hidden;
            background: #000;
        }

        .face-canvas {
            width: 100%;
            height: 100%;
            border-radius: 17px;
            position: absolute;
            top: 0;
            left: 0;
        }

        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 15px;
            background: #007AFF;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: #34C759;
        }

        .status {
            margin-top: 15px;
            padding: 12px 20px;
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(52, 199, 89, 0.3);
        }

        .chat-section {
            display: flex;
            flex-direction: column;
            height: 640px;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .chat-header h1 {
            color: #1d1d1f;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 20px;
            margin-bottom: 20px;
            border: 2px solid #e5e5ea;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 20px;
            max-width: 85%;
            word-wrap: break-word;
            animation: messageSlideIn 0.4s ease-out;
        }

        .message.user {
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 8px;
        }

        .message.assistant {
            background: linear-gradient(45deg, #E5E5EA, #F2F2F7);
            color: #1d1d1f;
            border-bottom-left-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .input-section {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: 2px solid #e5e5ea;
        }

        .text-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007AFF, #5856D6);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        .btn-voice {
            background: linear-gradient(45deg, #34C759, #30D158);
            color: white;
            min-width: 60px;
        }

        .btn-voice:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 199, 89, 0.3);
        }

        .btn-voice.recording {
            background: linear-gradient(45deg, #FF3B30, #FF6B35);
            animation: pulse 1.5s infinite;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 15px;
            color: #007AFF;
            font-weight: 600;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 15px;
            margin: 10px 0;
        }

        .loading.show {
            display: block;
            animation: loadingPulse 1.5s infinite;
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes loadingPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .debug-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 真实人脸动画区域 -->
        <div class="avatar-section">
            <div class="face-container">
                <canvas id="faceCanvas" class="face-canvas"></canvas>
            </div>
            
            <div class="controls">
                <button class="control-btn" id="initBtn">
                    🚀 初始化AI
                </button>
                <button class="control-btn" id="blinkBtn">
                    👁️ 眨眼动画
                </button>
                <button class="control-btn" id="speakBtn">
                    💋 说话动画
                </button>
                <button class="control-btn" id="resetBtn">
                    🔄 重置
                </button>
            </div>
            
            <div class="status" id="status">🤖 准备初始化AI面部检测</div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>🎭 AI真实人脸动画</h1>
                <p>基于MediaPipe的精准面部关键点检测</p>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>AI助手:</strong><br>
                        您好！我是基于AI面部关键点检测的真实人脸动画系统。<br><br>
                        🎭 <strong>技术特点：</strong><br>
                        • 使用MediaPipe精准检测468个面部关键点<br>
                        • 实时追踪真实人脸的眼睛和嘴巴位置<br>
                        • 基于关键点驱动真实面部动画<br>
                        • 支持眨眼、说话、表情变化<br><br>
                        请点击"🚀 初始化AI"开始面部检测！
                    </div>
                </div>
            </div>

            <div class="loading" id="loadingIndicator">
                <span>🧠 AI正在处理中...</span>
            </div>

            <div class="input-section">
                <input
                    type="text"
                    id="textInput"
                    class="text-input"
                    placeholder="输入您的问题..."
                >
                <button id="voiceBtn" class="btn btn-voice" title="语音输入">
                    🎤
                </button>
                <button id="sendBtn" class="btn">发送</button>
            </div>
        </div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info" id="debugInfo">
        <div>状态: <span id="debugStatus">未初始化</span></div>
        <div>关键点: <span id="debugLandmarks">0</span></div>
        <div>眼睛: <span id="debugEyes">未检测</span></div>
        <div>嘴巴: <span id="debugMouth">未检测</span></div>
    </div>

    <script src="real_face_animation.js"></script>
</body>
</html>
