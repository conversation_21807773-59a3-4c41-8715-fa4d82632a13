# 🗺️ 高德地图API申请与配置指南

## 📋 申请步骤

### 1. 注册高德开发者账号
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 点击右上角"注册"按钮
3. 填写个人或企业信息完成注册
4. 进行实名认证（个人用户需要身份证，企业用户需要营业执照）

### 2. 创建应用
1. 登录后进入 [控制台](https://console.amap.com/)
2. 点击"应用管理" -> "我的应用"
3. 点击"创建新应用"
4. 填写应用信息：
   - 应用名称：RAG-Anything地图集成
   - 应用类型：Web服务
   - 应用描述：智能问答系统地图功能集成

### 3. 添加Key
1. 在应用详情页点击"添加Key"
2. 填写Key信息：
   - Key名称：RAG-Anything-WebAPI
   - 服务平台：Web服务
   - 绑定域名：localhost:8000（开发环境）
3. 点击"提交"创建Key

### 4. 获取API Key
创建成功后，您将获得一个API Key，格式类似：
```
1234567890abcdef1234567890abcdef
```

## 🔧 配置方法

### 方法1：环境变量配置（推荐）
```bash
# 在终端中设置环境变量
export AMAP_API_KEY="your_amap_api_key_here"

# 或者在 .env 文件中配置
echo "AMAP_API_KEY=your_amap_api_key_here" >> .env
```

### 方法2：直接在代码中配置
```python
# 在 amap_api.py 中直接设置
amap = AmapAPI(api_key="your_amap_api_key_here")
```

## 📊 API配额说明

### 免费配额
- **Web服务API**：每日30万次调用
- **JavaScript API**：每日10万次调用
- **路线规划**：每日5000次调用
- **地理编码**：每日5000次调用

### 收费标准
超出免费配额后按调用次数收费：
- Web服务API：0.002元/次
- 路线规划：0.005元/次
- 地理编码：0.002元/次

## 🛠️ 支持的功能

### 已集成功能
- ✅ **地理编码**：地址转坐标
- ✅ **逆地理编码**：坐标转地址
- ✅ **路线规划**：驾车路线规划
- ✅ **POI搜索**：兴趣点搜索
- ✅ **天气查询**：实时天气信息

### 可扩展功能
- 🔄 **公交规划**：公共交通路线
- 🔄 **步行规划**：步行路线规划
- 🔄 **骑行规划**：骑行路线规划
- 🔄 **实时路况**：交通状况查询
- 🔄 **地理围栏**：区域监控功能

## 🎯 使用示例

### 基础查询示例
```python
# 地理编码
location = await amap.geocode("北京市朝阳区望京SOHO")

# 路线规划
route = await amap.route_planning(
    origin="北京市朝阳区望京SOHO",
    destination="北京市海淀区中关村"
)

# POI搜索
pois = await amap.poi_search("美食", city="北京")

# 天气查询
weather = await amap.get_weather("北京")
```

### 智能问答示例
用户可以直接问：
- "从北京到上海怎么走？"
- "附近有什么好吃的？"
- "北京今天天气怎么样？"
- "天安门在哪里？"

## ⚠️ 注意事项

### 安全建议
1. **不要在前端代码中暴露API Key**
2. **设置域名白名单限制访问**
3. **定期更换API Key**
4. **监控API调用量避免超额**

### 开发建议
1. **使用异步调用提高性能**
2. **添加错误处理和重试机制**
3. **缓存常用查询结果**
4. **合理设置请求超时时间**

### 生产环境配置
```bash
# 生产环境域名配置
# 在高德控制台中添加您的实际域名
# 例如：yourdomain.com
```

## 🔗 相关链接

- [高德开放平台](https://lbs.amap.com/)
- [Web服务API文档](https://lbs.amap.com/api/webservice/summary)
- [JavaScript API文档](https://lbs.amap.com/api/javascript-api/summary)
- [开发者控制台](https://console.amap.com/)
- [API调用统计](https://console.amap.com/dev/flow/app)

## 🆘 常见问题

### Q: API Key无效怎么办？
A: 检查Key是否正确复制，域名是否正确配置，服务是否已启用。

### Q: 超出配额限制怎么办？
A: 可以升级到付费版本，或者优化调用频率，添加缓存机制。

### Q: 定位不准确怎么办？
A: 检查网络连接，确保浏览器允许位置访问权限。

### Q: 路线规划失败怎么办？
A: 检查起点终点地址是否正确，是否在服务范围内。

---

配置完成后，重启服务器即可使用高德地图功能！🚀
